{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport Swal from 'sweetalert2';\nimport Toast from 'vue-toastification';\nimport 'vue-toastification/dist/index.css';\nimport apiService from './services/api';\n\n// Aggressive cache busting\nconst cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);\n\n// Clear only non-authentication storage before app initialization\nif (typeof Storage !== 'undefined') {\n  // Preserve authentication tokens\n  const ndytToken = localStorage.getItem('ndyt_token');\n  const ndytUser = localStorage.getItem('ndyt_user');\n  const ndytTeamPin = localStorage.getItem('ndyt_team_pin');\n\n  // Clear all storage\n  localStorage.clear();\n  sessionStorage.clear();\n\n  // Restore authentication data\n  if (ndytToken) localStorage.setItem('ndyt_token', ndytToken);\n  if (ndytUser) localStorage.setItem('ndyt_user', ndytUser);\n  if (ndytTeamPin) localStorage.setItem('ndyt_team_pin', ndytTeamPin);\n}\n\n// Add cache-busting meta tags\nconst addCacheBustingMeta = () => {\n  const metaTags = [{\n    name: 'cache-control',\n    content: 'no-cache, no-store, must-revalidate, max-age=0'\n  }, {\n    name: 'pragma',\n    content: 'no-cache'\n  }, {\n    name: 'expires',\n    content: '0'\n  }, {\n    name: 'last-modified',\n    content: new Date().toUTCString()\n  }, {\n    name: 'etag',\n    content: cacheBuster\n  }, {\n    name: 'cache-buster',\n    content: cacheBuster\n  }];\n  metaTags.forEach(tag => {\n    const meta = document.createElement('meta');\n    meta.setAttribute('http-equiv', tag.name);\n    meta.setAttribute('content', tag.content);\n    document.head.appendChild(meta);\n  });\n};\n\n// Force reload on browser back/forward\nwindow.addEventListener('pageshow', event => {\n  if (event.persisted || window.performance.navigation.type === 2) {\n    window.location.reload(true);\n  }\n});\n\n// Prevent caching on unload but preserve auth tokens\nwindow.addEventListener('beforeunload', () => {\n  if (typeof Storage !== 'undefined') {\n    // Preserve authentication tokens\n    const ndytToken = localStorage.getItem('ndyt_token');\n    const ndytUser = localStorage.getItem('ndyt_user');\n    const ndytTeamPin = localStorage.getItem('ndyt_team_pin');\n\n    // Clear all storage\n    localStorage.clear();\n    sessionStorage.clear();\n\n    // Restore authentication data\n    if (ndytToken) localStorage.setItem('ndyt_token', ndytToken);\n    if (ndytUser) localStorage.setItem('ndyt_user', ndytUser);\n    if (ndytTeamPin) localStorage.setItem('ndyt_team_pin', ndytTeamPin);\n  }\n});\nconst app = createApp(App);\n\n// Configure SweetAlert2 with app colors\nconst swalConfig = {\n  background: '#1e1e2e',\n  color: '#f1f5f9',\n  confirmButtonColor: '#4f46e5',\n  cancelButtonColor: '#e53e3e',\n  customClass: {\n    popup: 'swal-popup',\n    confirmButton: 'swal-confirm-btn',\n    cancelButton: 'swal-cancel-btn'\n  }\n};\n\n// Configure Toast with app colors\nconst toastOptions = {\n  position: 'top-right',\n  timeout: 4000,\n  closeOnClick: true,\n  pauseOnFocusLoss: true,\n  pauseOnHover: true,\n  draggable: true,\n  draggablePercent: 0.6,\n  showCloseButtonOnHover: false,\n  hideProgressBar: false,\n  closeButton: 'button',\n  icon: true,\n  rtl: true,\n  toastDefaults: {\n    success: {\n      toastClassName: 'toast-success',\n      bodyClassName: 'toast-body',\n      hideProgressBar: false,\n      timeout: 3000\n    },\n    error: {\n      toastClassName: 'toast-error',\n      bodyClassName: 'toast-body',\n      hideProgressBar: false,\n      timeout: 5000\n    }\n  }\n};\n\n// Make SweetAlert2 globally available\napp.config.globalProperties.$swal = Swal.mixin(swalConfig);\n\n// Add global cache-busting mixin\napp.mixin({\n  beforeCreate() {\n    // Force component refresh\n    this.$options._cacheBuster = cacheBuster;\n  },\n  mounted() {\n    // Add cache-busting to component\n    if (this.$el && this.$el.setAttribute) {\n      this.$el.setAttribute('data-cache-bust', cacheBuster);\n    }\n  }\n});\napp.use(Toast, toastOptions);\n\n// Fallback shim: ensure this.$toast is always available\nif (!app.config.globalProperties.$toast || typeof app.config.globalProperties.$toast.success !== 'function') {\n  const swalInstance = app.config.globalProperties.$swal || Swal.mixin(swalConfig);\n  app.config.globalProperties.$toast = {\n    success: message => swalInstance.fire({\n      title: 'تم بنجاح',\n      text: message,\n      icon: 'success'\n    }),\n    error: message => swalInstance.fire({\n      title: 'حدث خطأ',\n      text: message,\n      icon: 'error'\n    }),\n    info: message => swalInstance.fire({\n      title: 'إشعار',\n      text: message,\n      icon: 'info'\n    }),\n    warning: message => swalInstance.fire({\n      title: 'تنبيه',\n      text: message,\n      icon: 'warning'\n    })\n  };\n}\napp.use(router);\n\n// Add cache-busting meta tags before mounting\naddCacheBustingMeta();\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "router", "<PERSON><PERSON>", "Toast", "apiService", "cacheBuster", "Date", "now", "Math", "random", "toString", "substr", "Storage", "ndytToken", "localStorage", "getItem", "ndytUser", "ndytTeamPin", "clear", "sessionStorage", "setItem", "addCacheBustingMeta", "metaTags", "name", "content", "toUTCString", "for<PERSON>ach", "tag", "meta", "document", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "window", "addEventListener", "event", "persisted", "performance", "navigation", "type", "location", "reload", "app", "swalConfig", "background", "color", "confirmButtonColor", "cancelButtonColor", "customClass", "popup", "confirmButton", "cancelButton", "toastOptions", "position", "timeout", "closeOnClick", "pauseOnFocusLoss", "pauseOnHover", "draggable", "draggablePercent", "showCloseButtonOnHover", "hideProgressBar", "closeButton", "icon", "rtl", "toastDefaults", "success", "toastClassName", "bodyClassName", "error", "config", "globalProperties", "$swal", "mixin", "beforeCreate", "$options", "_cacheBuster", "mounted", "$el", "use", "$toast", "swalInstance", "message", "fire", "title", "text", "info", "warning", "mount"], "sources": ["F:/My Apps/iqtp/iqtp_backend/ndyt-v2/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport Swal from 'sweetalert2'\nimport Toast from 'vue-toastification'\nimport 'vue-toastification/dist/index.css'\nimport apiService from './services/api'\n\n// Aggressive cache busting\nconst cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);\n\n// Clear only non-authentication storage before app initialization\nif (typeof Storage !== 'undefined') {\n  // Preserve authentication tokens\n  const ndytToken = localStorage.getItem('ndyt_token');\n  const ndytUser = localStorage.getItem('ndyt_user');\n  const ndytTeamPin = localStorage.getItem('ndyt_team_pin');\n  \n  // Clear all storage\n  localStorage.clear();\n  sessionStorage.clear();\n  \n  // Restore authentication data\n  if (ndytToken) localStorage.setItem('ndyt_token', ndytToken);\n  if (ndytUser) localStorage.setItem('ndyt_user', ndytUser);\n  if (ndytTeamPin) localStorage.setItem('ndyt_team_pin', ndytTeamPin);\n}\n\n// Add cache-busting meta tags\nconst addCacheBustingMeta = () => {\n  const metaTags = [\n    { name: 'cache-control', content: 'no-cache, no-store, must-revalidate, max-age=0' },\n    { name: 'pragma', content: 'no-cache' },\n    { name: 'expires', content: '0' },\n    { name: 'last-modified', content: new Date().toUTCString() },\n    { name: 'etag', content: cacheBuster },\n    { name: 'cache-buster', content: cacheBuster }\n  ];\n  \n  metaTags.forEach(tag => {\n    const meta = document.createElement('meta');\n    meta.setAttribute('http-equiv', tag.name);\n    meta.setAttribute('content', tag.content);\n    document.head.appendChild(meta);\n  });\n};\n\n// Force reload on browser back/forward\nwindow.addEventListener('pageshow', (event) => {\n  if (event.persisted || window.performance.navigation.type === 2) {\n    window.location.reload(true);\n  }\n});\n\n// Prevent caching on unload but preserve auth tokens\nwindow.addEventListener('beforeunload', () => {\n  if (typeof Storage !== 'undefined') {\n    // Preserve authentication tokens\n    const ndytToken = localStorage.getItem('ndyt_token');\n    const ndytUser = localStorage.getItem('ndyt_user');\n    const ndytTeamPin = localStorage.getItem('ndyt_team_pin');\n    \n    // Clear all storage\n    localStorage.clear();\n    sessionStorage.clear();\n    \n    // Restore authentication data\n    if (ndytToken) localStorage.setItem('ndyt_token', ndytToken);\n    if (ndytUser) localStorage.setItem('ndyt_user', ndytUser);\n    if (ndytTeamPin) localStorage.setItem('ndyt_team_pin', ndytTeamPin);\n  }\n});\n\nconst app = createApp(App)\n\n// Configure SweetAlert2 with app colors\nconst swalConfig = {\n  background: '#1e1e2e',\n  color: '#f1f5f9',\n  confirmButtonColor: '#4f46e5',\n  cancelButtonColor: '#e53e3e',\n  customClass: {\n    popup: 'swal-popup',\n    confirmButton: 'swal-confirm-btn',\n    cancelButton: 'swal-cancel-btn'\n  }\n};\n\n// Configure Toast with app colors\nconst toastOptions = {\n  position: 'top-right',\n  timeout: 4000,\n  closeOnClick: true,\n  pauseOnFocusLoss: true,\n  pauseOnHover: true,\n  draggable: true,\n  draggablePercent: 0.6,\n  showCloseButtonOnHover: false,\n  hideProgressBar: false,\n  closeButton: 'button',\n  icon: true,\n  rtl: true,\n  toastDefaults: {\n    success: {\n      toastClassName: 'toast-success',\n      bodyClassName: 'toast-body',\n      hideProgressBar: false,\n      timeout: 3000\n    },\n    error: {\n      toastClassName: 'toast-error', \n      bodyClassName: 'toast-body',\n      hideProgressBar: false,\n      timeout: 5000\n    }\n  }\n};\n\n// Make SweetAlert2 globally available\napp.config.globalProperties.$swal = Swal.mixin(swalConfig);\n\n// Add global cache-busting mixin\napp.mixin({\n  beforeCreate() {\n    // Force component refresh\n    this.$options._cacheBuster = cacheBuster;\n  },\n  mounted() {\n    // Add cache-busting to component\n    if (this.$el && this.$el.setAttribute) {\n      this.$el.setAttribute('data-cache-bust', cacheBuster);\n    }\n  }\n});\n\napp.use(Toast, toastOptions)\n\n// Fallback shim: ensure this.$toast is always available\nif (!app.config.globalProperties.$toast || typeof app.config.globalProperties.$toast.success !== 'function') {\n  const swalInstance = app.config.globalProperties.$swal || Swal.mixin(swalConfig);\n  app.config.globalProperties.$toast = {\n    success: (message) => swalInstance.fire({ title: 'تم بنجاح', text: message, icon: 'success' }),\n    error: (message) => swalInstance.fire({ title: 'حدث خطأ', text: message, icon: 'error' }),\n    info: (message) => swalInstance.fire({ title: 'إشعار', text: message, icon: 'info' }),\n    warning: (message) => swalInstance.fire({ title: 'تنبيه', text: message, icon: 'warning' })\n  };\n}\n\napp.use(router)\n\n// Add cache-busting meta tags before mounting\naddCacheBustingMeta();\n\napp.mount('#app')\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAO,mCAAmC;AAC1C,OAAOC,UAAU,MAAM,gBAAgB;;AAEvC;AACA,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;;AAExE;AACA,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;EAClC;EACA,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EACpD,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAClD,MAAME,WAAW,GAAGH,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;;EAEzD;EACAD,YAAY,CAACI,KAAK,CAAC,CAAC;EACpBC,cAAc,CAACD,KAAK,CAAC,CAAC;;EAEtB;EACA,IAAIL,SAAS,EAAEC,YAAY,CAACM,OAAO,CAAC,YAAY,EAAEP,SAAS,CAAC;EAC5D,IAAIG,QAAQ,EAAEF,YAAY,CAACM,OAAO,CAAC,WAAW,EAAEJ,QAAQ,CAAC;EACzD,IAAIC,WAAW,EAAEH,YAAY,CAACM,OAAO,CAAC,eAAe,EAAEH,WAAW,CAAC;AACrE;;AAEA;AACA,MAAMI,mBAAmB,GAAGA,CAAA,KAAM;EAChC,MAAMC,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,eAAe;IAAEC,OAAO,EAAE;EAAiD,CAAC,EACpF;IAAED,IAAI,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAW,CAAC,EACvC;IAAED,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE;EAAI,CAAC,EACjC;IAAED,IAAI,EAAE,eAAe;IAAEC,OAAO,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACmB,WAAW,CAAC;EAAE,CAAC,EAC5D;IAAEF,IAAI,EAAE,MAAM;IAAEC,OAAO,EAAEnB;EAAY,CAAC,EACtC;IAAEkB,IAAI,EAAE,cAAc;IAAEC,OAAO,EAAEnB;EAAY,CAAC,CAC/C;EAEDiB,QAAQ,CAACI,OAAO,CAACC,GAAG,IAAI;IACtB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC3CF,IAAI,CAACG,YAAY,CAAC,YAAY,EAAEJ,GAAG,CAACJ,IAAI,CAAC;IACzCK,IAAI,CAACG,YAAY,CAAC,SAAS,EAAEJ,GAAG,CAACH,OAAO,CAAC;IACzCK,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,IAAI,CAAC;EACjC,CAAC,CAAC;AACJ,CAAC;;AAED;AACAM,MAAM,CAACC,gBAAgB,CAAC,UAAU,EAAGC,KAAK,IAAK;EAC7C,IAAIA,KAAK,CAACC,SAAS,IAAIH,MAAM,CAACI,WAAW,CAACC,UAAU,CAACC,IAAI,KAAK,CAAC,EAAE;IAC/DN,MAAM,CAACO,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;EAC9B;AACF,CAAC,CAAC;;AAEF;AACAR,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAE,MAAM;EAC5C,IAAI,OAAOvB,OAAO,KAAK,WAAW,EAAE;IAClC;IACA,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACpD,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAClD,MAAME,WAAW,GAAGH,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;;IAEzD;IACAD,YAAY,CAACI,KAAK,CAAC,CAAC;IACpBC,cAAc,CAACD,KAAK,CAAC,CAAC;;IAEtB;IACA,IAAIL,SAAS,EAAEC,YAAY,CAACM,OAAO,CAAC,YAAY,EAAEP,SAAS,CAAC;IAC5D,IAAIG,QAAQ,EAAEF,YAAY,CAACM,OAAO,CAAC,WAAW,EAAEJ,QAAQ,CAAC;IACzD,IAAIC,WAAW,EAAEH,YAAY,CAACM,OAAO,CAAC,eAAe,EAAEH,WAAW,CAAC;EACrE;AACF,CAAC,CAAC;AAEF,MAAM0B,GAAG,GAAG5C,SAAS,CAACC,GAAG,CAAC;;AAE1B;AACA,MAAM4C,UAAU,GAAG;EACjBC,UAAU,EAAE,SAAS;EACrBC,KAAK,EAAE,SAAS;EAChBC,kBAAkB,EAAE,SAAS;EAC7BC,iBAAiB,EAAE,SAAS;EAC5BC,WAAW,EAAE;IACXC,KAAK,EAAE,YAAY;IACnBC,aAAa,EAAE,kBAAkB;IACjCC,YAAY,EAAE;EAChB;AACF,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE,WAAW;EACrBC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE,IAAI;EAClBC,gBAAgB,EAAE,IAAI;EACtBC,YAAY,EAAE,IAAI;EAClBC,SAAS,EAAE,IAAI;EACfC,gBAAgB,EAAE,GAAG;EACrBC,sBAAsB,EAAE,KAAK;EAC7BC,eAAe,EAAE,KAAK;EACtBC,WAAW,EAAE,QAAQ;EACrBC,IAAI,EAAE,IAAI;EACVC,GAAG,EAAE,IAAI;EACTC,aAAa,EAAE;IACbC,OAAO,EAAE;MACPC,cAAc,EAAE,eAAe;MAC/BC,aAAa,EAAE,YAAY;MAC3BP,eAAe,EAAE,KAAK;MACtBP,OAAO,EAAE;IACX,CAAC;IACDe,KAAK,EAAE;MACLF,cAAc,EAAE,aAAa;MAC7BC,aAAa,EAAE,YAAY;MAC3BP,eAAe,EAAE,KAAK;MACtBP,OAAO,EAAE;IACX;EACF;AACF,CAAC;;AAED;AACAZ,GAAG,CAAC4B,MAAM,CAACC,gBAAgB,CAACC,KAAK,GAAGvE,IAAI,CAACwE,KAAK,CAAC9B,UAAU,CAAC;;AAE1D;AACAD,GAAG,CAAC+B,KAAK,CAAC;EACRC,YAAYA,CAAA,EAAG;IACb;IACA,IAAI,CAACC,QAAQ,CAACC,YAAY,GAAGxE,WAAW;EAC1C,CAAC;EACDyE,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,IAAI,CAACC,GAAG,IAAI,IAAI,CAACA,GAAG,CAAChD,YAAY,EAAE;MACrC,IAAI,CAACgD,GAAG,CAAChD,YAAY,CAAC,iBAAiB,EAAE1B,WAAW,CAAC;IACvD;EACF;AACF,CAAC,CAAC;AAEFsC,GAAG,CAACqC,GAAG,CAAC7E,KAAK,EAAEkD,YAAY,CAAC;;AAE5B;AACA,IAAI,CAACV,GAAG,CAAC4B,MAAM,CAACC,gBAAgB,CAACS,MAAM,IAAI,OAAOtC,GAAG,CAAC4B,MAAM,CAACC,gBAAgB,CAACS,MAAM,CAACd,OAAO,KAAK,UAAU,EAAE;EAC3G,MAAMe,YAAY,GAAGvC,GAAG,CAAC4B,MAAM,CAACC,gBAAgB,CAACC,KAAK,IAAIvE,IAAI,CAACwE,KAAK,CAAC9B,UAAU,CAAC;EAChFD,GAAG,CAAC4B,MAAM,CAACC,gBAAgB,CAACS,MAAM,GAAG;IACnCd,OAAO,EAAGgB,OAAO,IAAKD,YAAY,CAACE,IAAI,CAAC;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAEH,OAAO;MAAEnB,IAAI,EAAE;IAAU,CAAC,CAAC;IAC9FM,KAAK,EAAGa,OAAO,IAAKD,YAAY,CAACE,IAAI,CAAC;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAEH,OAAO;MAAEnB,IAAI,EAAE;IAAQ,CAAC,CAAC;IACzFuB,IAAI,EAAGJ,OAAO,IAAKD,YAAY,CAACE,IAAI,CAAC;MAAEC,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAEH,OAAO;MAAEnB,IAAI,EAAE;IAAO,CAAC,CAAC;IACrFwB,OAAO,EAAGL,OAAO,IAAKD,YAAY,CAACE,IAAI,CAAC;MAAEC,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAEH,OAAO;MAAEnB,IAAI,EAAE;IAAU,CAAC;EAC5F,CAAC;AACH;AAEArB,GAAG,CAACqC,GAAG,CAAC/E,MAAM,CAAC;;AAEf;AACAoB,mBAAmB,CAAC,CAAC;AAErBsB,GAAG,CAAC8C,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}