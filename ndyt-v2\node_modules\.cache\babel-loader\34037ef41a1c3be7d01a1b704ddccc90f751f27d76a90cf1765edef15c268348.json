{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport router from '@/router';\nclass ApiService {\n  constructor() {\n    this.baseURL = '/api/v1/ndyt-activities';\n  }\n  async request(endpoint, options = {}) {\n    const token = localStorage.getItem('ndyt_token');\n    const teamPin = localStorage.getItem('ndyt_team_pin');\n    const config = {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      ...options\n    };\n\n    // Add authorization header if token exists\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`;\n    }\n\n    // Add team pin header if it exists\n    if (teamPin && (config.method === 'POST' || config.method === 'PUT' || config.method === 'DELETE')) {\n      config.headers['x-team-pin'] = teamPin;\n    }\n    try {\n      const response = await fetch(`${this.baseURL}${endpoint}`, config);\n\n      // Handle token expiration globally\n      if (response.status === 401 || response.status === 403) {\n        this.handleTokenExpiration();\n        throw new Error('Session expired');\n      }\n      return response;\n    } catch (error) {\n      // Re-throw the error so components can handle it\n      throw error;\n    }\n  }\n  handleTokenExpiration() {\n    // Clear all authentication data\n    localStorage.removeItem('ndyt_token');\n    localStorage.removeItem('ndyt_user');\n    localStorage.removeItem('ndyt_team_pin');\n\n    // Show toast notification if available\n    if (window.Vue && window.Vue.config.globalProperties.$toast) {\n      window.Vue.config.globalProperties.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى');\n    }\n\n    // Redirect to login page\n    router.push('/login');\n  }\n\n  // Convenience methods\n  async get(endpoint, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'GET'\n    });\n  }\n  async post(endpoint, data, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'POST',\n      body: JSON.stringify(data)\n    });\n  }\n  async put(endpoint, data, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'PUT',\n      body: JSON.stringify(data)\n    });\n  }\n  async delete(endpoint, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'DELETE'\n    });\n  }\n\n  // File upload method\n  async uploadFile(file, options = {}) {\n    const token = localStorage.getItem('ndyt_token');\n    const formData = new FormData();\n    formData.append('file', file);\n    const config = {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${token}`\n      },\n      body: formData,\n      ...options\n    };\n    try {\n      const response = await fetch(`${this.baseURL}/upload-file`, config);\n      if (response.status === 401 || response.status === 403) {\n        this.handleTokenExpiration();\n        throw new Error('Session expired');\n      }\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  }\n}\nexport default new ApiService();", "map": {"version": 3, "names": ["router", "ApiService", "constructor", "baseURL", "request", "endpoint", "options", "token", "localStorage", "getItem", "teamPin", "config", "method", "headers", "response", "fetch", "status", "handleTokenExpiration", "Error", "error", "removeItem", "window", "<PERSON><PERSON>", "globalProperties", "$toast", "push", "get", "post", "data", "body", "JSON", "stringify", "put", "delete", "uploadFile", "file", "formData", "FormData", "append"], "sources": ["F:/My Apps/iqtp/iqtp_backend/ndyt-v2/src/services/api.js"], "sourcesContent": ["import router from '@/router'\n\nclass ApiService {\n  constructor() {\n    this.baseURL = '/api/v1/ndyt-activities'\n  }\n\n  async request(endpoint, options = {}) {\n    const token = localStorage.getItem('ndyt_token')\n    const teamPin = localStorage.getItem('ndyt_team_pin')\n    \n    const config = {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      ...options\n    }\n\n    // Add authorization header if token exists\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`\n    }\n\n    // Add team pin header if it exists\n    if (teamPin && (config.method === 'POST' || config.method === 'PUT' || config.method === 'DELETE')) {\n      config.headers['x-team-pin'] = teamPin\n    }\n\n    try {\n      const response = await fetch(`${this.baseURL}${endpoint}`, config)\n      \n      // Handle token expiration globally\n      if (response.status === 401 || response.status === 403) {\n        this.handleTokenExpiration()\n        throw new Error('Session expired')\n      }\n\n      return response\n    } catch (error) {\n      // Re-throw the error so components can handle it\n      throw error\n    }\n  }\n\n  handleTokenExpiration() {\n    // Clear all authentication data\n    localStorage.removeItem('ndyt_token')\n    localStorage.removeItem('ndyt_user')\n    localStorage.removeItem('ndyt_team_pin')\n    \n    // Show toast notification if available\n    if (window.Vue && window.Vue.config.globalProperties.$toast) {\n      window.Vue.config.globalProperties.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى')\n    }\n    \n    // Redirect to login page\n    router.push('/login')\n  }\n\n  // Convenience methods\n  async get(endpoint, options = {}) {\n    return this.request(endpoint, { ...options, method: 'GET' })\n  }\n\n  async post(endpoint, data, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'POST',\n      body: JSON.stringify(data)\n    })\n  }\n\n  async put(endpoint, data, options = {}) {\n    return this.request(endpoint, {\n      ...options,\n      method: 'PUT',\n      body: JSON.stringify(data)\n    })\n  }\n\n  async delete(endpoint, options = {}) {\n    return this.request(endpoint, { ...options, method: 'DELETE' })\n  }\n\n  // File upload method\n  async uploadFile(file, options = {}) {\n    const token = localStorage.getItem('ndyt_token')\n    const formData = new FormData()\n    formData.append('file', file)\n\n    const config = {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${token}`\n      },\n      body: formData,\n      ...options\n    }\n\n    try {\n      const response = await fetch(`${this.baseURL}/upload-file`, config)\n      \n      if (response.status === 401 || response.status === 403) {\n        this.handleTokenExpiration()\n        throw new Error('Session expired')\n      }\n\n      return response\n    } catch (error) {\n      throw error\n    }\n  }\n}\n\nexport default new ApiService()\n"], "mappings": ";AAAA,OAAOA,MAAM,MAAM,UAAU;AAE7B,MAAMC,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,yBAAyB;EAC1C;EAEA,MAAMC,OAAOA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAErD,MAAME,MAAM,GAAG;MACbC,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,GAAGP,OAAO,CAACO;MACb,CAAC;MACD,GAAGP;IACL,CAAC;;IAED;IACA,IAAIC,KAAK,EAAE;MACTI,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUN,KAAK,EAAE;IACrD;;IAEA;IACA,IAAIG,OAAO,KAAKC,MAAM,CAACC,MAAM,KAAK,MAAM,IAAID,MAAM,CAACC,MAAM,KAAK,KAAK,IAAID,MAAM,CAACC,MAAM,KAAK,QAAQ,CAAC,EAAE;MAClGD,MAAM,CAACE,OAAO,CAAC,YAAY,CAAC,GAAGH,OAAO;IACxC;IAEA,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACZ,OAAO,GAAGE,QAAQ,EAAE,EAAEM,MAAM,CAAC;;MAElE;MACA,IAAIG,QAAQ,CAACE,MAAM,KAAK,GAAG,IAAIF,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;QACtD,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAC5B,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACpC;MAEA,OAAOJ,QAAQ;IACjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd;MACA,MAAMA,KAAK;IACb;EACF;EAEAF,qBAAqBA,CAAA,EAAG;IACtB;IACAT,YAAY,CAACY,UAAU,CAAC,YAAY,CAAC;IACrCZ,YAAY,CAACY,UAAU,CAAC,WAAW,CAAC;IACpCZ,YAAY,CAACY,UAAU,CAAC,eAAe,CAAC;;IAExC;IACA,IAAIC,MAAM,CAACC,GAAG,IAAID,MAAM,CAACC,GAAG,CAACX,MAAM,CAACY,gBAAgB,CAACC,MAAM,EAAE;MAC3DH,MAAM,CAACC,GAAG,CAACX,MAAM,CAACY,gBAAgB,CAACC,MAAM,CAACL,KAAK,CAAC,iDAAiD,CAAC;IACpG;;IAEA;IACAnB,MAAM,CAACyB,IAAI,CAAC,QAAQ,CAAC;EACvB;;EAEA;EACA,MAAMC,GAAGA,CAACrB,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAChC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAAE,GAAGC,OAAO;MAAEM,MAAM,EAAE;IAAM,CAAC,CAAC;EAC9D;EAEA,MAAMe,IAAIA,CAACtB,QAAQ,EAAEuB,IAAI,EAAEtB,OAAO,GAAG,CAAC,CAAC,EAAE;IACvC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAC5B,GAAGC,OAAO;MACVM,MAAM,EAAE,MAAM;MACdiB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACH,IAAI;IAC3B,CAAC,CAAC;EACJ;EAEA,MAAMI,GAAGA,CAAC3B,QAAQ,EAAEuB,IAAI,EAAEtB,OAAO,GAAG,CAAC,CAAC,EAAE;IACtC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAC5B,GAAGC,OAAO;MACVM,MAAM,EAAE,KAAK;MACbiB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACH,IAAI;IAC3B,CAAC,CAAC;EACJ;EAEA,MAAMK,MAAMA,CAAC5B,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACnC,OAAO,IAAI,CAACF,OAAO,CAACC,QAAQ,EAAE;MAAE,GAAGC,OAAO;MAAEM,MAAM,EAAE;IAAS,CAAC,CAAC;EACjE;;EAEA;EACA,MAAMsB,UAAUA,CAACC,IAAI,EAAE7B,OAAO,GAAG,CAAC,CAAC,EAAE;IACnC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,MAAM2B,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAE7B,MAAMxB,MAAM,GAAG;MACbC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,eAAe,EAAE,UAAUN,KAAK;MAClC,CAAC;MACDsB,IAAI,EAAEO,QAAQ;MACd,GAAG9B;IACL,CAAC;IAED,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACZ,OAAO,cAAc,EAAEQ,MAAM,CAAC;MAEnE,IAAIG,QAAQ,CAACE,MAAM,KAAK,GAAG,IAAIF,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;QACtD,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAC5B,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACpC;MAEA,OAAOJ,QAAQ;IACjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF;AACF;AAEA,eAAe,IAAIlB,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}