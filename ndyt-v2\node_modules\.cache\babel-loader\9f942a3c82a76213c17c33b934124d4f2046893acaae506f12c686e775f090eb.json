{"ast": null, "code": "import { createElementVNode as _createElementVNode, vModelText as _vModelText, withDirectives as _withDirectives, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createStaticVNode as _createStaticVNode, Fragment as _Fragment } from \"vue\";\nimport _imports_0 from '../assets/ndyt_logo.jpg';\nconst _hoisted_1 = {\n  class: \"auth-wrapper\"\n};\nconst _hoisted_2 = {\n  class: \"card\"\n};\nconst _hoisted_3 = {\n  class: \"field\"\n};\nconst _hoisted_4 = {\n  class: \"field\"\n};\nconst _hoisted_5 = {\n  class: \"field\"\n};\nconst _hoisted_6 = [\"disabled\"];\nconst _hoisted_7 = {\n  key: 0\n};\nconst _hoisted_8 = {\n  key: 1\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"error\"\n};\nconst _hoisted_10 = {\n  class: \"hint\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_cache[10] || (_cache[10] = _createStaticVNode(\"<div class=\\\"navbar\\\" data-v-5c6101e4><div class=\\\"navbar-content\\\" data-v-5c6101e4><div class=\\\"navbar-text\\\" data-v-5c6101e4><span class=\\\"org-name\\\" data-v-5c6101e4>المجلس الأعلى للشباب</span><img class=\\\"logo\\\" src=\\\"\" + _imports_0 + \"\\\" alt=\\\"شعار الفريق\\\" data-v-5c6101e4><span class=\\\"team-name\\\" data-v-5c6101e4>الفريق الوطني للشباب الرقمي</span></div></div></div>\", 1)), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[9] || (_cache[9] = _createElementVNode(\"h1\", null, \"تسجيل الدخول\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_cache[4] || (_cache[4] = _createElementVNode(\"label\", null, \"اسم المستخدم\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.username = $event),\n    type: \"text\",\n    dir: \"rtl\",\n    placeholder: \"ادخل اسم المستخدم\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.username]])]), _createElementVNode(\"div\", _hoisted_4, [_cache[5] || (_cache[5] = _createElementVNode(\"label\", null, \"كلمة المرور\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.password = $event),\n    type: \"password\",\n    dir: \"rtl\",\n    placeholder: \"ادخل كلمة المرور\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.password]])]), _createElementVNode(\"div\", _hoisted_5, [_cache[6] || (_cache[6] = _createElementVNode(\"label\", null, \"رمز الفريق الرقمي\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.team_pin = $event),\n    type: \"text\",\n    dir: \"rtl\",\n    placeholder: \"ادخل رمز الفريق الرقمي\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.team_pin]])]), _createElementVNode(\"button\", {\n    disabled: $data.busy,\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.doLogin && $options.doLogin(...args))\n  }, [!$data.busy ? (_openBlock(), _createElementBlock(\"span\", _hoisted_7, \"دخول\")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_8, \"... جاري الدخول\"))], 8 /* PROPS */, _hoisted_6), $data.error ? (_openBlock(), _createElementBlock(\"p\", _hoisted_9, _toDisplayString($data.error), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"p\", _hoisted_10, [_cache[8] || (_cache[8] = _createTextVNode(\"ليس لديك حساب؟ \", -1 /* CACHED */)), _createVNode(_component_router_link, {\n    to: \"/register\"\n  }, {\n    default: _withCtx(() => [...(_cache[7] || (_cache[7] = [_createTextVNode(\"سجل الآن\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  })])])])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "$data", "username", "$event", "type", "dir", "placeholder", "_hoisted_4", "password", "_hoisted_5", "team_pin", "disabled", "busy", "onClick", "_cache", "args", "$options", "do<PERSON><PERSON><PERSON>", "_createElementBlock", "_hoisted_7", "_hoisted_8", "error", "_hoisted_9", "_toDisplayString", "_hoisted_10", "_createVNode", "_component_router_link", "to"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\LoginView.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'LoginView',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      password: '',\r\n      team_pin: '',\r\n      busy: false,\r\n      error: ''\r\n    };\r\n  },\r\n  mounted() {\r\n    // Aggressive cache busting for login component\r\n    const cacheBuster = Date.now() + Math.random().toString(36).substr(2, 9);\r\n    \r\n    // Add cache-busting meta tags specifically for login\r\n    const metaTags = [\r\n      { name: 'cache-control', content: 'no-cache, no-store, must-revalidate, max-age=0' },\r\n      { name: 'pragma', content: 'no-cache' },\r\n      { name: 'expires', content: '0' },\r\n      { name: 'last-modified', content: new Date().toUTCString() },\r\n      { name: 'etag', content: cacheBuster },\r\n      { name: 'login-cache-buster', content: cacheBuster }\r\n    ];\r\n    \r\n    metaTags.forEach(tag => {\r\n      // Remove existing meta tags with same name\r\n      const existing = document.querySelector(`meta[http-equiv=\"${tag.name}\"]`);\r\n      if (existing) existing.remove();\r\n      \r\n      // Add new meta tag\r\n      const meta = document.createElement('meta');\r\n      meta.setAttribute('http-equiv', tag.name);\r\n      meta.setAttribute('content', tag.content);\r\n      document.head.appendChild(meta);\r\n    });\r\n    \r\n    // Force component refresh to prevent caching issues\r\n    this.$forceUpdate();\r\n    \r\n    // Clear any cached form data\r\n    this.username = '';\r\n    this.password = '';\r\n    this.team_pin = '';\r\n    this.error = '';\r\n    \r\n    // Clear only non-authentication browser storage\r\n    if (typeof Storage !== 'undefined') {\r\n      // Only clear sessionStorage, preserve localStorage auth tokens\r\n      sessionStorage.clear();\r\n    }\r\n    \r\n    // Force browser to not cache this page\r\n    if (window.history && window.history.replaceState) {\r\n      const url = new URL(window.location.href);\r\n      url.searchParams.set('_cb', cacheBuster);\r\n      url.searchParams.set('_nocache', '1');\r\n      window.history.replaceState(null, null, url.toString());\r\n    }\r\n    \r\n    // Add cache-busting attribute to component element\r\n    if (this.$el && this.$el.setAttribute) {\r\n      this.$el.setAttribute('data-login-cache-bust', cacheBuster);\r\n      this.$el.setAttribute('data-no-cache', 'true');\r\n    }\r\n  },\r\n  beforeUnmount() {\r\n    // Clear form data when component is destroyed\r\n    this.username = '';\r\n    this.password = '';\r\n    this.team_pin = '';\r\n    this.error = '';\r\n  },\r\n  methods: {\r\n    async doLogin() {\r\n      this.error = '';\r\n      if (!this.username || !this.password || !this.team_pin) {\r\n        this.error = 'يرجى تعبئة جميع الحقول';\r\n        return;\r\n      }\r\n      this.busy = true;\r\n      try {\r\n        const res = await fetch('/api/v1/ndyt-activities/auth/login', {\r\n          method: 'POST',\r\n          headers: { 'Content-Type': 'application/json' },\r\n          body: JSON.stringify({ username: this.username, password: this.password, team_pin: this.team_pin })\r\n        });\r\n        const data = await res.json();\r\n        if (!res.ok) throw new Error(data.error || 'فشل تسجيل الدخول');\r\n        localStorage.setItem('ndyt_token', data.token);\r\n        // optional: store user info\r\n        localStorage.setItem('ndyt_user', JSON.stringify(data.user || {}));\r\n        // store team pin for later submissions\r\n        localStorage.setItem('ndyt_team_pin', this.team_pin);\r\n        const redirect = this.$route.query.redirect || '/';\r\n        this.$router.replace(redirect);\r\n      } catch (e) {\r\n        this.error = e.message;\r\n      } finally {\r\n        this.busy = false;\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<template>\r\n  <div class=\"navbar\">\r\n    <div class=\"navbar-content\">\r\n      <div class=\"navbar-text\">\r\n        <span class=\"org-name\">المجلس الأعلى للشباب</span>\r\n        <img class=\"logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n        <span class=\"team-name\">الفريق الوطني للشباب الرقمي</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"auth-wrapper\">\r\n    <div class=\"card\">\r\n      <h1>تسجيل الدخول</h1>\r\n      <div class=\"field\">\r\n        <label>اسم المستخدم</label>\r\n        <input v-model=\"username\" type=\"text\" dir=\"rtl\" placeholder=\"ادخل اسم المستخدم\" />\r\n      </div>\r\n      <div class=\"field\">\r\n        <label>كلمة المرور</label>\r\n        <input v-model=\"password\" type=\"password\" dir=\"rtl\" placeholder=\"ادخل كلمة المرور\" />\r\n      </div>\r\n      <div class=\"field\">\r\n        <label>رمز الفريق الرقمي</label>\r\n        <input v-model=\"team_pin\" type=\"text\" dir=\"rtl\" placeholder=\"ادخل رمز الفريق الرقمي\" />\r\n      </div>\r\n      <button :disabled=\"busy\" @click=\"doLogin\">\r\n        <span v-if=\"!busy\">دخول</span>\r\n        <span v-else>... جاري الدخول</span>\r\n      </button>\r\n      <p v-if=\"error\" class=\"error\">{{ error }}</p>\r\n      <p class=\"hint\">ليس لديك حساب؟ <router-link to=\"/register\">سجل الآن</router-link></p>\r\n    </div>\r\n  </div>\r\n</template>\r\n<style scoped>\r\n.navbar {\r\n  background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n  border: 2px solid #3a3a5e;\r\n  border-radius: 12px;\r\n  margin: 16px;\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.navbar-content {\r\n  padding: 16px 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n}\r\n\r\n.navbar-text {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  flex: 1;\r\n}\r\n\r\n.org-name, .team-name {\r\n  font-weight: 700;\r\n  font-size: clamp(16px, 3vw, 24px);\r\n  color: #f5f5f5;\r\n  text-align: center;\r\n  line-height: 1.4;\r\n}\r\n\r\n.logo {\r\n  height: clamp(50px, 8vw, 70px);\r\n  width: auto;\r\n  border: 2px solid #4a5568;\r\n  border-radius: 50%;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.logo:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.auth-wrapper {\r\n  display: grid;\r\n  place-items: center;\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #101214, #1b1f24);\r\n  padding: 16px;\r\n}\r\n.card {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 20px;\r\n  padding: 32px;\r\n  width: 100%;\r\n  max-width: 420px;\r\n  color: #e5e7eb;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n  transition: all 0.3s ease;\r\n  direction: rtl;\r\n}\r\n.card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);\r\n}\r\n.card h1 {\r\n  font-size: 28px;\r\n  margin: 0 0 32px;\r\n  text-align: center;\r\n  font-weight: 700;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n.field {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  margin: 20px 0;\r\n}\r\n.field label {\r\n  font-size: 14px;\r\n  color: #cbd5e1;\r\n  font-weight: 600;\r\n  text-align: right;\r\n}\r\ninput {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  color: #e5e7eb;\r\n  border-radius: 12px;\r\n  padding: 14px 16px;\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n  text-align: right;\r\n  direction: rtl;\r\n}\r\ninput:focus {\r\n  outline: none;\r\n  border-color: #4f46e5;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\r\n}\r\ninput::placeholder {\r\n  color: #cbd5e1;\r\n  text-align: right;\r\n  opacity: 1;\r\n}\r\nbutton {\r\n  width: 100%;\r\n  height: 48px;\r\n  min-width: 160px;\r\n  border: none;\r\n  border-radius: 12px;\r\n  background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n  color: white;\r\n  cursor: pointer;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);\r\n}\r\nbutton:hover {\r\n  background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);\r\n}\r\nbutton:active {\r\n  transform: translateY(0);\r\n}\r\nbutton:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n.error {\r\n  color: #ef4444;\r\n  margin-top: 10px;\r\n  text-align: center;\r\n  font-size: 14px;\r\n}\r\n.hint {\r\n  margin-top: 24px;\r\n  color: #cbd5e1;\r\n  text-align: center;\r\n  font-size: 14px;\r\n}\r\n.hint a {\r\n  color: #4f46e5;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  transition: color 0.3s ease;\r\n}\r\n.hint a:hover {\r\n  color: #6366f1;\r\n  text-decoration: underline;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .card {\r\n    padding: 24px;\r\n    margin: 16px;\r\n  }\r\n  \r\n  .card h1 {\r\n    font-size: 24px;\r\n    margin-bottom: 24px;\r\n  }\r\n  \r\n  input {\r\n    padding: 12px 14px;\r\n    font-size: 16px; /* Prevents zoom on iOS */\r\n  }\r\n  \r\n  button {\r\n    padding: 12px;\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .navbar-content {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .navbar-text {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .org-name, .team-name {\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .navbar {\r\n    margin: 8px;\r\n  }\r\n  \r\n  .navbar-content {\r\n    padding: 12px 16px;\r\n  }\r\n  \r\n  .org-name, .team-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .logo {\r\n    height: 50px;\r\n  }\r\n  \r\n  .auth-wrapper {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .card {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .card h1 {\r\n    font-size: 22px;\r\n  }\r\n  \r\n  .field {\r\n    margin: 16px 0;\r\n  }\r\n}\r\n</style>"], "mappings": ";OA+G0BA,UAA6B;;EAKhDC,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAO;;EAIbA,KAAK,EAAC;AAAO;;EAIbA,KAAK,EAAC;AAAO;;;;;;;;;;EAQFA,KAAK,EAAC;;;EACnBA,KAAK,EAAC;AAAM;;;yeApBnBC,mBAAA,CAsBM,OAtBNC,UAsBM,GArBJD,mBAAA,CAoBM,OApBNE,UAoBM,G,0BAnBJF,mBAAA,CAAqB,YAAjB,cAAY,qBAChBA,mBAAA,CAGM,OAHNG,UAGM,G,0BAFJH,mBAAA,CAA2B,eAApB,cAAY,qB,gBACnBA,mBAAA,CAAkF;+DAAlEI,KAAA,CAAAC,QAAQ,GAAAC,MAAA;IAAEC,IAAI,EAAC,MAAM;IAACC,GAAG,EAAC,KAAK;IAACC,WAAW,EAAC;iDAA5CL,KAAA,CAAAC,QAAQ,E,KAE1BL,mBAAA,CAGM,OAHNU,UAGM,G,0BAFJV,mBAAA,CAA0B,eAAnB,aAAW,qB,gBAClBA,mBAAA,CAAqF;+DAArEI,KAAA,CAAAO,QAAQ,GAAAL,MAAA;IAAEC,IAAI,EAAC,UAAU;IAACC,GAAG,EAAC,KAAK;IAACC,WAAW,EAAC;iDAAhDL,KAAA,CAAAO,QAAQ,E,KAE1BX,mBAAA,CAGM,OAHNY,UAGM,G,0BAFJZ,mBAAA,CAAgC,eAAzB,mBAAiB,qB,gBACxBA,mBAAA,CAAuF;+DAAvEI,KAAA,CAAAS,QAAQ,GAAAP,MAAA;IAAEC,IAAI,EAAC,MAAM;IAACC,GAAG,EAAC,KAAK;IAACC,WAAW,EAAC;iDAA5CL,KAAA,CAAAS,QAAQ,E,KAE1Bb,mBAAA,CAGS;IAHAc,QAAQ,EAAEV,KAAA,CAAAW,IAAI;IAAGC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,OAAA,IAAAD,QAAA,CAAAC,OAAA,IAAAF,IAAA,CAAO;OACzBd,KAAA,CAAAW,IAAI,I,cAAjBM,mBAAA,CAA8B,QAAAC,UAAA,EAAX,MAAI,M,cACvBD,mBAAA,CAAmC,QAAAE,UAAA,EAAtB,iBAAe,G,8BAErBnB,KAAA,CAAAoB,KAAK,I,cAAdH,mBAAA,CAA6C,KAA7CI,UAA6C,EAAAC,gBAAA,CAAZtB,KAAA,CAAAoB,KAAK,oB,mCACtCxB,mBAAA,CAAqF,KAArF2B,WAAqF,G,2CAArE,iBAAe,qBAAAC,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAW;sBAAC,MAAQ,KAAAb,MAAA,QAAAA,MAAA,O,iBAAR,UAAQ,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}