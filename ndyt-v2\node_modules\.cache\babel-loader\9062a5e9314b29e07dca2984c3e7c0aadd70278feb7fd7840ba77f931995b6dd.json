{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { Document, Packer, Paragraph, TextRun, Table, TableRow, TableCell, WidthType, AlignmentType, BorderStyle, ImageRun } from 'docx';\nimport { saveAs } from 'file-saver';\nexport default {\n  name: 'ActivityView',\n  data() {\n    return {\n      activityOwnerName: '',\n      activityTitleName: '',\n      activityDescription: '',\n      activityDateInput: '',\n      activityIdeaInput: '',\n      activityGoals: [],\n      targetGroups: [],\n      audienceCount: 0,\n      activityLocationInput: '',\n      activityTimeInput: '',\n      activityDurationInput: '',\n      activityBudgetInput: 0,\n      budgetDetailsList: [],\n      activityTotalBudget: 0,\n      activityLevels: [],\n      activityStatus: '',\n      filledActivity: {\n        title: this.activityTitleName,\n        owner: this.activityOwnerName,\n        description: this.activityDescription,\n        date: this.activityDateInput,\n        idea: this.activityIdeaInput,\n        goals: this.activityGoals,\n        audience: this.targetGroups,\n        location: this.activityLocationInput,\n        time: this.activityTimeInput,\n        duration: this.activityDurationInput,\n        budget: this.activityBudgetInput,\n        budgetDetails: this.budgetDetailsList,\n        totalBudget: this.activityTotalBudget\n      },\n      //------------------------------------\n      activities: [],\n      //------------------------------------\n      user: null,\n      showUserMenu: false,\n      myActivities: [],\n      loadingActivities: false,\n      editingActivity: null,\n      showMyActivities: false,\n      currentView: 'submit',\n      coordinatorName: '',\n      selectedFilter: null,\n      // null means show all, otherwise filter by state\n      showAccountSettings: false,\n      accountForm: {\n        fullName: '',\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: '',\n        teamPin: ''\n      },\n      updatingAccount: false,\n      showPinConfirmation: false,\n      pinConfirmationData: {\n        pin: '',\n        action: '',\n        // 'edit' or 'delete'\n        activity: null,\n        callback: null\n      },\n      showActivityModal: false,\n      selectedActivity: null\n    };\n  },\n  computed: {\n    filteredActivities() {\n      if (!this.selectedFilter) {\n        return this.myActivities;\n      }\n      return this.myActivities.filter(activity => activity.state === this.selectedFilter);\n    }\n  },\n  methods: {\n    selectFilter(state) {\n      // Toggle filter: if same state is clicked, clear filter; otherwise set new filter\n      this.selectedFilter = this.selectedFilter === state ? null : state;\n    },\n    toggleUserMenu() {\n      this.showUserMenu = !this.showUserMenu;\n    },\n    logout() {\n      localStorage.removeItem('ndyt_token');\n      localStorage.removeItem('ndyt_user');\n      localStorage.removeItem('ndyt_team_pin');\n      this.$router.push('/login');\n    },\n    goToAdmin() {\n      this.showUserMenu = false;\n      this.$router.push('/admin');\n    },\n    openAccountSettings() {\n      this.showUserMenu = false;\n      this.accountForm.fullName = this.user?.full_name || '';\n      this.accountForm.teamPin = localStorage.getItem('ndyt_team_pin') || '';\n      this.accountForm.currentPassword = '';\n      this.accountForm.newPassword = '';\n      this.accountForm.confirmPassword = '';\n      this.showAccountSettings = true;\n    },\n    closeAccountSettings() {\n      this.showAccountSettings = false;\n      this.accountForm = {\n        fullName: '',\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: '',\n        teamPin: ''\n      };\n    },\n    async updateAccountSettings() {\n      // Validate form\n      if (!this.accountForm.fullName.trim()) {\n        await this.$swal.fire({\n          title: 'خطأ في البيانات',\n          text: 'يرجى إدخال الاسم الكامل',\n          icon: 'error'\n        });\n        return;\n      }\n      if (this.accountForm.newPassword && this.accountForm.newPassword !== this.accountForm.confirmPassword) {\n        await this.$swal.fire({\n          title: 'خطأ في البيانات',\n          text: 'كلمة المرور الجديدة وتأكيدها غير متطابقتان',\n          icon: 'error'\n        });\n        return;\n      }\n      if (this.accountForm.newPassword && this.accountForm.newPassword.length < 6) {\n        await this.$swal.fire({\n          title: 'خطأ في البيانات',\n          text: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',\n          icon: 'error'\n        });\n        return;\n      }\n      this.updatingAccount = true;\n      try {\n        const updateData = {\n          full_name: this.accountForm.fullName.trim(),\n          team_pin: this.accountForm.teamPin.trim()\n        };\n\n        // Only include password if user wants to change it\n        if (this.accountForm.newPassword) {\n          updateData.current_password = this.accountForm.currentPassword;\n          updateData.new_password = this.accountForm.newPassword;\n        }\n        const response = await this.$api.put('/user/update-profile', updateData);\n        if (response.ok) {\n          await response.json();\n\n          // Update local user data\n          this.user.full_name = this.accountForm.fullName.trim();\n          localStorage.setItem('ndyt_user', JSON.stringify(this.user));\n          localStorage.setItem('ndyt_team_pin', this.accountForm.teamPin.trim());\n          await this.$swal.fire({\n            title: 'تم التحديث بنجاح',\n            text: 'تم تحديث معلومات الحساب بنجاح',\n            icon: 'success'\n          });\n          this.closeAccountSettings();\n        } else {\n          const errorData = await response.json();\n          await this.$swal.fire({\n            title: 'خطأ في التحديث',\n            text: errorData.error || 'فشل في تحديث معلومات الحساب',\n            icon: 'error'\n          });\n        }\n      } catch (error) {\n        if (error.message === 'Session expired') {\n          // Token expiration is already handled by the API service\n          return;\n        }\n        console.error('Error updating account:', error);\n        await this.$swal.fire({\n          title: 'خطأ في الاتصال',\n          text: 'حدث خطأ أثناء تحديث معلومات الحساب',\n          icon: 'error'\n        });\n      } finally {\n        this.updatingAccount = false;\n      }\n    },\n    async submitCV() {\n      try {\n        // Validate coordinator name\n        if (!this.coordinatorName.trim()) {\n          await this.$swal.fire({\n            title: 'خطأ في البيانات',\n            text: 'يرجى إدخال اسم منسق المحافظة',\n            icon: 'error'\n          });\n          return;\n        }\n\n        // Collect all activity items\n        const activityItems = document.querySelectorAll('.activity-item');\n        if (activityItems.length === 0) {\n          await this.$swal.fire({\n            title: 'خطأ في البيانات',\n            text: 'يرجى إضافة نشاط واحد على الأقل',\n            icon: 'error'\n          });\n          return;\n        }\n        const token = localStorage.getItem('ndyt_token');\n        const teamPin = localStorage.getItem('ndyt_team_pin');\n        if (!token) {\n          this.$toast.error('يرجى تسجيل الدخول أولاً');\n          this.$router.push('/login');\n          return;\n        }\n        const activities = [];\n        let hasErrors = false;\n\n        // Process each activity item\n        for (let index = 0; index < activityItems.length; index++) {\n          const item = activityItems[index];\n\n          // Extract basic fields - be more specific about selectors\n          const ownerInput = item.querySelector('input[placeholder=\"اسم صاحب االنشاط\"]');\n          const titleInput = item.querySelector('input[placeholder=\"عنوان النشاط\"]');\n          const shortDescInput = item.querySelector('input[placeholder=\"وصف قصير للنشاط\"]');\n          const dateInput = item.querySelector('input[type=\"date\"]');\n          const stateSelect = item.querySelector('select.activity-input');\n          const ownerName = ownerInput?.value?.trim();\n          const title = titleInput?.value?.trim();\n          const shortDescription = shortDescInput?.value?.trim();\n          const activityDate = dateInput?.value;\n          const state = stateSelect?.value;\n\n          // Debug logging to verify state extraction\n          console.log(`Activity ${index + 1} - State extracted:`, state, 'from element:', stateSelect);\n\n          // Extract additional fields by finding specific elements\n          const ideaTextarea = item.querySelector('textarea');\n          const activityIdea = ideaTextarea?.value?.trim() || '';\n\n          // Extract goals\n          const goalInputs = item.querySelectorAll('ul li input[placeholder=\"هدف النشاط\"]');\n          const activityGoals = Array.from(goalInputs).map(input => ({\n            text: input.value.trim()\n          })).filter(goal => goal.text);\n\n          // Extract target groups\n          const targetGroupInputs = item.querySelectorAll('ul li input[placeholder=\"الفئة المستهدفة\"]');\n          const targetGroups = Array.from(targetGroupInputs).map(input => ({\n            text: input.value.trim()\n          })).filter(group => group.text);\n\n          // Extract audience count\n          const audienceInput = item.querySelector('input[type=\"stat-number\"]');\n          const audienceCount = audienceInput?.value ? parseInt(audienceInput.value) : null;\n\n          // Extract location, time, duration\n          const locationInput = item.querySelector('input[placeholder=\"موقع النشاط\"]');\n          const activityLocation = locationInput?.value?.trim() || '';\n          const timeInput = item.querySelector('input[type=\"time\"]');\n          const activityTime = timeInput?.value || null;\n          const durationInput = item.querySelector('input[placeholder=\"مدة النشاط\"]');\n          const activityDuration = durationInput?.value?.trim() || '';\n\n          // Extract budget information\n          const budgetInputs = item.querySelectorAll('.budget-input input');\n          const activityBudget = budgetInputs[0]?.value ? parseFloat(budgetInputs[0].value) : null;\n          const totalBudget = budgetInputs[1]?.value ? parseFloat(budgetInputs[1].value) : null;\n\n          // Extract budget details using the specific class\n          const budgetDetailItems = item.querySelectorAll('.budget-detail-item');\n          const budgetDetails = [];\n          budgetDetailItems.forEach(detailItem => {\n            // Each budget detail item has multiple rows with inputs\n            const allInputs = detailItem.querySelectorAll('input');\n            if (allInputs.length >= 5) {\n              const detail = {\n                name: allInputs[0]?.value?.trim() || '',\n                type: allInputs[1]?.value?.trim() || '',\n                amount: allInputs[2]?.value ? parseInt(allInputs[2].value) : 0,\n                price: allInputs[3]?.value ? parseFloat(allInputs[3].value) : 0,\n                budgetPrice: allInputs[4]?.value ? parseFloat(allInputs[4].value) : 0\n              };\n              if (detail.name) budgetDetails.push(detail);\n            }\n          });\n\n          // Debug logging for budget details\n          console.log(`Activity ${index + 1} - Budget details extracted:`, budgetDetails);\n\n          // Extract activity levels\n          const levelInputs = item.querySelectorAll('ul li input[placeholder=\"وصف المرحلة التنفيذية\"]');\n          const activityLevels = Array.from(levelInputs).map(input => ({\n            description: input.value.trim()\n          })).filter(level => level.description);\n\n          // Validate required fields\n          if (!ownerName || !title || !activityDate || !state) {\n            let missingFields = [];\n            if (!ownerName) missingFields.push('اسم صاحب النشاط');\n            if (!title) missingFields.push('عنوان النشاط');\n            if (!activityDate) missingFields.push('تاريخ النشاط');\n            if (!state) missingFields.push('حالة النشاط');\n            this.$swal.fire({\n              title: 'خطأ في البيانات',\n              text: `يرجى ملء الحقول المطلوبة للنشاط رقم ${index + 1}: ${missingFields.join(', ')}`,\n              icon: 'error'\n            });\n            hasErrors = true;\n            break;\n          }\n          let fileId = null;\n\n          // Handle file upload if a file is selected\n          if (item.selectedFile) {\n            try {\n              const formData = new FormData();\n              formData.append('file', item.selectedFile);\n              const uploadResponse = await fetch('/api/v1/ndyt-activities/upload-file', {\n                method: 'POST',\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                body: formData\n              });\n              if (uploadResponse.ok) {\n                const uploadResult = await uploadResponse.json();\n                if (uploadResult.file && uploadResult.file.id) {\n                  fileId = uploadResult.file.id;\n                }\n              } else {\n                const errorData = await uploadResponse.json();\n                this.$toast.error(`فشل في رفع ملف النشاط رقم ${index + 1}: ${errorData.error || 'خطأ غير معروف'}`);\n                hasErrors = true;\n                break;\n              }\n            } catch (uploadError) {\n              console.error('File upload error:', uploadError);\n              this.$toast.error(`خطأ في رفع ملف النشاط رقم ${index + 1}`);\n              hasErrors = true;\n              break;\n            }\n          }\n          activities.push({\n            owner_name: ownerName,\n            title: title,\n            short_description: shortDescription || '',\n            activity_date: activityDate,\n            state: state,\n            file_id: fileId,\n            activity_idea: activityIdea,\n            activity_goals: activityGoals,\n            target_groups: targetGroups,\n            audience_count: audienceCount,\n            activity_location: activityLocation,\n            activity_time: activityTime,\n            activity_duration: activityDuration,\n            activity_budget: activityBudget,\n            budget_details: budgetDetails,\n            total_budget: totalBudget,\n            activity_levels: activityLevels\n          });\n        }\n        if (hasErrors) return;\n\n        // Prepare submission data\n        const submissionData = {\n          coordinator_name: this.coordinatorName.trim(),\n          activities: activities\n        };\n\n        // Submit to backend\n        const response = await fetch('/api/v1/ndyt-activities/submissions', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`,\n            'x-team-pin': teamPin\n          },\n          body: JSON.stringify(submissionData)\n        });\n        if (response.ok) {\n          await response.json();\n          this.$toast.success('تم إرسال النشاطات بنجاح!');\n\n          // Clear the form\n          this.coordinatorName = '';\n\n          // Remove all activity items\n          activityItems.forEach(item => item.remove());\n\n          // Refresh my activities if they're currently shown\n          if (this.showMyActivities) {\n            this.fetchMyActivities();\n          }\n        } else {\n          let errorData = {};\n          try {\n            errorData = await response.json();\n          } catch (jsonError) {\n            console.error('Error parsing response JSON:', jsonError);\n          }\n          this.$toast.error(`فشل في إرسال النشاطات: ${errorData.message || 'خطأ غير معروف'}`);\n        }\n      } catch (error) {\n        console.error('Error submitting activities:', error);\n        this.$toast.error('حدث خطأ أثناء إرسال النشاطات. يرجى المحاولة مرة أخرى.');\n      }\n    },\n    async fetchMyActivities() {\n      this.loadingActivities = true;\n      this.myActivities = []; // Clear existing activities\n\n      try {\n        const response = await this.$api.get('/activities');\n        if (response.ok) {\n          const data = await response.json();\n\n          // The new endpoint returns activities directly with role-based filtering\n          if (data.activities && Array.isArray(data.activities)) {\n            this.myActivities = data.activities.map(activity => ({\n              ...activity,\n              submission_info: {\n                id: activity.submission_id,\n                governorate: activity.governorate,\n                coordinator_name: activity.coordinator_name,\n                created_at: activity.created_at\n              }\n            }));\n          }\n        } else if (response.status === 404) {\n          // No activities found - this is normal, not an error\n          this.myActivities = [];\n        } else {\n          const errorData = await response.json().catch(() => ({}));\n          console.error('Failed to fetch activities:', errorData);\n          this.$toast.error(`فشل في تحميل النشاطات: ${errorData.message || 'خطأ في الخادم'}`);\n        }\n      } catch (error) {\n        if (error.message === 'Session expired') {\n          // Token expiration is already handled by the API service\n          return;\n        }\n        console.error('Error fetching activities:', error);\n        if (error.name === 'TypeError' && error.message.includes('fetch')) {\n          this.$toast.error('خطأ في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.');\n        } else {\n          this.$toast.error('حدث خطأ أثناء تحميل النشاطات. يرجى المحاولة مرة أخرى.');\n        }\n      } finally {\n        this.loadingActivities = false;\n      }\n    },\n    toggleMyActivities() {\n      this.showMyActivities = !this.showMyActivities;\n      if (this.showMyActivities && this.myActivities.length === 0) {\n        this.fetchMyActivities();\n      }\n    },\n    editActivity(activity) {\n      this.showPinConfirmationModal('edit', activity, () => {\n        this.openEditModal(activity);\n      });\n    },\n    openEditModal(activity) {\n      this.editingActivity = {\n        ...activity\n      };\n      // Ensure date is properly formatted for date input (YYYY-MM-DD)\n      if (this.editingActivity.activity_date) {\n        const date = new Date(this.editingActivity.activity_date);\n        // Use timezone-safe formatting to avoid date shifting\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        this.editingActivity.activity_date = `${year}-${month}-${day}`;\n      }\n    },\n    async saveActivity() {\n      if (!this.editingActivity) return;\n      try {\n        const updateData = {\n          owner_name: this.editingActivity.owner_name,\n          title: this.editingActivity.title,\n          short_description: this.editingActivity.short_description,\n          activity_date: this.editingActivity.activity_date,\n          state: this.editingActivity.state\n        };\n        const response = await this.$api.put(`/activities/${this.editingActivity.id}`, updateData);\n        if (response.ok) {\n          const updatedActivity = await response.json();\n          // Update the activity in the list\n          const index = this.myActivities.findIndex(a => a.id === updatedActivity.id);\n          if (index !== -1) {\n            this.myActivities[index] = {\n              ...updatedActivity,\n              submission_info: this.myActivities[index].submission_info\n            };\n          }\n          this.editingActivity = null;\n          this.$toast.success('تم تحديث النشاط بنجاح!');\n        } else {\n          this.$toast.error('فشل في تحديث النشاط');\n        }\n      } catch (error) {\n        if (error.message === 'Session expired') {\n          // Token expiration is already handled by the API service\n          return;\n        }\n        console.error('Error updating activity:', error);\n        this.$toast.error('حدث خطأ أثناء تحديث النشاط');\n      }\n    },\n    cancelEdit() {\n      this.editingActivity = null;\n    },\n    deleteActivity(activityId) {\n      const activity = this.myActivities.find(a => a.id === activityId);\n      this.showPinConfirmationModal('delete', activity, async () => {\n        await this.performDeleteActivity(activityId);\n      });\n    },\n    async performDeleteActivity(activityId) {\n      try {\n        const token = localStorage.getItem('ndyt_token');\n        const teamPin = localStorage.getItem('ndyt_team_pin');\n        const response = await fetch(`/api/v1/ndyt-activities/activities/${activityId}`, {\n          method: 'DELETE',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'x-team-pin': teamPin\n          }\n        });\n        if (response.ok) {\n          this.myActivities = this.myActivities.filter(a => a.id !== activityId);\n          this.$toast.success('تم حذف النشاط بنجاح!');\n        } else {\n          this.$toast.error('فشل في حذف النشاط');\n        }\n      } catch (error) {\n        console.error('Error deleting activity:', error);\n        this.$toast.error('حدث خطأ أثناء حذف النشاط');\n      }\n    },\n    getStatusClass(status) {\n      const statusMap = {\n        'منفذ بصرف': 'status-executed-paid',\n        'منفذ بدون صرف': 'status-executed-unpaid',\n        'مقبول': 'status-accepted',\n        'مرفوض': 'status-rejected',\n        'يحتاج تعديل': 'status-needs-edit',\n        'صرف و لم ينفذ': 'status-paid-not-executed',\n        'مقبول دون صرف': 'status-accepted-unpaid',\n        'مرسل': 'status-sent'\n      };\n      return statusMap[status] || 'status-default';\n    },\n    AddActivityItem() {\n      const activityDiv = document.createElement('div');\n      activityDiv.className = 'activity-item';\n      const activityOwner = document.createElement('input');\n      activityOwner.type = 'text';\n      activityOwner.placeholder = 'اسم صاحب االنشاط';\n      activityOwner.className = 'activity-input';\n      activityOwner.required = true;\n      activityOwner.addEventListener('input', event => {\n        this.activityOwnerName = event.target.value;\n      });\n      activityDiv.appendChild(activityOwner);\n      const activityTitle = document.createElement('input');\n      activityTitle.type = 'text';\n      activityTitle.placeholder = 'عنوان النشاط';\n      activityTitle.className = 'activity-input';\n      activityTitle.required = true;\n      activityTitle.addEventListener('input', event => {\n        this.activityTitle = event.target.value;\n      });\n      activityDiv.appendChild(activityTitle);\n      const activityShortDescription = document.createElement('input');\n      activityShortDescription.type = 'text';\n      activityShortDescription.placeholder = 'وصف قصير للنشاط';\n      activityShortDescription.className = 'activity-input';\n      activityShortDescription.required = true;\n      activityShortDescription.addEventListener('input', event => {\n        this.activityDescription = event.target.value;\n      });\n      activityDiv.appendChild(activityShortDescription);\n      const activityDateLabel = document.createElement('label');\n      activityDateLabel.textContent = 'تاريخ النشاط';\n      activityDiv.appendChild(activityDateLabel);\n      const activityDate = document.createElement('input');\n      activityDate.type = 'date';\n      activityDate.className = 'activity-input';\n      activityDate.required = true;\n      activityDate.addEventListener('input', event => {\n        this.activityDateInput = event.target.value;\n      });\n      activityDiv.appendChild(activityDate);\n      const activityIdeaLabel = document.createElement('label');\n      activityIdeaLabel.textContent = 'فكرة النشاط';\n      activityDiv.appendChild(activityIdeaLabel);\n      const activityIdea = document.createElement('textarea');\n      activityIdea.placeholder = 'فكرة النشاط';\n      activityIdea.className = 'activity-input';\n      activityIdea.required = true;\n      activityIdea.addEventListener('input', event => {\n        this.activityIdeaInput = event.target.value;\n      });\n      activityDiv.appendChild(activityIdea);\n      const activityGoalsLabel = document.createElement('label');\n      activityGoalsLabel.textContent = 'أهداف النشاط';\n      activityDiv.appendChild(activityGoalsLabel);\n      const activityGoalsList = document.createElement('ul');\n      activityDiv.appendChild(activityGoalsList);\n      const AddActivityGoal = () => {\n        const goal = {\n          text: ''\n        };\n        const goalItem = document.createElement('li');\n        const goalInput = document.createElement('input');\n        goalInput.type = 'text';\n        goalInput.placeholder = 'هدف النشاط';\n        goalInput.className = 'activity-input';\n        goalInput.required = true;\n        goalInput.addEventListener('input', event => {\n          goal.text = event.target.value;\n        });\n        this.activityGoals.push(goal);\n        goalItem.appendChild(goalInput);\n        activityGoalsList.appendChild(goalItem);\n      };\n      const activityGoalsAddButton = document.createElement('button');\n      activityGoalsAddButton.textContent = 'إضافة هدف';\n      activityGoalsAddButton.onclick = AddActivityGoal;\n      activityDiv.appendChild(activityGoalsAddButton);\n      const activityTargetGroupLabel = document.createElement('label');\n      activityTargetGroupLabel.textContent = 'الفئة المستهدفة';\n      activityDiv.appendChild(activityTargetGroupLabel);\n      const activityTargetGroupList = document.createElement('ul');\n      activityDiv.appendChild(activityTargetGroupList);\n      const AddActivityTargetGroup = () => {\n        const targetGroup = {\n          text: ''\n        };\n        const targetGroupItem = document.createElement('li');\n        const targetGroupInput = document.createElement('input');\n        targetGroupInput.type = 'text';\n        targetGroupInput.placeholder = 'الفئة المستهدفة';\n        targetGroupInput.className = 'activity-input';\n        targetGroupInput.required = true;\n        targetGroupInput.addEventListener('input', event => {\n          targetGroup.text = event.target.value;\n        });\n        this.targetGroups.push(targetGroup);\n        targetGroupItem.appendChild(targetGroupInput);\n        activityTargetGroupList.appendChild(targetGroupItem);\n      };\n      const activityTargetGroupAddButton = document.createElement('button');\n      activityTargetGroupAddButton.textContent = 'إضافة فئة مستهدفة';\n      activityTargetGroupAddButton.onclick = AddActivityTargetGroup;\n      activityDiv.appendChild(activityTargetGroupAddButton);\n      const activityAudienceCountLabel = document.createElement('label');\n      activityAudienceCountLabel.textContent = 'عدد الجمهور المستهدف';\n      activityDiv.appendChild(activityAudienceCountLabel);\n      const activityAudienceCountDiv = document.createElement('div');\n      activityAudienceCountDiv.style = 'display: flex; flex-direction: row; align-items: center; gap: 30px;';\n      const activityAudienceCount = document.createElement('input');\n      activityAudienceCount.type = 'stat-number';\n      activityAudienceCount.className = 'activity-input';\n      activityAudienceCount.required = true;\n      activityAudienceCount.addEventListener('input', event => {\n        this.audienceCount = event.target.value;\n      });\n      activityAudienceCountDiv.appendChild(activityAudienceCount);\n      const activityAudienceCountCurrency = document.createElement('span');\n      activityAudienceCountCurrency.textContent = 'شخص';\n      activityAudienceCountDiv.appendChild(activityAudienceCountCurrency);\n      activityDiv.appendChild(activityAudienceCountDiv);\n      const activityLocationLabel = document.createElement('label');\n      activityLocationLabel.textContent = 'موقع النشاط';\n      activityDiv.appendChild(activityLocationLabel);\n      const activityLocation = document.createElement('input');\n      activityLocation.type = 'text';\n      activityLocation.placeholder = 'موقع النشاط';\n      activityLocation.className = 'activity-input';\n      activityLocation.required = true;\n      activityLocation.addEventListener('input', event => {\n        this.activityLocationInput = event.target.value;\n      });\n      activityDiv.appendChild(activityLocation);\n      const activityTimeLabel = document.createElement('label');\n      activityTimeLabel.textContent = 'وقت النشاط';\n      activityDiv.appendChild(activityTimeLabel);\n      const activityTime = document.createElement('input');\n      activityTime.type = 'time';\n      activityTime.className = 'activity-input';\n      activityTime.required = true;\n      activityTime.addEventListener('input', event => {\n        this.activityTimeInput = event.target.value;\n      });\n      activityDiv.appendChild(activityTime);\n      const activityDurationLabel = document.createElement('label');\n      activityDurationLabel.textContent = 'مدة النشاط';\n      activityDiv.appendChild(activityDurationLabel);\n      const activityDuration = document.createElement('input');\n      activityDuration.type = 'text';\n      activityDuration.placeholder = 'مدة النشاط';\n      activityDuration.className = 'activity-input';\n      activityDuration.required = true;\n      activityDuration.addEventListener('input', event => {\n        this.activityDurationInput = event.target.value;\n      });\n      activityDiv.appendChild(activityDuration);\n      const activityBudgetLabel = document.createElement('label');\n      activityBudgetLabel.textContent = 'ميزانية النشاط';\n      activityDiv.appendChild(activityBudgetLabel);\n      const BudgetDiv = document.createElement('div');\n      BudgetDiv.className = 'budget-input';\n      BudgetDiv.style.display = 'flex';\n      BudgetDiv.style.flexDirection = 'row';\n      BudgetDiv.style.gap = '40px';\n      BudgetDiv.style.alignItems = 'center';\n      BudgetDiv.style.direction = 'rtl';\n      const BudgetInput = document.createElement('input');\n      BudgetInput.type = 'stat-number';\n      BudgetInput.placeholder = 'أدخل الميزانية';\n      BudgetInput.required = true;\n      BudgetInput.addEventListener('input', event => {\n        this.activityBudgetInput = event.target.value;\n      });\n      BudgetDiv.appendChild(BudgetInput);\n      const currencyTypeLabel = document.createElement('label');\n      currencyTypeLabel.textContent = 'د. ع';\n      BudgetDiv.innerHTML += currencyTypeLabel.outerHTML;\n      activityDiv.appendChild(BudgetDiv);\n      const activityBudgetDetailsLabel = document.createElement('label');\n      activityBudgetDetailsLabel.textContent = 'تفاصيل الصرف';\n      activityDiv.appendChild(activityBudgetDetailsLabel);\n      const activityBudgetDetailsList = document.createElement('ul');\n      activityDiv.appendChild(activityBudgetDetailsList);\n      const AddActivityBudgetDetailsRecord = () => {\n        const material = {\n          name: '',\n          type: '',\n          unit: '',\n          amount: 0,\n          price: 0,\n          budgetPrice: 0\n        };\n        const budgetDetailItem = document.createElement('div');\n        budgetDetailItem.className = 'budget-detail-item';\n        budgetDetailItem.style = 'display: flex; flex-direction: column; gap: 20px;' + 'align-items: start; margin-bottom: 10px; justify-items: center;' + 'background-color: transparent; padding: 5px 25px; border-radius: 5px;' + 'border: 1px solid gray;';\n        const materialNameRow = document.createElement('div');\n        materialNameRow.style.display = 'flex';\n        materialNameRow.flexDirection = 'row';\n        materialNameRow.style.gap = '80px';\n        const materialNameLabel = document.createElement('label');\n        materialNameLabel.textContent = 'اسم المادة';\n        materialNameRow.appendChild(materialNameLabel);\n        const materialNameInput = document.createElement('input');\n        materialNameInput.type = 'text';\n        materialNameInput.placeholder = 'أدخل اسم المادة';\n        materialNameInput.required = true;\n        materialNameInput.addEventListener('input', event => {\n          material.name = event.target.value;\n        });\n        materialNameRow.appendChild(materialNameInput);\n        budgetDetailItem.appendChild(materialNameRow);\n        const materialTypeRow = document.createElement('div');\n        materialTypeRow.style.display = 'flex';\n        materialTypeRow.style.flexDirection = 'row';\n        materialTypeRow.style.gap = '80px';\n        const materialTypeLabel = document.createElement('label');\n        materialTypeLabel.textContent = 'نوع المادة';\n        const materialTypeInput = document.createElement('input');\n        materialTypeInput.type = 'text';\n        materialTypeInput.placeholder = 'أدخل نوع المادة';\n        materialTypeInput.required = true;\n        materialTypeInput.addEventListener('input', event => {\n          material.type = event.target.value;\n        });\n        materialTypeRow.appendChild(materialTypeLabel);\n        materialTypeRow.appendChild(materialTypeInput);\n        budgetDetailItem.appendChild(materialTypeRow);\n        const materialAmountRow = document.createElement('div');\n        materialAmountRow.style.display = 'flex';\n        materialAmountRow.style.flexDirection = 'row';\n        materialAmountRow.style.gap = '80px';\n        const materialAmountLabel = document.createElement('label');\n        materialAmountLabel.textContent = 'عدد الكمية';\n        materialAmountRow.appendChild(materialAmountLabel);\n        const materialAmountInput = document.createElement('input');\n        materialAmountInput.type = 'text';\n        materialAmountInput.placeholder = 'أدخل عدد الكمية';\n        materialAmountInput.required = true;\n        materialAmountInput.addEventListener('input', event => {\n          material.amount = event.target.value;\n        });\n        materialAmountRow.appendChild(materialAmountInput);\n        budgetDetailItem.appendChild(materialAmountRow);\n        const materialPriceRow = document.createElement('div');\n        materialPriceRow.style.display = 'flex';\n        materialPriceRow.style.flexDirection = 'row';\n        materialPriceRow.style.gap = '80px';\n        const materialPriceLabel = document.createElement('label');\n        materialPriceLabel.textContent = 'سعر الوحدة';\n        materialPriceRow.appendChild(materialPriceLabel);\n        const materialPriceInput = document.createElement('input');\n        materialPriceInput.type = 'text';\n        materialPriceInput.placeholder = 'أدخل سعر المادة';\n        materialPriceInput.required = true;\n        materialPriceInput.addEventListener('input', event => {\n          material.price = event.target.value;\n        });\n        materialPriceRow.appendChild(materialPriceInput);\n        budgetDetailItem.appendChild(materialPriceRow);\n        const materialBudgetPriceRow = document.createElement('div');\n        materialBudgetPriceRow.style.display = 'flex';\n        materialBudgetPriceRow.style.flexDirection = 'row';\n        materialBudgetPriceRow.style.gap = '80px';\n        const materialBudgetPriceLabel = document.createElement('label');\n        materialBudgetPriceLabel.textContent = 'سعر الصرف';\n        materialBudgetPriceRow.appendChild(materialBudgetPriceLabel);\n        const materialBudgetPriceInput = document.createElement('input');\n        materialBudgetPriceInput.type = 'text';\n        materialBudgetPriceInput.placeholder = 'أدخل سعر الصرف';\n        materialBudgetPriceInput.required = true;\n        materialBudgetPriceInput.addEventListener('input', event => {\n          material.budgetPrice = event.target.value;\n        });\n        this.budgetDetailsList.push(material);\n        materialBudgetPriceRow.appendChild(materialBudgetPriceInput);\n        budgetDetailItem.appendChild(materialBudgetPriceRow);\n        activityBudgetDetailsList.appendChild(budgetDetailItem);\n      };\n      const AddActivityBudgetDetailsRecordButton = document.createElement('button');\n      AddActivityBudgetDetailsRecordButton.textContent = 'إضافة صرف';\n      AddActivityBudgetDetailsRecordButton.onclick = AddActivityBudgetDetailsRecord;\n      activityDiv.appendChild(AddActivityBudgetDetailsRecordButton);\n      const activityTotalBudgetLabel = document.createElement('label');\n      activityTotalBudgetLabel.textContent = 'مجموع الصرف الكلي';\n      activityDiv.appendChild(activityTotalBudgetLabel);\n      const totalBudgetDiv = document.createElement('div');\n      totalBudgetDiv.className = 'budget-input';\n      totalBudgetDiv.style = 'display: flex; flex-direction: row; gap: 40px; align-items: center;';\n      const totalBudgetInput = document.createElement('input');\n      totalBudgetInput.type = 'stat-number';\n      totalBudgetInput.placeholder = 'أدخل المجموع الكلي';\n      totalBudgetInput.required = true;\n      totalBudgetInput.addEventListener('input', event => {\n        this.activityTotalBudget = event.target.value;\n      });\n      totalBudgetInput.className = 'budget-input';\n      totalBudgetDiv.appendChild(totalBudgetInput);\n      totalBudgetDiv.appendChild(currencyTypeLabel);\n      activityDiv.appendChild(totalBudgetDiv);\n      const activityAppliedLevelsLabel = document.createElement('label');\n      activityAppliedLevelsLabel.textContent = 'مراحل تنفيذ النشاط';\n      activityDiv.appendChild(activityAppliedLevelsLabel);\n      const activityAppliedLevelsList = document.createElement('ul');\n      activityDiv.appendChild(activityAppliedLevelsList);\n      const AddActivityAppliedLevel = () => {\n        const appliedLevel = {\n          description: ''\n        };\n        const appliedLevelItem = document.createElement('li');\n        const appliedLevelInput = document.createElement('input');\n        appliedLevelInput.type = 'text';\n        appliedLevelInput.placeholder = 'وصف المرحلة التنفيذية';\n        appliedLevelInput.className = 'activity-input';\n        appliedLevelInput.required = true;\n        appliedLevelInput.addEventListener('input', event => {\n          appliedLevel.description = event.target.value;\n        });\n        this.activityLevels.push(appliedLevel);\n        appliedLevelItem.appendChild(appliedLevelInput);\n        activityAppliedLevelsList.appendChild(appliedLevelItem);\n      };\n      const activityAppliedLevelAddButton = document.createElement('button');\n      activityAppliedLevelAddButton.textContent = 'إضافة مرحلة تنفيذ';\n      activityAppliedLevelAddButton.onclick = AddActivityAppliedLevel;\n      activityDiv.appendChild(activityAppliedLevelAddButton);\n      const activityStateLabel = document.createElement('label');\n      activityStateLabel.textContent = 'حالة النشاط';\n      activityDiv.appendChild(activityStateLabel);\n      const activityApplyState = document.createElement('select');\n      activityApplyState.className = 'activity-input';\n      activityApplyState.required = true;\n\n      // Add default option\n      const defaultOption = document.createElement('option');\n      defaultOption.value = '';\n      defaultOption.textContent = 'اختر حالة النشاط';\n      defaultOption.disabled = true;\n      defaultOption.selected = true;\n      activityApplyState.appendChild(defaultOption);\n      const states = ['منفذ بصرف', 'منفذ بدون صرف', 'مقبول', 'مرفوض', 'يحتاج تعديل', 'صرف و لم ينفذ', 'مقبول دون صرف', 'مرسل'];\n      states.forEach(state => {\n        const option = document.createElement('option');\n        option.value = state;\n        option.textContent = state;\n        activityApplyState.appendChild(option);\n      });\n      activityApplyState.addEventListener('change', event => {\n        this.activityStatus = event.target.value;\n        // Update the filledActivity object when status changes\n        this.filledActivity = {\n          title: this.activityTitleName,\n          owner: this.activityOwnerName,\n          description: this.activityDescription,\n          date: this.activityDateInput,\n          idea: this.activityIdeaInput,\n          goals: this.activityGoals,\n          audience: this.targetGroups,\n          status: this.activityStatus,\n          governorate: this.activityGovernorate,\n          coordinator: this.coordinatorName\n        };\n      });\n      const activityDeleteButton = document.createElement('button');\n      activityDeleteButton.className = 'activity-delete-button';\n      activityDeleteButton.textContent = 'حذف النشاط';\n      activityDeleteButton.onclick = () => {\n        activityDiv.remove();\n      };\n      activityDiv.appendChild(activityApplyState);\n      activityDiv.appendChild(activityDeleteButton);\n      this.activities.push(activityDiv);\n      document.querySelector('.activities-list').appendChild(activityDiv);\n    },\n    handleClickOutside(event) {\n      const userSection = event.target.closest('.user-section');\n      if (!userSection) {\n        this.showUserMenu = false;\n      }\n    },\n    setCurrentView(view) {\n      this.currentView = view;\n      // Automatically fetch activities when switching to view tab\n      if (view === 'view') {\n        this.fetchMyActivities();\n      }\n    },\n    refreshActivities() {\n      this.fetchMyActivities();\n    },\n    async loadCoordinatorName() {\n      try {\n        const token = localStorage.getItem('ndyt_token');\n        if (!token) return;\n        const response = await fetch('/api/v1/ndyt-activities/coordinator', {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (response.ok) {\n          const data = await response.json();\n          this.coordinatorName = data.coordinator_name || '';\n        } else if (response.status === 404) {\n          // No coordinator found for user's governorate\n          this.coordinatorName = '';\n        }\n      } catch (error) {\n        console.error('Error loading coordinator name:', error);\n        // Don't show alert for this error as it's not critical\n      }\n    },\n    exportToCSV() {\n      if (this.filteredActivities.length === 0) {\n        this.$toast.error('لا توجد نشاطات للتصدير');\n        return;\n      }\n\n      // Define CSV headers in Arabic\n      const headers = ['عنوان النشاط', 'صاحب النشاط', 'وصف النشاط', 'تاريخ النشاط', 'حالة النشاط', 'المحافظة', 'منسق المحافظة', 'تاريخ الإرسال'];\n\n      // Convert activities data to CSV format\n      const csvData = this.filteredActivities.map(activity => {\n        return [`\"${activity.title || ''}\"`, `\"${activity.owner_name || ''}\"`, `\"${activity.short_description || ''}\"`, activity.activity_date ? new Date(activity.activity_date).toLocaleDateString('ar-EG') : '', `\"${activity.state || ''}\"`, `\"${activity.submission_info?.governorate || ''}\"`, `\"${activity.submission_info?.coordinator_name || ''}\"`, activity.submission_info?.created_at ? new Date(activity.submission_info.created_at).toLocaleDateString('ar-EG') : ''].join(',');\n      });\n\n      // Combine headers and data\n      const csvContent = [headers.join(','), ...csvData].join('\\n');\n\n      // Add BOM for proper Arabic text encoding in Excel\n      const BOM = '\\uFEFF';\n      const csvWithBOM = BOM + csvContent;\n\n      // Create and download the file\n      const blob = new Blob([csvWithBOM], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      if (link.download !== undefined) {\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n\n        // Generate filename with current date and filter info\n        const currentDate = new Date().toISOString().split('T')[0];\n        const filterText = this.selectedFilter ? `_${this.selectedFilter}` : '_جميع_النشاطات';\n        const filename = `نشاطات_الفريق_الوطني${filterText}_${currentDate}.csv`;\n        link.setAttribute('download', filename);\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        this.$toast.success(`تم تصدير ${this.filteredActivities.length} نشاط بنجاح!`);\n      } else {\n        this.$toast.error('المتصفح لا يدعم تحميل الملفات');\n      }\n    },\n    // PIN Confirmation Methods\n    showPinConfirmationModal(action, activity, callback) {\n      this.pinConfirmationData = {\n        pin: '',\n        action: action,\n        activity: activity,\n        callback: callback\n      };\n      this.showPinConfirmation = true;\n      // Focus on PIN input after modal opens\n      this.$nextTick(() => {\n        const pinInput = document.getElementById('confirmPin');\n        if (pinInput) {\n          pinInput.focus();\n        }\n      });\n    },\n    closePinConfirmation() {\n      this.showPinConfirmation = false;\n      this.pinConfirmationData = {\n        pin: '',\n        action: '',\n        activity: null,\n        callback: null\n      };\n    },\n    async confirmPinAction() {\n      const enteredPin = this.pinConfirmationData.pin;\n      const storedPin = localStorage.getItem('ndyt_team_pin');\n      if (enteredPin !== storedPin) {\n        this.$toast.error('رمز الفريق الرقمي غير صحيح');\n        return;\n      }\n\n      // Store callback before closing modal\n      const callback = this.pinConfirmationData.callback;\n\n      // Close modal first\n      this.closePinConfirmation();\n\n      // Execute the callback function after DOM update\n      if (callback) {\n        this.$nextTick(async () => {\n          await callback();\n        });\n      }\n    },\n    downloadFile(file) {\n      try {\n        // Create a direct download link\n        const fileUrl = `/api/v1/ndyt-activities${file.file_url}`;\n\n        // Create a temporary anchor element to trigger download\n        const link = document.createElement('a');\n        link.href = fileUrl;\n        link.download = file.file_name;\n        link.target = '_blank'; // Open in new tab as fallback\n        link.style.display = 'none';\n\n        // Append to body, click, and remove\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        this.$toast.success(`تم تحميل الملف: ${file.file_name}`);\n      } catch (error) {\n        console.error('Download error:', error);\n        this.$toast.error(`فشل في تحميل الملف: ${file.file_name}`);\n      }\n    },\n    getShortFileName(fileName) {\n      // Truncate long file names for display\n      if (fileName.length > 20) {\n        const extension = fileName.split('.').pop();\n        const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));\n        return nameWithoutExt.substring(0, 15) + '...' + '.' + extension;\n      }\n      return fileName;\n    },\n    openActivityModal(activity) {\n      this.selectedActivity = activity;\n      this.showActivityModal = true;\n      // Prevent body scrolling when modal is open\n      document.body.style.overflow = 'hidden';\n    },\n    closeActivityModal() {\n      this.showActivityModal = false;\n      this.selectedActivity = null;\n      // Restore body scrolling when modal is closed\n      document.body.style.overflow = 'auto';\n    },\n    formatDate(dateString) {\n      if (!dateString) return 'غير محدد';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('ar-EG', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    },\n    formatTime(timeString) {\n      if (!timeString) return 'غير محدد';\n      return timeString;\n    },\n    formatCurrency(amount) {\n      if (!amount) return 'غير محدد';\n      return new Intl.NumberFormat('ar-EG').format(amount) + ' د.ع';\n    },\n    async loadImageAsArrayBuffer(imagePath) {\n      try {\n        // Import the image as a module to get the correct URL\n        let imageUrl;\n        if (imagePath.includes('scy_logo')) {\n          imageUrl = (await import('@/assets/scy_logo.jpg')).default;\n        } else if (imagePath.includes('ndyt_logo')) {\n          imageUrl = (await import('@/assets/ndyt_logo.jpg')).default;\n        }\n        if (!imageUrl) throw new Error(`Could not resolve image: ${imagePath}`);\n        const response = await fetch(imageUrl);\n        if (!response.ok) throw new Error(`Failed to load image: ${imageUrl}`);\n        return await response.arrayBuffer();\n      } catch (error) {\n        console.error('Error loading image:', error);\n        return null;\n      }\n    },\n    async exportToDocx() {\n      if (!this.selectedActivity) return;\n      const activity = this.selectedActivity;\n\n      // Load logo images\n      const scyLogoBuffer = await this.loadImageAsArrayBuffer('scy_logo');\n      const ndytLogoBuffer = await this.loadImageAsArrayBuffer('ndyt_logo');\n\n      // Create document sections\n      const sections = [];\n\n      // Document header with logos and organization names\n      const headerParagraphs = [];\n\n      // Header with logos and organization names in one line\n      if (scyLogoBuffer && ndytLogoBuffer) {\n        // Create a table for proper logo and text alignment\n        const headerTable = new Table({\n          width: {\n            size: 100,\n            type: WidthType.PERCENTAGE\n          },\n          borders: {\n            top: {\n              style: BorderStyle.NONE\n            },\n            bottom: {\n              style: BorderStyle.NONE\n            },\n            left: {\n              style: BorderStyle.NONE\n            },\n            right: {\n              style: BorderStyle.NONE\n            },\n            insideHorizontal: {\n              style: BorderStyle.NONE\n            },\n            insideVertical: {\n              style: BorderStyle.NONE\n            }\n          },\n          rows: [new TableRow({\n            children: [\n            // SCY Logo\n            new TableCell({\n              children: [new Paragraph({\n                children: [new ImageRun({\n                  data: scyLogoBuffer,\n                  transformation: {\n                    width: 80,\n                    height: 80\n                  }\n                })],\n                alignment: AlignmentType.CENTER\n              })],\n              width: {\n                size: 20,\n                type: WidthType.PERCENTAGE\n              }\n            }),\n            // Organization Names\n            new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: \"المجلس الأعلى للشباب\",\n                  bold: true,\n                  size: 32,\n                  font: \"Arial\"\n                })],\n                alignment: AlignmentType.CENTER,\n                spacing: {\n                  after: 200\n                },\n                bidirectional: true\n              }), new Paragraph({\n                children: [new TextRun({\n                  text: \"الفريق الوطني للشباب الرقمي\",\n                  bold: true,\n                  size: 28,\n                  font: \"Arial\"\n                })],\n                alignment: AlignmentType.CENTER,\n                spacing: {\n                  after: 200\n                },\n                bidirectional: true\n              })],\n              width: {\n                size: 60,\n                type: WidthType.PERCENTAGE\n              }\n            }),\n            // NDYT Logo\n            new TableCell({\n              children: [new Paragraph({\n                children: [new ImageRun({\n                  data: ndytLogoBuffer,\n                  transformation: {\n                    width: 80,\n                    height: 80\n                  }\n                })],\n                alignment: AlignmentType.CENTER\n              })],\n              width: {\n                size: 20,\n                type: WidthType.PERCENTAGE\n              }\n            })]\n          })]\n        });\n        headerParagraphs.push(headerTable);\n      } else {\n        // Fallback if images don't load\n        headerParagraphs.push(new Paragraph({\n          children: [new TextRun({\n            text: \"المجلس الأعلى للشباب\",\n            bold: true,\n            size: 32,\n            font: \"Arial\"\n          })],\n          alignment: AlignmentType.CENTER,\n          spacing: {\n            after: 200\n          },\n          bidirectional: true\n        }), new Paragraph({\n          children: [new TextRun({\n            text: \"الفريق الوطني للشباب الرقمي\",\n            bold: true,\n            size: 28,\n            font: \"Arial\"\n          })],\n          alignment: AlignmentType.CENTER,\n          spacing: {\n            after: 400\n          },\n          bidirectional: true\n        }));\n      }\n\n      // Activity title\n      headerParagraphs.push(new Paragraph({\n        children: [new TextRun({\n          text: activity.title,\n          bold: true,\n          size: 36,\n          underline: {},\n          font: \"Arial\"\n        })],\n        alignment: AlignmentType.CENTER,\n        spacing: {\n          before: 400,\n          after: 600\n        },\n        bidirectional: true\n      }));\n\n      // Activity details table\n      const detailsTable = new Table({\n        width: {\n          size: 100,\n          type: WidthType.PERCENTAGE\n        },\n        borders: {\n          top: {\n            style: BorderStyle.SINGLE,\n            size: 4,\n            color: \"000000\"\n          },\n          bottom: {\n            style: BorderStyle.SINGLE,\n            size: 4,\n            color: \"000000\"\n          },\n          left: {\n            style: BorderStyle.SINGLE,\n            size: 4,\n            color: \"000000\"\n          },\n          right: {\n            style: BorderStyle.SINGLE,\n            size: 4,\n            color: \"000000\"\n          },\n          insideHorizontal: {\n            style: BorderStyle.SINGLE,\n            size: 2,\n            color: \"666666\"\n          },\n          insideVertical: {\n            style: BorderStyle.SINGLE,\n            size: 2,\n            color: \"666666\"\n          }\n        },\n        rows: [\n        // Basic information rows\n        this.createTableRow(\"الاسم\", `الفريق الوطني للشباب الرقمي - ${activity.governorate} - ${activity.owner_name}`), this.createTableRow(\"اسم النشاط\", activity.title), ...(activity.activity_idea ? [this.createTableRow(\"فكرة النشاط\", activity.activity_idea)] : []), ...(activity.activity_goals && activity.activity_goals.length > 0 ? [this.createTableRow(\"أهداف النشاط\", activity.activity_goals.map(g => `• ${g.text}`).join('\\n'))] : []), ...(activity.target_groups && activity.target_groups.length > 0 ? [this.createTableRow(\"الفئة المستهدفة\", activity.target_groups.map(g => g.text).join(', '))] : []), ...(activity.audience_count ? [this.createTableRow(\"عدد المستهدفين\", `${activity.audience_count} شخص`)] : []), this.createTableRow(\"تاريخ النشاط\", this.formatDate(activity.activity_date)), ...(activity.activity_time ? [this.createTableRow(\"وقت النشاط\", this.formatTime(activity.activity_time))] : []), ...(activity.activity_location ? [this.createTableRow(\"مكان النشاط\", activity.activity_location)] : []), ...(activity.activity_duration ? [this.createTableRow(\"مدة النشاط\", activity.activity_duration)] : []), ...(activity.activity_budget ? [this.createTableRow(\"ميزانية النشاط\", this.formatCurrency(activity.activity_budget))] : []), ...(activity.activity_levels && activity.activity_levels.length > 0 ? [this.createTableRow(\"مراحل تنفيذ النشاط\", activity.activity_levels.map(l => l.description).join('\\n'))] : []), this.createTableRow(\"حالة النشاط\", activity.state), this.createTableRow(\"المحافظة\", activity.governorate), this.createTableRow(\"منسق المحافظة\", activity.coordinator_name), this.createTableRow(\"تاريخ الإرسال\", this.formatDate(activity.created_at))]\n      });\n      sections.push(...headerParagraphs, detailsTable);\n\n      // Budget details table if exists\n      if (activity.budget_details && activity.budget_details.length > 0) {\n        sections.push(new Paragraph({\n          children: [new TextRun({\n            text: \"تفاصيل الصرف\",\n            bold: true,\n            size: 28,\n            font: \"Arial\"\n          })],\n          spacing: {\n            before: 400,\n            after: 200\n          },\n          bidirectional: true,\n          alignment: AlignmentType.CENTER\n        }));\n        const budgetTable = new Table({\n          width: {\n            size: 100,\n            type: WidthType.PERCENTAGE\n          },\n          borders: {\n            top: {\n              style: BorderStyle.SINGLE,\n              size: 4,\n              color: \"000000\"\n            },\n            bottom: {\n              style: BorderStyle.SINGLE,\n              size: 4,\n              color: \"000000\"\n            },\n            left: {\n              style: BorderStyle.SINGLE,\n              size: 4,\n              color: \"000000\"\n            },\n            right: {\n              style: BorderStyle.SINGLE,\n              size: 4,\n              color: \"000000\"\n            },\n            insideHorizontal: {\n              style: BorderStyle.SINGLE,\n              size: 2,\n              color: \"666666\"\n            },\n            insideVertical: {\n              style: BorderStyle.SINGLE,\n              size: 2,\n              color: \"666666\"\n            }\n          },\n          rows: [\n          // Header row\n          new TableRow({\n            children: [new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: \"المادة\",\n                  bold: true,\n                  font: \"Arial\",\n                  size: 24,\n                  color: \"FFFFFF\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              width: {\n                size: 20,\n                type: WidthType.PERCENTAGE\n              },\n              shading: {\n                fill: \"4472C4\" // Blue header background\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                }\n              },\n              margins: {\n                top: 100,\n                bottom: 100,\n                left: 100,\n                right: 100\n              }\n            }), new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: \"النوع\",\n                  bold: true,\n                  font: \"Arial\",\n                  size: 24,\n                  color: \"FFFFFF\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              width: {\n                size: 20,\n                type: WidthType.PERCENTAGE\n              },\n              shading: {\n                fill: \"4472C4\"\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                }\n              },\n              margins: {\n                top: 100,\n                bottom: 100,\n                left: 100,\n                right: 100\n              }\n            }), new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: \"العدد\",\n                  bold: true,\n                  font: \"Arial\",\n                  size: 24,\n                  color: \"FFFFFF\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              width: {\n                size: 20,\n                type: WidthType.PERCENTAGE\n              },\n              shading: {\n                fill: \"4472C4\"\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                }\n              },\n              margins: {\n                top: 100,\n                bottom: 100,\n                left: 100,\n                right: 100\n              }\n            }), new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: \"السعر\",\n                  bold: true,\n                  font: \"Arial\",\n                  size: 24,\n                  color: \"FFFFFF\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              width: {\n                size: 20,\n                type: WidthType.PERCENTAGE\n              },\n              shading: {\n                fill: \"4472C4\"\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                }\n              },\n              margins: {\n                top: 100,\n                bottom: 100,\n                left: 100,\n                right: 100\n              }\n            }), new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: \"المجموع\",\n                  bold: true,\n                  font: \"Arial\",\n                  size: 24,\n                  color: \"FFFFFF\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              width: {\n                size: 20,\n                type: WidthType.PERCENTAGE\n              },\n              shading: {\n                fill: \"4472C4\"\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 2,\n                  color: \"000000\"\n                }\n              },\n              margins: {\n                top: 100,\n                bottom: 100,\n                left: 100,\n                right: 100\n              }\n            })]\n          }),\n          // Data rows\n          ...activity.budget_details.map((detail, index) => new TableRow({\n            children: [new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: detail.name || '',\n                  font: \"Arial\",\n                  size: 22,\n                  color: \"000000\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              shading: {\n                fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\" // Alternating row colors\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                }\n              },\n              margins: {\n                top: 80,\n                bottom: 80,\n                left: 100,\n                right: 100\n              }\n            }), new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: detail.type || '',\n                  font: \"Arial\",\n                  size: 22,\n                  color: \"000000\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              shading: {\n                fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\"\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                }\n              },\n              margins: {\n                top: 80,\n                bottom: 80,\n                left: 100,\n                right: 100\n              }\n            }), new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: detail.amount?.toString() || '0',\n                  font: \"Arial\",\n                  size: 22,\n                  color: \"000000\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              shading: {\n                fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\"\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                }\n              },\n              margins: {\n                top: 80,\n                bottom: 80,\n                left: 100,\n                right: 100\n              }\n            }), new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: this.formatCurrency(detail.price),\n                  font: \"Arial\",\n                  size: 22,\n                  color: \"000000\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              shading: {\n                fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\"\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                }\n              },\n              margins: {\n                top: 80,\n                bottom: 80,\n                left: 100,\n                right: 100\n              }\n            }), new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: this.formatCurrency(detail.budgetPrice),\n                  font: \"Arial\",\n                  size: 22,\n                  color: \"000000\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              shading: {\n                fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\"\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 1,\n                  color: \"666666\"\n                }\n              },\n              margins: {\n                top: 80,\n                bottom: 80,\n                left: 100,\n                right: 100\n              }\n            })]\n          })),\n          // Total row if total budget exists\n          ...(activity.total_budget ? [new TableRow({\n            children: [new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: \"الإجمالي\",\n                  bold: true,\n                  font: \"Arial\",\n                  size: 24,\n                  color: \"FFFFFF\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              columnSpan: 4,\n              shading: {\n                fill: \"2F5597\" // Dark blue for total row\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 3,\n                  color: \"000000\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 3,\n                  color: \"000000\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 3,\n                  color: \"000000\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 3,\n                  color: \"000000\"\n                }\n              },\n              margins: {\n                top: 100,\n                bottom: 100,\n                left: 100,\n                right: 100\n              }\n            }), new TableCell({\n              children: [new Paragraph({\n                children: [new TextRun({\n                  text: this.formatCurrency(activity.total_budget),\n                  bold: true,\n                  font: \"Arial\",\n                  size: 24,\n                  color: \"FFFFFF\"\n                })],\n                bidirectional: true,\n                alignment: AlignmentType.CENTER\n              })],\n              shading: {\n                fill: \"2F5597\" // Dark blue for total row\n              },\n              borders: {\n                top: {\n                  style: BorderStyle.SINGLE,\n                  size: 3,\n                  color: \"000000\"\n                },\n                bottom: {\n                  style: BorderStyle.SINGLE,\n                  size: 3,\n                  color: \"000000\"\n                },\n                left: {\n                  style: BorderStyle.SINGLE,\n                  size: 3,\n                  color: \"000000\"\n                },\n                right: {\n                  style: BorderStyle.SINGLE,\n                  size: 3,\n                  color: \"000000\"\n                }\n              },\n              margins: {\n                top: 100,\n                bottom: 100,\n                left: 100,\n                right: 100\n              }\n            })]\n          })] : [])]\n        });\n        sections.push(budgetTable);\n      }\n\n      // Create document with RTL support\n      const doc = new Document({\n        sections: [{\n          children: sections,\n          properties: {\n            page: {\n              margin: {\n                top: 1440,\n                // 1 inch\n                right: 1440,\n                // 1 inch\n                bottom: 1440,\n                // 1 inch\n                left: 1440 // 1 inch\n              }\n            }\n          }\n        }],\n        styles: {\n          default: {\n            document: {\n              run: {\n                font: \"Arial\",\n                size: 24\n              },\n              paragraph: {\n                spacing: {\n                  line: 276\n                }\n              }\n            }\n          }\n        }\n      });\n\n      // Generate and save file\n      try {\n        const blob = await Packer.toBlob(doc);\n        const fileName = `نشاط_${activity.title.replace(/[^\\w\\s]/gi, '')}_${new Date().toISOString().split('T')[0]}.docx`;\n        saveAs(blob, fileName);\n        this.$toast.success('تم تصدير الملف بنجاح');\n      } catch (error) {\n        console.error('Error exporting DOCX:', error);\n        this.$toast.error('حدث خطأ أثناء تصدير الملف');\n      }\n    },\n    createTableRow(label, value) {\n      return new TableRow({\n        children: [\n        // Value cell first (right side in RTL)\n        new TableCell({\n          children: [new Paragraph({\n            children: [new TextRun({\n              text: value || 'غير محدد',\n              font: \"Arial\",\n              size: 24,\n              color: \"000000\"\n            })],\n            bidirectional: true,\n            alignment: AlignmentType.RIGHT\n          })],\n          width: {\n            size: 70,\n            type: WidthType.PERCENTAGE\n          },\n          shading: {\n            fill: \"FFFFFF\" // White background for values\n          },\n          borders: {\n            top: {\n              style: BorderStyle.SINGLE,\n              size: 2,\n              color: \"666666\"\n            },\n            bottom: {\n              style: BorderStyle.SINGLE,\n              size: 2,\n              color: \"666666\"\n            },\n            left: {\n              style: BorderStyle.SINGLE,\n              size: 2,\n              color: \"666666\"\n            },\n            right: {\n              style: BorderStyle.SINGLE,\n              size: 2,\n              color: \"666666\"\n            }\n          },\n          margins: {\n            top: 100,\n            bottom: 100,\n            left: 150,\n            right: 150\n          }\n        }),\n        // Label cell second (left side in RTL)\n        new TableCell({\n          children: [new Paragraph({\n            children: [new TextRun({\n              text: label,\n              bold: true,\n              font: \"Arial\",\n              size: 24,\n              color: \"000000\"\n            })],\n            bidirectional: true,\n            alignment: AlignmentType.RIGHT\n          })],\n          width: {\n            size: 30,\n            type: WidthType.PERCENTAGE\n          },\n          shading: {\n            fill: \"E6E6FA\" // Light lavender background for labels\n          },\n          borders: {\n            top: {\n              style: BorderStyle.SINGLE,\n              size: 2,\n              color: \"666666\"\n            },\n            bottom: {\n              style: BorderStyle.SINGLE,\n              size: 2,\n              color: \"666666\"\n            },\n            left: {\n              style: BorderStyle.SINGLE,\n              size: 2,\n              color: \"666666\"\n            },\n            right: {\n              style: BorderStyle.SINGLE,\n              size: 2,\n              color: \"666666\"\n            }\n          },\n          margins: {\n            top: 100,\n            bottom: 100,\n            left: 150,\n            right: 150\n          }\n        })]\n      });\n    },\n    async printActivity() {\n      if (!this.selectedActivity) return;\n      try {\n        // Get the image URLs from the current page\n        const scyLogoImg = document.querySelector('img[src*=\"scy_logo\"]');\n        const ndytLogoImg = document.querySelector('img[src*=\"ndyt_logo\"]');\n        const scyLogoSrc = scyLogoImg ? scyLogoImg.src : '';\n        const ndytLogoSrc = ndytLogoImg ? ndytLogoImg.src : '';\n\n        // Create a new window for printing\n        const printWindow = window.open('', '_blank');\n\n        // Generate the print content with correct image URLs\n        const printContent = this.generatePrintContent(this.selectedActivity, scyLogoSrc, ndytLogoSrc);\n\n        // Write the content to the new window\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n\n        // Wait for images to load, then print\n        printWindow.onload = () => {\n          // Wait a bit longer for images to fully load\n          setTimeout(() => {\n            printWindow.print();\n            printWindow.close();\n          }, 1000);\n        };\n      } catch (error) {\n        console.error('Error printing activity:', error);\n        this.$toast.error('حدث خطأ أثناء الطباعة');\n      }\n    },\n    generatePrintContent(activity, scyLogoSrc = '', ndytLogoSrc = '') {\n      return `\n<!DOCTYPE html>\n<html dir=\"rtl\" lang=\"ar\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>تفاصيل النشاط - ${activity.title}</title>\n    <style>\n        @page {\n            size: A4;\n            margin: 2cm;\n        }\n\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n\n        body {\n            font-family: 'Arial', sans-serif;\n            line-height: 1.6;\n            color: #000;\n            background: white;\n            direction: rtl;\n        }\n\n        .document-header {\n            text-align: center;\n            margin-bottom: 30px;\n            border-bottom: 3px solid #000;\n            padding-bottom: 20px;\n        }\n\n        .logo-section {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            margin-bottom: 20px;\n        }\n\n        .logo-container {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            width: 120px;\n        }\n\n        .organization-logo {\n            width: 80px;\n            height: 80px;\n            border-radius: 50%;\n            border: 2px solid #333;\n            object-fit: cover;\n        }\n\n        .logo-label {\n            font-size: 12px;\n            margin-top: 5px;\n            color: #666;\n        }\n\n        .organization-info {\n            flex: 1;\n            text-align: center;\n        }\n\n        .organization-info h2 {\n            font-size: 28px;\n            font-weight: bold;\n            margin-bottom: 10px;\n            color: #000;\n        }\n\n        .organization-info h3 {\n            font-size: 24px;\n            font-weight: bold;\n            color: #333;\n        }\n\n        .document-title h1 {\n            font-size: 32px;\n            font-weight: bold;\n            text-decoration: underline;\n            margin-top: 20px;\n            color: #000;\n        }\n\n        .details-table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-top: 30px;\n        }\n\n        .detail-row {\n            display: table-row;\n        }\n\n        .detail-label {\n            display: table-cell;\n            background-color: #f0f0f0;\n            border: 2px solid #333;\n            padding: 12px 15px;\n            font-weight: bold;\n            font-size: 16px;\n            width: 30%;\n            vertical-align: top;\n            text-align: right;\n        }\n\n        .detail-value {\n            display: table-cell;\n            border: 2px solid #333;\n            padding: 12px 15px;\n            font-size: 16px;\n            width: 70%;\n            vertical-align: top;\n            text-align: right;\n        }\n\n        .goals-list, .target-groups-list, .levels-list {\n            list-style: none;\n            padding: 0;\n        }\n\n        .goals-list li, .levels-list li {\n            margin-bottom: 5px;\n            padding-right: 20px;\n            position: relative;\n        }\n\n        .goals-list li:before {\n            content: \"•\";\n            position: absolute;\n            right: 0;\n            font-weight: bold;\n        }\n\n        .target-groups-list li {\n            display: inline-block;\n            margin-left: 10px;\n        }\n\n        .target-groups-list li:not(:last-child):after {\n            content: \"،\";\n        }\n\n        .status-badge {\n            padding: 6px 12px;\n            border-radius: 20px;\n            font-weight: bold;\n            font-size: 14px;\n        }\n\n        .status-executed-paid { background-color: #d4edda; color: #155724; }\n        .status-executed-unpaid { background-color: #fff3cd; color: #856404; }\n        .status-accepted { background-color: #cce5ff; color: #004085; }\n        .status-rejected { background-color: #f8d7da; color: #721c24; }\n        .status-needs-edit { background-color: #ffeaa7; color: #6c5ce7; }\n        .status-paid-not-executed { background-color: #fab1a0; color: #e17055; }\n        .status-accepted-unpaid { background-color: #a8e6cf; color: #00b894; }\n        .status-sent { background-color: #e0e0e0; color: #636e72; }\n\n        .budget-table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-top: 10px;\n        }\n\n        .budget-table th {\n            background-color: #4472C4;\n            color: white;\n            border: 2px solid #000;\n            padding: 10px;\n            text-align: center;\n            font-weight: bold;\n        }\n\n        .budget-table td {\n            border: 1px solid #666;\n            padding: 8px;\n            text-align: center;\n        }\n\n        .budget-table tbody tr:nth-child(even) {\n            background-color: #f2f2f2;\n        }\n\n        .budget-table .total-row {\n            background-color: #2F5597 !important;\n            color: white;\n            font-weight: bold;\n        }\n\n        .budget-table .total-row td {\n            border: 3px solid #000;\n        }\n\n        @media print {\n            body {\n                -webkit-print-color-adjust: exact;\n                print-color-adjust: exact;\n            }\n\n            .no-print {\n                display: none !important;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"document-header\">\n        <div class=\"logo-section\">\n            <div class=\"logo-container\">\n                ${scyLogoSrc ? `<img src=\"${scyLogoSrc}\" alt=\"شعار المجلس الأعلى للشباب\" class=\"organization-logo\" />` : ''}\n                <span class=\"logo-label\">المجلس الأعلى للشباب</span>\n            </div>\n            <div class=\"organization-info\">\n                <h2>المجلس الأعلى للشباب</h2>\n                <h3>الفريق الوطني للشباب الرقمي</h3>\n            </div>\n            <div class=\"logo-container\">\n                ${ndytLogoSrc ? `<img src=\"${ndytLogoSrc}\" alt=\"شعار الفريق الوطني للشباب الرقمي\" class=\"organization-logo\" />` : ''}\n                <span class=\"logo-label\">الفريق الوطني للشباب الرقمي</span>\n            </div>\n        </div>\n        <div class=\"document-title\">\n            <h1>${activity.title}</h1>\n        </div>\n    </div>\n\n    <div class=\"details-table\">\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">الاسم</div>\n            <div class=\"detail-value\">الفريق الوطني للشباب الرقمي - ${activity.governorate} - ${activity.owner_name}</div>\n        </div>\n\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">اسم النشاط</div>\n            <div class=\"detail-value\">${activity.title}</div>\n        </div>\n\n        ${activity.activity_idea ? `\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">فكرة النشاط</div>\n            <div class=\"detail-value\">${activity.activity_idea}</div>\n        </div>\n        ` : ''}\n\n        ${activity.activity_goals && activity.activity_goals.length > 0 ? `\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">أهداف النشاط</div>\n            <div class=\"detail-value\">\n                <ul class=\"goals-list\">\n                    ${activity.activity_goals.map(goal => `<li>${goal.text}</li>`).join('')}\n                </ul>\n            </div>\n        </div>\n        ` : ''}\n\n        ${activity.target_groups && activity.target_groups.length > 0 ? `\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">الفئة المستهدفة</div>\n            <div class=\"detail-value\">\n                <ul class=\"target-groups-list\">\n                    ${activity.target_groups.map(group => `<li>${group.text}</li>`).join('')}\n                </ul>\n            </div>\n        </div>\n        ` : ''}\n\n        ${activity.audience_count ? `\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">عدد المستهدفين</div>\n            <div class=\"detail-value\">${activity.audience_count} شخص</div>\n        </div>\n        ` : ''}\n\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">تاريخ النشاط</div>\n            <div class=\"detail-value\">${this.formatDate(activity.activity_date)}</div>\n        </div>\n\n        ${activity.activity_time ? `\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">وقت النشاط</div>\n            <div class=\"detail-value\">${this.formatTime(activity.activity_time)}</div>\n        </div>\n        ` : ''}\n\n        ${activity.activity_location ? `\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">مكان النشاط</div>\n            <div class=\"detail-value\">${activity.activity_location}</div>\n        </div>\n        ` : ''}\n\n        ${activity.activity_duration ? `\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">مدة النشاط</div>\n            <div class=\"detail-value\">${activity.activity_duration}</div>\n        </div>\n        ` : ''}\n\n        ${activity.activity_budget ? `\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">ميزانية النشاط</div>\n            <div class=\"detail-value\">${this.formatCurrency(activity.activity_budget)}</div>\n        </div>\n        ` : ''}\n\n        ${activity.activity_levels && activity.activity_levels.length > 0 ? `\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">مراحل تنفيذ النشاط</div>\n            <div class=\"detail-value\">\n                <ul class=\"levels-list\">\n                    ${activity.activity_levels.map(level => `<li>${level.description}</li>`).join('')}\n                </ul>\n            </div>\n        </div>\n        ` : ''}\n\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">حالة النشاط</div>\n            <div class=\"detail-value\">\n                <span class=\"status-badge ${this.getStatusClass(activity.state)}\">\n                    ${activity.state}\n                </span>\n            </div>\n        </div>\n\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">المحافظة</div>\n            <div class=\"detail-value\">${activity.governorate}</div>\n        </div>\n\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">منسق المحافظة</div>\n            <div class=\"detail-value\">${activity.coordinator_name}</div>\n        </div>\n\n        <div class=\"detail-row\">\n            <div class=\"detail-label\">تاريخ الإرسال</div>\n            <div class=\"detail-value\">${this.formatDate(activity.created_at)}</div>\n        </div>\n    </div>\n\n    ${activity.budget_details && activity.budget_details.length > 0 ? `\n    <div style=\"margin-top: 30px;\">\n        <h3 style=\"text-align: center; margin-bottom: 20px; font-size: 24px;\">تفاصيل الصرف</h3>\n        <table class=\"budget-table\">\n            <thead>\n                <tr>\n                    <th>المادة</th>\n                    <th>النوع</th>\n                    <th>العدد</th>\n                    <th>السعر</th>\n                    <th>المجموع</th>\n                </tr>\n            </thead>\n            <tbody>\n                ${activity.budget_details.map((detail, index) => `\n                <tr ${index % 2 === 0 ? 'style=\"background-color: #f2f2f2;\"' : ''}>\n                    <td>${detail.name || ''}</td>\n                    <td>${detail.type || ''}</td>\n                    <td>${detail.amount || ''}</td>\n                    <td>${this.formatCurrency(detail.price)}</td>\n                    <td>${this.formatCurrency(detail.budgetPrice)}</td>\n                </tr>\n                `).join('')}\n            </tbody>\n            ${activity.total_budget ? `\n            <tfoot>\n                <tr class=\"total-row\">\n                    <td colspan=\"4\"><strong>الإجمالي</strong></td>\n                    <td><strong>${this.formatCurrency(activity.total_budget)}</strong></td>\n                </tr>\n            </tfoot>\n            ` : ''}\n        </table>\n    </div>\n    ` : ''}\n</body>\n</html>\n            `;\n    }\n  },\n  mounted() {\n    // Get user info from localStorage\n    const userStr = localStorage.getItem('ndyt_user');\n    if (userStr) {\n      this.user = JSON.parse(userStr);\n    }\n    // Add click outside listener\n    document.addEventListener('click', this.handleClickOutside);\n    // Load coordinator name from previous submissions\n    this.loadCoordinatorName();\n  },\n  beforeUnmount() {\n    document.removeEventListener('click', this.handleClickOutside);\n    // Ensure body scroll is restored if component is destroyed while modal is open\n    document.body.style.overflow = 'auto';\n  }\n};", "map": {"version": 3, "names": ["Document", "<PERSON>er", "Paragraph", "TextRun", "Table", "TableRow", "TableCell", "WidthType", "AlignmentType", "BorderStyle", "ImageRun", "saveAs", "name", "data", "activityOwnerName", "activityTitleName", "activityDescription", "activityDateInput", "activityIdeaInput", "activityGoals", "targetGroups", "audienceCount", "activityLocationInput", "activityTimeInput", "activityDurationInput", "activityBudgetInput", "budgetDetailsList", "activityTotalBudget", "activityLevels", "activityStatus", "filledActivity", "title", "owner", "description", "date", "idea", "goals", "audience", "location", "time", "duration", "budget", "budgetDetails", "totalBudget", "activities", "user", "showUserMenu", "myActivities", "loadingActivities", "editingActivity", "showMyActivities", "current<PERSON>iew", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "showAccountSettings", "accountForm", "fullName", "currentPassword", "newPassword", "confirmPassword", "teamPin", "updatingAccount", "showPinConfirmation", "pinConfirmationData", "pin", "action", "activity", "callback", "showActivityModal", "selectedActivity", "computed", "filteredActivities", "filter", "state", "methods", "selectFilter", "toggleUserMenu", "logout", "localStorage", "removeItem", "$router", "push", "goToAdmin", "openAccountSettings", "full_name", "getItem", "closeAccountSettings", "updateAccountSettings", "trim", "$swal", "fire", "text", "icon", "length", "updateData", "team_pin", "current_password", "new_password", "response", "$api", "put", "ok", "json", "setItem", "JSON", "stringify", "errorData", "error", "message", "console", "submitCV", "activityItems", "document", "querySelectorAll", "token", "$toast", "hasErrors", "index", "item", "ownerInput", "querySelector", "titleInput", "shortDescInput", "dateInput", "stateSelect", "ownerName", "value", "shortDescription", "activityDate", "log", "ideaTextarea", "activityIdea", "goalInputs", "Array", "from", "map", "input", "goal", "targetGroupInputs", "group", "audienceInput", "parseInt", "locationInput", "activityLocation", "timeInput", "activityTime", "durationInput", "activityDuration", "budgetInputs", "activity<PERSON>udget", "parseFloat", "budgetDetailItems", "for<PERSON>ach", "detailItem", "allInputs", "detail", "type", "amount", "price", "budgetPrice", "levelInputs", "level", "missingFields", "join", "fileId", "selectedFile", "formData", "FormData", "append", "uploadResponse", "fetch", "method", "headers", "body", "uploadResult", "file", "id", "uploadError", "owner_name", "short_description", "activity_date", "file_id", "activity_idea", "activity_goals", "target_groups", "audience_count", "activity_location", "activity_time", "activity_duration", "activity_budget", "budget_details", "total_budget", "activity_levels", "submissionData", "coordinator_name", "success", "remove", "fetchMyActivities", "jsonError", "get", "isArray", "submission_info", "submission_id", "governorate", "created_at", "status", "catch", "includes", "toggleMyActivities", "editActivity", "showPinConfirmationModal", "openEditModal", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "saveActivity", "updatedActivity", "findIndex", "a", "cancelEdit", "deleteActivity", "activityId", "find", "performDeleteActivity", "getStatusClass", "statusMap", "AddActivityItem", "activityDiv", "createElement", "className", "activityOwner", "placeholder", "required", "addEventListener", "event", "target", "append<PERSON><PERSON><PERSON>", "activityTitle", "activityShortDescription", "activityDateLabel", "textContent", "activityIdeaLabel", "activityGoalsLabel", "activityGoalsList", "AddActivityGoal", "goalItem", "goalInput", "activityGoalsAddButton", "onclick", "activityTargetGroupLabel", "activityTargetGroupList", "AddActivityTargetGroup", "targetGroup", "targetGroupItem", "targetGroupInput", "activityTargetGroupAddButton", "activityAudienceCountLabel", "activityAudienceCountDiv", "style", "activityAudienceCount", "activityAudienceCountCurrency", "activityLocationLabel", "activityTimeLabel", "activityDurationLabel", "activityBudgetLabel", "BudgetDiv", "display", "flexDirection", "gap", "alignItems", "direction", "BudgetInput", "currencyTypeLabel", "innerHTML", "outerHTML", "activityBudgetDetailsLabel", "activityBudgetDetailsList", "AddActivityBudgetDetailsRecord", "material", "unit", "budgetDetailItem", "materialNameRow", "materialNameLabel", "materialNameInput", "materialTypeRow", "materialTypeLabel", "materialTypeInput", "materialAmountRow", "materialAmountLabel", "materialAmountInput", "materialPriceRow", "materialPrice<PERSON>abel", "materialPriceInput", "materialBudgetPriceRow", "materialBudgetPriceLabel", "materialBudgetPriceInput", "AddActivityBudgetDetailsRecordButton", "activityTotalBudgetLabel", "totalBudgetDiv", "totalBudgetInput", "activityAppliedLevelsLabel", "activityAppliedLevelsList", "AddActivityAppliedLevel", "appliedLevel", "appliedLevelItem", "appliedLevelInput", "activityAppliedLevelAddButton", "activityStateLabel", "activityApplyState", "defaultOption", "disabled", "selected", "states", "option", "activityGovernorate", "coordinator", "activityDeleteButton", "handleClickOutside", "userSection", "closest", "set<PERSON><PERSON><PERSON>View", "view", "refreshActivities", "loadCoordinatorName", "exportToCSV", "csvData", "toLocaleDateString", "csv<PERSON><PERSON>nt", "BOM", "csvWithBOM", "blob", "Blob", "link", "download", "undefined", "url", "URL", "createObjectURL", "setAttribute", "currentDate", "toISOString", "split", "filterText", "filename", "visibility", "click", "<PERSON><PERSON><PERSON><PERSON>", "$nextTick", "pinInput", "getElementById", "focus", "closePinConfirmation", "confirmPinAction", "enteredPin", "storedPin", "downloadFile", "fileUrl", "file_url", "href", "file_name", "getShortFileName", "fileName", "extension", "pop", "nameWithoutExt", "substring", "lastIndexOf", "openActivityModal", "overflow", "closeActivityModal", "formatDate", "dateString", "formatTime", "timeString", "formatCurrency", "Intl", "NumberFormat", "format", "loadImageAsArrayBuffer", "imagePath", "imageUrl", "default", "Error", "arrayBuffer", "exportToDocx", "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ndytLogoBuffer", "sections", "headerParagraphs", "headerTable", "width", "size", "PERCENTAGE", "borders", "top", "NONE", "bottom", "left", "right", "insideHorizontal", "insideVertical", "rows", "children", "transformation", "height", "alignment", "CENTER", "bold", "font", "spacing", "after", "bidirectional", "underline", "before", "detailsTable", "SINGLE", "color", "createTableRow", "g", "l", "budgetTable", "shading", "fill", "margins", "toString", "columnSpan", "doc", "properties", "page", "margin", "styles", "run", "paragraph", "line", "toBlob", "replace", "label", "RIGHT", "printActivity", "scyLogoImg", "ndytLogoImg", "scyLogoSrc", "src", "ndytLogoSrc", "printWindow", "window", "open", "printContent", "generatePrintContent", "write", "close", "onload", "setTimeout", "print", "mounted", "userStr", "parse", "beforeUnmount", "removeEventListener"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\ActivityView.vue"], "sourcesContent": ["<script>\r\nimport { Document, Packer, Paragraph, TextRun, Table, TableRow, TableCell, WidthType, AlignmentType, BorderStyle, ImageRun } from 'docx';\r\nimport { saveAs } from 'file-saver';\r\n\r\nexport default {\r\n    name: 'ActivityView',\r\n    data() {\r\n        return {\r\n            activityOwnerName: '',\r\n            activityTitleName: '',\r\n            activityDescription: '',\r\n            activityDateInput: '',\r\n            activityIdeaInput: '',\r\n            activityGoals: [],\r\n            targetGroups: [],\r\n            audienceCount: 0,\r\n            activityLocationInput: '',\r\n            activityTimeInput: '',\r\n            activityDurationInput: '',\r\n            activityBudgetInput: 0,\r\n            budgetDetailsList: [],\r\n            activityTotalBudget: 0,\r\n            activityLevels: [],\r\n            activityStatus: '',\r\n            filledActivity: {\r\n                title: this.activityTitleName,\r\n                owner: this.activityOwnerName,\r\n                description: this.activityDescription,\r\n                date: this.activityDateInput,\r\n                idea: this.activityIdeaInput,\r\n                goals: this.activityGoals,\r\n                audience: this.targetGroups,\r\n                location: this.activityLocationInput,\r\n                time: this.activityTimeInput,\r\n                duration: this.activityDurationInput,\r\n                budget: this.activityBudgetInput,\r\n                budgetDetails: this.budgetDetailsList,\r\n                totalBudget: this.activityTotalBudget\r\n            },\r\n            //------------------------------------\r\n            activities: [],\r\n            //------------------------------------\r\n            user: null,\r\n            showUserMenu: false,\r\n            myActivities: [],\r\n            loadingActivities: false,\r\n            editingActivity: null,\r\n            showMyActivities: false,\r\n            currentView: 'submit',\r\n            coordinatorName: '',\r\n            selectedFilter: null, // null means show all, otherwise filter by state\r\n            showAccountSettings: false,\r\n            accountForm: {\r\n                fullName: '',\r\n                currentPassword: '',\r\n                newPassword: '',\r\n                confirmPassword: '',\r\n                teamPin: ''\r\n            },\r\n            updatingAccount: false,\r\n            showPinConfirmation: false,\r\n            pinConfirmationData: {\r\n                pin: '',\r\n                action: '', // 'edit' or 'delete'\r\n                activity: null,\r\n                callback: null\r\n            },\r\n            showActivityModal: false,\r\n            selectedActivity: null\r\n        };\r\n    },\r\n    computed: {\r\n        filteredActivities() {\r\n            if (!this.selectedFilter) {\r\n                return this.myActivities;\r\n            }\r\n            return this.myActivities.filter(activity => activity.state === this.selectedFilter);\r\n        }\r\n    },\r\n    methods: {\r\n        selectFilter(state) {\r\n            // Toggle filter: if same state is clicked, clear filter; otherwise set new filter\r\n            this.selectedFilter = this.selectedFilter === state ? null : state;\r\n        },\r\n        toggleUserMenu() {\r\n            this.showUserMenu = !this.showUserMenu;\r\n        },\r\n        logout() {\r\n            localStorage.removeItem('ndyt_token');\r\n            localStorage.removeItem('ndyt_user');\r\n            localStorage.removeItem('ndyt_team_pin');\r\n            this.$router.push('/login');\r\n        },\r\n        goToAdmin() {\r\n            this.showUserMenu = false;\r\n            this.$router.push('/admin');\r\n        },\r\n        openAccountSettings() {\r\n            this.showUserMenu = false;\r\n            this.accountForm.fullName = this.user?.full_name || '';\r\n            this.accountForm.teamPin = localStorage.getItem('ndyt_team_pin') || '';\r\n            this.accountForm.currentPassword = '';\r\n            this.accountForm.newPassword = '';\r\n            this.accountForm.confirmPassword = '';\r\n            this.showAccountSettings = true;\r\n        },\r\n        closeAccountSettings() {\r\n            this.showAccountSettings = false;\r\n            this.accountForm = {\r\n                fullName: '',\r\n                currentPassword: '',\r\n                newPassword: '',\r\n                confirmPassword: '',\r\n                teamPin: ''\r\n            };\r\n        },\r\n        async updateAccountSettings() {\r\n            // Validate form\r\n            if (!this.accountForm.fullName.trim()) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'يرجى إدخال الاسم الكامل',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            if (this.accountForm.newPassword && this.accountForm.newPassword !== this.accountForm.confirmPassword) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'كلمة المرور الجديدة وتأكيدها غير متطابقتان',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            if (this.accountForm.newPassword && this.accountForm.newPassword.length < 6) {\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في البيانات',\r\n                    text: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',\r\n                    icon: 'error'\r\n                });\r\n                return;\r\n            }\r\n\r\n            this.updatingAccount = true;\r\n\r\n            try {\r\n                const updateData = {\r\n                    full_name: this.accountForm.fullName.trim(),\r\n                    team_pin: this.accountForm.teamPin.trim()\r\n                };\r\n\r\n                // Only include password if user wants to change it\r\n                if (this.accountForm.newPassword) {\r\n                    updateData.current_password = this.accountForm.currentPassword;\r\n                    updateData.new_password = this.accountForm.newPassword;\r\n                }\r\n\r\n                const response = await this.$api.put('/user/update-profile', updateData);\r\n\r\n                if (response.ok) {\r\n                    await response.json();\r\n\r\n                    // Update local user data\r\n                    this.user.full_name = this.accountForm.fullName.trim();\r\n                    localStorage.setItem('ndyt_user', JSON.stringify(this.user));\r\n                    localStorage.setItem('ndyt_team_pin', this.accountForm.teamPin.trim());\r\n\r\n                    await this.$swal.fire({\r\n                        title: 'تم التحديث بنجاح',\r\n                        text: 'تم تحديث معلومات الحساب بنجاح',\r\n                        icon: 'success'\r\n                    });\r\n\r\n                    this.closeAccountSettings();\r\n                } else {\r\n                    const errorData = await response.json();\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في التحديث',\r\n                        text: errorData.error || 'فشل في تحديث معلومات الحساب',\r\n                        icon: 'error'\r\n                    });\r\n                }\r\n            } catch (error) {\r\n                if (error.message === 'Session expired') {\r\n                    // Token expiration is already handled by the API service\r\n                    return;\r\n                }\r\n                console.error('Error updating account:', error);\r\n                await this.$swal.fire({\r\n                    title: 'خطأ في الاتصال',\r\n                    text: 'حدث خطأ أثناء تحديث معلومات الحساب',\r\n                    icon: 'error'\r\n                });\r\n            } finally {\r\n                this.updatingAccount = false;\r\n            }\r\n        },\r\n        async submitCV() {\r\n            try {\r\n                // Validate coordinator name\r\n                if (!this.coordinatorName.trim()) {\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في البيانات',\r\n                        text: 'يرجى إدخال اسم منسق المحافظة',\r\n                        icon: 'error'\r\n                    });\r\n                    return;\r\n                }\r\n                \r\n                // Collect all activity items\r\n                const activityItems = document.querySelectorAll('.activity-item');\r\n                \r\n                if (activityItems.length === 0) {\r\n                    await this.$swal.fire({\r\n                        title: 'خطأ في البيانات',\r\n                        text: 'يرجى إضافة نشاط واحد على الأقل',\r\n                        icon: 'error'\r\n                    });\r\n                    return;\r\n                }\r\n                \r\n                const token = localStorage.getItem('ndyt_token');\r\n                const teamPin = localStorage.getItem('ndyt_team_pin');\r\n                \r\n                if (!token) {\r\n                    this.$toast.error('يرجى تسجيل الدخول أولاً');\r\n                    this.$router.push('/login');\r\n                    return;\r\n                }\r\n                \r\n                const activities = [];\r\n                let hasErrors = false;\r\n                \r\n                // Process each activity item\r\n                for (let index = 0; index < activityItems.length; index++) {\r\n                    const item = activityItems[index];\r\n\r\n                    // Extract basic fields - be more specific about selectors\r\n                    const ownerInput = item.querySelector('input[placeholder=\"اسم صاحب االنشاط\"]');\r\n                    const titleInput = item.querySelector('input[placeholder=\"عنوان النشاط\"]');\r\n                    const shortDescInput = item.querySelector('input[placeholder=\"وصف قصير للنشاط\"]');\r\n                    const dateInput = item.querySelector('input[type=\"date\"]');\r\n                    const stateSelect = item.querySelector('select.activity-input');\r\n\r\n                    const ownerName = ownerInput?.value?.trim();\r\n                    const title = titleInput?.value?.trim();\r\n                    const shortDescription = shortDescInput?.value?.trim();\r\n                    const activityDate = dateInput?.value;\r\n                    const state = stateSelect?.value;\r\n\r\n                    // Debug logging to verify state extraction\r\n                    console.log(`Activity ${index + 1} - State extracted:`, state, 'from element:', stateSelect);\r\n\r\n                    // Extract additional fields by finding specific elements\r\n                    const ideaTextarea = item.querySelector('textarea');\r\n                    const activityIdea = ideaTextarea?.value?.trim() || '';\r\n\r\n                    // Extract goals\r\n                    const goalInputs = item.querySelectorAll('ul li input[placeholder=\"هدف النشاط\"]');\r\n                    const activityGoals = Array.from(goalInputs).map(input => ({ text: input.value.trim() })).filter(goal => goal.text);\r\n\r\n                    // Extract target groups\r\n                    const targetGroupInputs = item.querySelectorAll('ul li input[placeholder=\"الفئة المستهدفة\"]');\r\n                    const targetGroups = Array.from(targetGroupInputs).map(input => ({ text: input.value.trim() })).filter(group => group.text);\r\n\r\n                    // Extract audience count\r\n                    const audienceInput = item.querySelector('input[type=\"stat-number\"]');\r\n                    const audienceCount = audienceInput?.value ? parseInt(audienceInput.value) : null;\r\n\r\n                    // Extract location, time, duration\r\n                    const locationInput = item.querySelector('input[placeholder=\"موقع النشاط\"]');\r\n                    const activityLocation = locationInput?.value?.trim() || '';\r\n\r\n                    const timeInput = item.querySelector('input[type=\"time\"]');\r\n                    const activityTime = timeInput?.value || null;\r\n\r\n                    const durationInput = item.querySelector('input[placeholder=\"مدة النشاط\"]');\r\n                    const activityDuration = durationInput?.value?.trim() || '';\r\n\r\n                    // Extract budget information\r\n                    const budgetInputs = item.querySelectorAll('.budget-input input');\r\n                    const activityBudget = budgetInputs[0]?.value ? parseFloat(budgetInputs[0].value) : null;\r\n                    const totalBudget = budgetInputs[1]?.value ? parseFloat(budgetInputs[1].value) : null;\r\n\r\n                    // Extract budget details using the specific class\r\n                    const budgetDetailItems = item.querySelectorAll('.budget-detail-item');\r\n                    const budgetDetails = [];\r\n\r\n                    budgetDetailItems.forEach(detailItem => {\r\n                        // Each budget detail item has multiple rows with inputs\r\n                        const allInputs = detailItem.querySelectorAll('input');\r\n                        if (allInputs.length >= 5) {\r\n                            const detail = {\r\n                                name: allInputs[0]?.value?.trim() || '',\r\n                                type: allInputs[1]?.value?.trim() || '',\r\n                                amount: allInputs[2]?.value ? parseInt(allInputs[2].value) : 0,\r\n                                price: allInputs[3]?.value ? parseFloat(allInputs[3].value) : 0,\r\n                                budgetPrice: allInputs[4]?.value ? parseFloat(allInputs[4].value) : 0\r\n                            };\r\n                            if (detail.name) budgetDetails.push(detail);\r\n                        }\r\n                    });\r\n\r\n                    // Debug logging for budget details\r\n                    console.log(`Activity ${index + 1} - Budget details extracted:`, budgetDetails);\r\n\r\n                    // Extract activity levels\r\n                    const levelInputs = item.querySelectorAll('ul li input[placeholder=\"وصف المرحلة التنفيذية\"]');\r\n                    const activityLevels = Array.from(levelInputs).map(input => ({ description: input.value.trim() })).filter(level => level.description);\r\n\r\n                    // Validate required fields\r\n                    if (!ownerName || !title || !activityDate || !state) {\r\n                        let missingFields = [];\r\n                        if (!ownerName) missingFields.push('اسم صاحب النشاط');\r\n                        if (!title) missingFields.push('عنوان النشاط');\r\n                        if (!activityDate) missingFields.push('تاريخ النشاط');\r\n                        if (!state) missingFields.push('حالة النشاط');\r\n\r\n                        this.$swal.fire({\r\n                            title: 'خطأ في البيانات',\r\n                            text: `يرجى ملء الحقول المطلوبة للنشاط رقم ${index + 1}: ${missingFields.join(', ')}`,\r\n                            icon: 'error'\r\n                        });\r\n                        hasErrors = true;\r\n                        break;\r\n                    }\r\n\r\n                    let fileId = null;\r\n\r\n                    // Handle file upload if a file is selected\r\n                    if (item.selectedFile) {\r\n                        try {\r\n                            const formData = new FormData();\r\n                            formData.append('file', item.selectedFile);\r\n\r\n                            const uploadResponse = await fetch('/api/v1/ndyt-activities/upload-file', {\r\n                                method: 'POST',\r\n                                headers: {\r\n                                    'Authorization': `Bearer ${token}`\r\n                                },\r\n                                body: formData\r\n                            });\r\n\r\n                            if (uploadResponse.ok) {\r\n                                const uploadResult = await uploadResponse.json();\r\n                                if (uploadResult.file && uploadResult.file.id) {\r\n                                    fileId = uploadResult.file.id;\r\n                                }\r\n                            } else {\r\n                                const errorData = await uploadResponse.json();\r\n                                this.$toast.error(`فشل في رفع ملف النشاط رقم ${index + 1}: ${errorData.error || 'خطأ غير معروف'}`);\r\n                                hasErrors = true;\r\n                                break;\r\n                            }\r\n                        } catch (uploadError) {\r\n                            console.error('File upload error:', uploadError);\r\n                            this.$toast.error(`خطأ في رفع ملف النشاط رقم ${index + 1}`);\r\n                            hasErrors = true;\r\n                            break;\r\n                        }\r\n                    }\r\n\r\n                    activities.push({\r\n                        owner_name: ownerName,\r\n                        title: title,\r\n                        short_description: shortDescription || '',\r\n                        activity_date: activityDate,\r\n                        state: state,\r\n                        file_id: fileId,\r\n                        activity_idea: activityIdea,\r\n                        activity_goals: activityGoals,\r\n                        target_groups: targetGroups,\r\n                        audience_count: audienceCount,\r\n                        activity_location: activityLocation,\r\n                        activity_time: activityTime,\r\n                        activity_duration: activityDuration,\r\n                        activity_budget: activityBudget,\r\n                        budget_details: budgetDetails,\r\n                        total_budget: totalBudget,\r\n                        activity_levels: activityLevels\r\n                    });\r\n                }\r\n                \r\n                if (hasErrors) return;\r\n                \r\n                // Prepare submission data\r\n                const submissionData = {\r\n                    coordinator_name: this.coordinatorName.trim(),\r\n                    activities: activities\r\n                };\r\n                \r\n                // Submit to backend\r\n                const response = await fetch('/api/v1/ndyt-activities/submissions', {\r\n                    method: 'POST',\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'x-team-pin': teamPin\r\n                    },\r\n                    body: JSON.stringify(submissionData)\r\n                });\r\n                \r\n                if (response.ok) {\r\n                    await response.json();\r\n                    this.$toast.success('تم إرسال النشاطات بنجاح!');\r\n                    \r\n                    // Clear the form\r\n                    this.coordinatorName = '';\r\n                    \r\n                    // Remove all activity items\r\n                    activityItems.forEach(item => item.remove());\r\n                    \r\n                    // Refresh my activities if they're currently shown\r\n                    if (this.showMyActivities) {\r\n                        this.fetchMyActivities();\r\n                    }\r\n                } else {\r\n                    let errorData = {};\r\n                    try {\r\n                        errorData = await response.json();\r\n                    } catch (jsonError) {\r\n                        console.error('Error parsing response JSON:', jsonError);\r\n                    }\r\n                    this.$toast.error(`فشل في إرسال النشاطات: ${errorData.message || 'خطأ غير معروف'}`);\r\n                }\r\n                \r\n            } catch (error) {\r\n                console.error('Error submitting activities:', error);\r\n                this.$toast.error('حدث خطأ أثناء إرسال النشاطات. يرجى المحاولة مرة أخرى.');\r\n            }\r\n        },\r\n        async fetchMyActivities() {\r\n            this.loadingActivities = true;\r\n            this.myActivities = []; // Clear existing activities\r\n\r\n            try {\r\n                const response = await this.$api.get('/activities');\r\n\r\n                if (response.ok) {\r\n                    const data = await response.json();\r\n\r\n                    // The new endpoint returns activities directly with role-based filtering\r\n                    if (data.activities && Array.isArray(data.activities)) {\r\n                        this.myActivities = data.activities.map(activity => ({\r\n                            ...activity,\r\n                            submission_info: {\r\n                                id: activity.submission_id,\r\n                                governorate: activity.governorate,\r\n                                coordinator_name: activity.coordinator_name,\r\n                                created_at: activity.created_at\r\n                            }\r\n                        }));\r\n                    }\r\n                } else if (response.status === 404) {\r\n                    // No activities found - this is normal, not an error\r\n                    this.myActivities = [];\r\n                } else {\r\n                    const errorData = await response.json().catch(() => ({}));\r\n                    console.error('Failed to fetch activities:', errorData);\r\n                    this.$toast.error(`فشل في تحميل النشاطات: ${errorData.message || 'خطأ في الخادم'}`);\r\n                }\r\n            } catch (error) {\r\n                if (error.message === 'Session expired') {\r\n                    // Token expiration is already handled by the API service\r\n                    return;\r\n                }\r\n                console.error('Error fetching activities:', error);\r\n                if (error.name === 'TypeError' && error.message.includes('fetch')) {\r\n                    this.$toast.error('خطأ في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.');\r\n                } else {\r\n                    this.$toast.error('حدث خطأ أثناء تحميل النشاطات. يرجى المحاولة مرة أخرى.');\r\n                }\r\n            } finally {\r\n                this.loadingActivities = false;\r\n            }\r\n        },\r\n        toggleMyActivities() {\r\n            this.showMyActivities = !this.showMyActivities;\r\n            if (this.showMyActivities && this.myActivities.length === 0) {\r\n                this.fetchMyActivities();\r\n            }\r\n        },\r\n        editActivity(activity) {\r\n            this.showPinConfirmationModal('edit', activity, () => {\r\n                this.openEditModal(activity);\r\n            });\r\n        },\r\n        openEditModal(activity) {\r\n            this.editingActivity = { ...activity };\r\n            // Ensure date is properly formatted for date input (YYYY-MM-DD)\r\n            if (this.editingActivity.activity_date) {\r\n                const date = new Date(this.editingActivity.activity_date);\r\n                // Use timezone-safe formatting to avoid date shifting\r\n                const year = date.getFullYear();\r\n                const month = String(date.getMonth() + 1).padStart(2, '0');\r\n                const day = String(date.getDate()).padStart(2, '0');\r\n                this.editingActivity.activity_date = `${year}-${month}-${day}`;\r\n            }\r\n        },\r\n        async saveActivity() {\r\n            if (!this.editingActivity) return;\r\n\r\n            try {\r\n                const updateData = {\r\n                    owner_name: this.editingActivity.owner_name,\r\n                    title: this.editingActivity.title,\r\n                    short_description: this.editingActivity.short_description,\r\n                    activity_date: this.editingActivity.activity_date,\r\n                    state: this.editingActivity.state\r\n                };\r\n\r\n                const response = await this.$api.put(`/activities/${this.editingActivity.id}`, updateData);\r\n\r\n                if (response.ok) {\r\n                    const updatedActivity = await response.json();\r\n                    // Update the activity in the list\r\n                    const index = this.myActivities.findIndex(a => a.id === updatedActivity.id);\r\n                    if (index !== -1) {\r\n                        this.myActivities[index] = { ...updatedActivity, submission_info: this.myActivities[index].submission_info };\r\n                    }\r\n                    this.editingActivity = null;\r\n                    this.$toast.success('تم تحديث النشاط بنجاح!');\r\n                } else {\r\n                    this.$toast.error('فشل في تحديث النشاط');\r\n                }\r\n            } catch (error) {\r\n                if (error.message === 'Session expired') {\r\n                    // Token expiration is already handled by the API service\r\n                    return;\r\n                }\r\n                console.error('Error updating activity:', error);\r\n                this.$toast.error('حدث خطأ أثناء تحديث النشاط');\r\n            }\r\n        },\r\n        cancelEdit() {\r\n            this.editingActivity = null;\r\n        },\r\n        deleteActivity(activityId) {\r\n            const activity = this.myActivities.find(a => a.id === activityId);\r\n            this.showPinConfirmationModal('delete', activity, async () => {\r\n                await this.performDeleteActivity(activityId);\r\n            });\r\n        },\r\n        async performDeleteActivity(activityId) {\r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                const teamPin = localStorage.getItem('ndyt_team_pin');\r\n                \r\n                const response = await fetch(`/api/v1/ndyt-activities/activities/${activityId}`, {\r\n                    method: 'DELETE',\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'x-team-pin': teamPin\r\n                    }\r\n                });\r\n                \r\n                if (response.ok) {\r\n                    this.myActivities = this.myActivities.filter(a => a.id !== activityId);\r\n                    this.$toast.success('تم حذف النشاط بنجاح!');\r\n                } else {\r\n                    this.$toast.error('فشل في حذف النشاط');\r\n                }\r\n            } catch (error) {\r\n                console.error('Error deleting activity:', error);\r\n                this.$toast.error('حدث خطأ أثناء حذف النشاط');\r\n            }\r\n        },\r\n        getStatusClass(status) {\r\n            const statusMap = {\r\n                'منفذ بصرف': 'status-executed-paid',\r\n                'منفذ بدون صرف': 'status-executed-unpaid',\r\n                'مقبول': 'status-accepted',\r\n                'مرفوض': 'status-rejected',\r\n                'يحتاج تعديل': 'status-needs-edit',\r\n                'صرف و لم ينفذ': 'status-paid-not-executed',\r\n                'مقبول دون صرف': 'status-accepted-unpaid',\r\n                'مرسل': 'status-sent'\r\n            };\r\n            return statusMap[status] || 'status-default';\r\n        },\r\n        AddActivityItem() {\r\n            const activityDiv = document.createElement('div');\r\n            activityDiv.className = 'activity-item';\r\n            const activityOwner = document.createElement('input');\r\n            activityOwner.type = 'text';\r\n            activityOwner.placeholder = 'اسم صاحب االنشاط';\r\n            activityOwner.className = 'activity-input';\r\n            activityOwner.required = true;\r\n            activityOwner.addEventListener('input', (event) => {\r\n                this.activityOwnerName = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityOwner);\r\n            const activityTitle = document.createElement('input');\r\n            activityTitle.type = 'text';\r\n            activityTitle.placeholder = 'عنوان النشاط';\r\n            activityTitle.className = 'activity-input';\r\n            activityTitle.required = true;\r\n            activityTitle.addEventListener('input', (event) => {\r\n                this.activityTitle = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityTitle);\r\n            const activityShortDescription = document.createElement('input');\r\n            activityShortDescription.type = 'text';\r\n            activityShortDescription.placeholder = 'وصف قصير للنشاط';\r\n            activityShortDescription.className = 'activity-input';\r\n            activityShortDescription.required = true;\r\n            activityShortDescription.addEventListener('input', (event) => {\r\n                this.activityDescription = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityShortDescription);\r\n            const activityDateLabel = document.createElement('label');\r\n            activityDateLabel.textContent = 'تاريخ النشاط';\r\n            activityDiv.appendChild(activityDateLabel);\r\n            const activityDate = document.createElement('input');\r\n            activityDate.type = 'date';\r\n            activityDate.className = 'activity-input';\r\n            activityDate.required = true;\r\n            activityDate.addEventListener('input', (event) => {\r\n                this.activityDateInput = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityDate);\r\n            const activityIdeaLabel = document.createElement('label');\r\n            activityIdeaLabel.textContent = 'فكرة النشاط';\r\n            activityDiv.appendChild(activityIdeaLabel);\r\n            const activityIdea = document.createElement('textarea');\r\n            activityIdea.placeholder = 'فكرة النشاط';\r\n            activityIdea.className = 'activity-input';\r\n            activityIdea.required = true;\r\n            activityIdea.addEventListener('input', (event) => {\r\n                this.activityIdeaInput = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityIdea);\r\n            const activityGoalsLabel = document.createElement('label');\r\n            activityGoalsLabel.textContent = 'أهداف النشاط';\r\n            activityDiv.appendChild(activityGoalsLabel);\r\n            const activityGoalsList = document.createElement('ul');\r\n            activityDiv.appendChild(activityGoalsList);\r\n            const AddActivityGoal = () => {\r\n                const goal = { text: '' }\r\n                const goalItem = document.createElement('li');\r\n                const goalInput = document.createElement('input');\r\n                goalInput.type = 'text';\r\n                goalInput.placeholder = 'هدف النشاط';\r\n                goalInput.className = 'activity-input';\r\n                goalInput.required = true;\r\n                goalInput.addEventListener('input', (event) => {\r\n                    goal.text = event.target.value;\r\n                });\r\n                this.activityGoals.push(goal);\r\n                goalItem.appendChild(goalInput);\r\n                activityGoalsList.appendChild(goalItem);\r\n            };\r\n            const activityGoalsAddButton = document.createElement('button');\r\n            activityGoalsAddButton.textContent = 'إضافة هدف';\r\n            activityGoalsAddButton.onclick = AddActivityGoal;\r\n            activityDiv.appendChild(activityGoalsAddButton);\r\n            const activityTargetGroupLabel = document.createElement('label');\r\n            activityTargetGroupLabel.textContent = 'الفئة المستهدفة';\r\n            activityDiv.appendChild(activityTargetGroupLabel);\r\n            const activityTargetGroupList = document.createElement('ul');\r\n            activityDiv.appendChild(activityTargetGroupList);\r\n            const AddActivityTargetGroup = () => {\r\n                const targetGroup = { text: '' };\r\n                const targetGroupItem = document.createElement('li');\r\n                const targetGroupInput = document.createElement('input');\r\n                targetGroupInput.type = 'text';\r\n                targetGroupInput.placeholder = 'الفئة المستهدفة';\r\n                targetGroupInput.className = 'activity-input';\r\n                targetGroupInput.required = true;\r\n                targetGroupInput.addEventListener('input', (event) => {\r\n                    targetGroup.text = event.target.value;\r\n                });\r\n                this.targetGroups.push(targetGroup);\r\n                targetGroupItem.appendChild(targetGroupInput);\r\n                activityTargetGroupList.appendChild(targetGroupItem);\r\n            };\r\n            const activityTargetGroupAddButton = document.createElement('button');\r\n            activityTargetGroupAddButton.textContent = 'إضافة فئة مستهدفة';\r\n            activityTargetGroupAddButton.onclick = AddActivityTargetGroup;\r\n            activityDiv.appendChild(activityTargetGroupAddButton);\r\n            const activityAudienceCountLabel = document.createElement('label');\r\n            activityAudienceCountLabel.textContent = 'عدد الجمهور المستهدف';\r\n            activityDiv.appendChild(activityAudienceCountLabel);\r\n            const activityAudienceCountDiv = document.createElement('div');\r\n            activityAudienceCountDiv.style = 'display: flex; flex-direction: row; align-items: center; gap: 30px;';\r\n            const activityAudienceCount = document.createElement('input');\r\n            activityAudienceCount.type = 'stat-number';\r\n            activityAudienceCount.className = 'activity-input';\r\n            activityAudienceCount.required = true;\r\n            activityAudienceCount.addEventListener('input', (event) => {\r\n                this.audienceCount = event.target.value;\r\n            });\r\n            activityAudienceCountDiv.appendChild(activityAudienceCount);\r\n            const activityAudienceCountCurrency = document.createElement('span');\r\n            activityAudienceCountCurrency.textContent = 'شخص';\r\n            activityAudienceCountDiv.appendChild(activityAudienceCountCurrency);\r\n            activityDiv.appendChild(activityAudienceCountDiv);\r\n            const activityLocationLabel = document.createElement('label');\r\n            activityLocationLabel.textContent = 'موقع النشاط';\r\n            activityDiv.appendChild(activityLocationLabel);\r\n            const activityLocation = document.createElement('input');\r\n            activityLocation.type = 'text';\r\n            activityLocation.placeholder = 'موقع النشاط';\r\n            activityLocation.className = 'activity-input';\r\n            activityLocation.required = true;\r\n            activityLocation.addEventListener('input', (event) => {\r\n                this.activityLocationInput = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityLocation);\r\n            const activityTimeLabel = document.createElement('label');\r\n            activityTimeLabel.textContent = 'وقت النشاط';\r\n            activityDiv.appendChild(activityTimeLabel);\r\n            const activityTime = document.createElement('input');\r\n            activityTime.type = 'time';\r\n            activityTime.className = 'activity-input';\r\n            activityTime.required = true;\r\n            activityTime.addEventListener('input', (event) => {\r\n                this.activityTimeInput = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityTime);\r\n            const activityDurationLabel = document.createElement('label');\r\n            activityDurationLabel.textContent = 'مدة النشاط';\r\n            activityDiv.appendChild(activityDurationLabel);\r\n            const activityDuration = document.createElement('input');\r\n            activityDuration.type = 'text';\r\n            activityDuration.placeholder = 'مدة النشاط';\r\n            activityDuration.className = 'activity-input';\r\n            activityDuration.required = true;\r\n            activityDuration.addEventListener('input', (event) => {\r\n                this.activityDurationInput = event.target.value;\r\n            });\r\n            activityDiv.appendChild(activityDuration);\r\n            const activityBudgetLabel = document.createElement('label')\r\n            activityBudgetLabel.textContent = 'ميزانية النشاط'\r\n            activityDiv.appendChild(activityBudgetLabel)\r\n            const BudgetDiv = document.createElement('div');\r\n            BudgetDiv.className = 'budget-input';\r\n            BudgetDiv.style.display = 'flex';\r\n            BudgetDiv.style.flexDirection = 'row';\r\n            BudgetDiv.style.gap = '40px';\r\n            BudgetDiv.style.alignItems = 'center';\r\n            BudgetDiv.style.direction = 'rtl'; \r\n            const BudgetInput = document.createElement('input');\r\n            BudgetInput.type = 'stat-number'; \r\n            BudgetInput.placeholder = 'أدخل الميزانية'\r\n            BudgetInput.required = true;\r\n            BudgetInput.addEventListener('input', (event) => {\r\n                this.activityBudgetInput = event.target.value;\r\n            });\r\n            BudgetDiv.appendChild(BudgetInput);\r\n            const currencyTypeLabel = document.createElement('label');\r\n            currencyTypeLabel.textContent = 'د. ع'; \r\n            BudgetDiv.innerHTML += currencyTypeLabel.outerHTML;\r\n            activityDiv.appendChild(BudgetDiv);\r\n            const activityBudgetDetailsLabel = document.createElement('label');\r\n            activityBudgetDetailsLabel.textContent = 'تفاصيل الصرف';\r\n            activityDiv.appendChild(activityBudgetDetailsLabel);\r\n            const activityBudgetDetailsList = document.createElement('ul');\r\n            activityDiv.appendChild(activityBudgetDetailsList);\r\n            const AddActivityBudgetDetailsRecord = () => {\r\n               const material = { name: '', type: '', unit: '', amount: 0, price: 0, budgetPrice: 0 };\r\n               const budgetDetailItem = document.createElement('div');\r\n               budgetDetailItem.className = 'budget-detail-item';\r\n               budgetDetailItem.style =\r\n               'display: flex; flex-direction: column; gap: 20px;' +\r\n               'align-items: start; margin-bottom: 10px; justify-items: center;' +\r\n               'background-color: transparent; padding: 5px 25px; border-radius: 5px;' +\r\n               'border: 1px solid gray;';\r\n               const materialNameRow = document.createElement('div');\r\n               materialNameRow.style.display = 'flex'\r\n               materialNameRow.flexDirection = 'row'\r\n               materialNameRow.style.gap = '80px'\r\n               const materialNameLabel = document.createElement('label');\r\n               materialNameLabel.textContent = 'اسم المادة';\r\n               materialNameRow.appendChild(materialNameLabel);\r\n               const materialNameInput = document.createElement('input');\r\n               materialNameInput.type = 'text';\r\n               materialNameInput.placeholder = 'أدخل اسم المادة';\r\n               materialNameInput.required = true;\r\n               materialNameInput.addEventListener('input', (event) => {\r\n                   material.name = event.target.value;\r\n               });\r\n               materialNameRow.appendChild(materialNameInput);\r\n               budgetDetailItem.appendChild(materialNameRow);\r\n               const materialTypeRow = document.createElement('div')\r\n               materialTypeRow.style.display = 'flex'\r\n               materialTypeRow.style.flexDirection = 'row'\r\n               materialTypeRow.style.gap = '80px'\r\n               const materialTypeLabel = document.createElement('label')\r\n               materialTypeLabel.textContent = 'نوع المادة'\r\n               const materialTypeInput = document.createElement('input')\r\n               materialTypeInput.type = 'text'\r\n               materialTypeInput.placeholder = 'أدخل نوع المادة'\r\n               materialTypeInput.required = true;\r\n               materialTypeInput.addEventListener('input', (event) => {\r\n                   material.type = event.target.value;\r\n               });\r\n               materialTypeRow.appendChild(materialTypeLabel)\r\n               materialTypeRow.appendChild(materialTypeInput)\r\n               budgetDetailItem.appendChild(materialTypeRow)\r\n               const materialAmountRow = document.createElement('div')\r\n               materialAmountRow.style.display = 'flex'\r\n               materialAmountRow.style.flexDirection = 'row'\r\n               materialAmountRow.style.gap = '80px'\r\n               const materialAmountLabel = document.createElement('label')\r\n               materialAmountLabel.textContent = 'عدد الكمية'\r\n               materialAmountRow.appendChild(materialAmountLabel)\r\n               const materialAmountInput = document.createElement('input')\r\n               materialAmountInput.type = 'text'\r\n               materialAmountInput.placeholder = 'أدخل عدد الكمية'\r\n               materialAmountInput.required = true;\r\n               materialAmountInput.addEventListener('input', (event) => {\r\n                   material.amount = event.target.value;\r\n               });\r\n               materialAmountRow.appendChild(materialAmountInput)\r\n               budgetDetailItem.appendChild(materialAmountRow)\r\n               const materialPriceRow = document.createElement('div')\r\n               materialPriceRow.style.display = 'flex'\r\n               materialPriceRow.style.flexDirection = 'row'\r\n               materialPriceRow.style.gap = '80px'\r\n               const materialPriceLabel = document.createElement('label')\r\n               materialPriceLabel.textContent = 'سعر الوحدة'\r\n               materialPriceRow.appendChild(materialPriceLabel)\r\n               const materialPriceInput = document.createElement('input')\r\n               materialPriceInput.type = 'text'\r\n               materialPriceInput.placeholder= 'أدخل سعر المادة'\r\n               materialPriceInput.required = true;\r\n               materialPriceInput.addEventListener('input', (event) => {\r\n                   material.price = event.target.value;\r\n               });\r\n               materialPriceRow.appendChild(materialPriceInput)\r\n               budgetDetailItem.appendChild(materialPriceRow)\r\n               const materialBudgetPriceRow = document.createElement('div')\r\n               materialBudgetPriceRow.style.display = 'flex'\r\n               materialBudgetPriceRow.style.flexDirection = 'row'\r\n               materialBudgetPriceRow.style.gap = '80px'\r\n               const materialBudgetPriceLabel = document.createElement('label')\r\n               materialBudgetPriceLabel.textContent = 'سعر الصرف'\r\n               materialBudgetPriceRow.appendChild(materialBudgetPriceLabel)\r\n               const materialBudgetPriceInput = document.createElement('input')\r\n               materialBudgetPriceInput.type = 'text'\r\n               materialBudgetPriceInput.placeholder= 'أدخل سعر الصرف'\r\n               materialBudgetPriceInput.required = true;\r\n               materialBudgetPriceInput.addEventListener('input', (event) => {\r\n                   material.budgetPrice = event.target.value;\r\n               });\r\n               this.budgetDetailsList.push(material);\r\n               materialBudgetPriceRow.appendChild(materialBudgetPriceInput)\r\n               budgetDetailItem.appendChild(materialBudgetPriceRow)\r\n               activityBudgetDetailsList.appendChild(budgetDetailItem);\r\n            };\r\n            const AddActivityBudgetDetailsRecordButton = document.createElement('button');\r\n            AddActivityBudgetDetailsRecordButton.textContent = 'إضافة صرف'\r\n            AddActivityBudgetDetailsRecordButton.onclick = AddActivityBudgetDetailsRecord;\r\n            activityDiv.appendChild(AddActivityBudgetDetailsRecordButton);\r\n            const activityTotalBudgetLabel = document.createElement('label')\r\n            activityTotalBudgetLabel.textContent = 'مجموع الصرف الكلي'\r\n            activityDiv.appendChild(activityTotalBudgetLabel)\r\n            const totalBudgetDiv = document.createElement('div')\r\n            totalBudgetDiv.className = 'budget-input'\r\n            totalBudgetDiv.style = \r\n                'display: flex; flex-direction: row; gap: 40px; align-items: center;'\r\n            const totalBudgetInput = document.createElement('input')\r\n            totalBudgetInput.type = 'stat-number'\r\n            totalBudgetInput.placeholder = 'أدخل المجموع الكلي'\r\n            totalBudgetInput.required = true;\r\n            totalBudgetInput.addEventListener('input', (event) => {\r\n                this.activityTotalBudget = event.target.value;\r\n            });\r\n            totalBudgetInput.className = 'budget-input'\r\n            totalBudgetDiv.appendChild(totalBudgetInput)\r\n            totalBudgetDiv.appendChild(currencyTypeLabel)\r\n            activityDiv.appendChild(totalBudgetDiv)\r\n            const activityAppliedLevelsLabel = document.createElement('label')\r\n            activityAppliedLevelsLabel.textContent = 'مراحل تنفيذ النشاط'\r\n            activityDiv.appendChild(activityAppliedLevelsLabel)\r\n            const activityAppliedLevelsList = document.createElement('ul');\r\n            activityDiv.appendChild(activityAppliedLevelsList);\r\n            const AddActivityAppliedLevel = () => {\r\n                const appliedLevel = { description: '' };\r\n                const appliedLevelItem = document.createElement('li');\r\n                const appliedLevelInput = document.createElement('input');\r\n                appliedLevelInput.type = 'text';\r\n                appliedLevelInput.placeholder = 'وصف المرحلة التنفيذية';\r\n                appliedLevelInput.className = 'activity-input';\r\n                appliedLevelInput.required = true;\r\n                appliedLevelInput.addEventListener('input', (event) => {\r\n                    appliedLevel.description = event.target.value;\r\n                });\r\n                this.activityLevels.push(appliedLevel);\r\n                appliedLevelItem.appendChild(appliedLevelInput);\r\n                activityAppliedLevelsList.appendChild(appliedLevelItem);\r\n            };\r\n            const activityAppliedLevelAddButton = document.createElement('button');\r\n            activityAppliedLevelAddButton.textContent = 'إضافة مرحلة تنفيذ';\r\n            activityAppliedLevelAddButton.onclick = AddActivityAppliedLevel;\r\n            activityDiv.appendChild(activityAppliedLevelAddButton)\r\n            const activityStateLabel = document.createElement('label');\r\n            activityStateLabel.textContent = 'حالة النشاط';\r\n            activityDiv.appendChild(activityStateLabel);\r\n            const activityApplyState = document.createElement('select');\r\n            activityApplyState.className = 'activity-input';\r\n            activityApplyState.required = true;\r\n\r\n            // Add default option\r\n            const defaultOption = document.createElement('option');\r\n            defaultOption.value = '';\r\n            defaultOption.textContent = 'اختر حالة النشاط';\r\n            defaultOption.disabled = true;\r\n            defaultOption.selected = true;\r\n            activityApplyState.appendChild(defaultOption);\r\n\r\n            const states = [\r\n                'منفذ بصرف',  'منفذ بدون صرف', 'مقبول', 'مرفوض',\r\n                'يحتاج تعديل', 'صرف و لم ينفذ', 'مقبول دون صرف', 'مرسل'\r\n            ];\r\n            states.forEach(state => {\r\n                const option = document.createElement('option');\r\n                option.value = state;\r\n                option.textContent = state;\r\n                activityApplyState.appendChild(option);\r\n            });\r\n            activityApplyState.addEventListener('change', (event) => {\r\n                this.activityStatus = event.target.value;\r\n                // Update the filledActivity object when status changes\r\n                this.filledActivity = {\r\n                    title: this.activityTitleName,\r\n                    owner: this.activityOwnerName,\r\n                    description: this.activityDescription,\r\n                    date: this.activityDateInput,\r\n                    idea: this.activityIdeaInput,\r\n                    goals: this.activityGoals,\r\n                    audience: this.targetGroups,\r\n                    status: this.activityStatus,\r\n                    governorate: this.activityGovernorate,\r\n                    coordinator: this.coordinatorName\r\n                };\r\n            });\r\n            const activityDeleteButton = document.createElement('button');\r\n            activityDeleteButton.className = 'activity-delete-button';\r\n            activityDeleteButton.textContent = 'حذف النشاط';\r\n            activityDeleteButton.onclick = () => {\r\n                activityDiv.remove();\r\n            };\r\n            activityDiv.appendChild(activityApplyState);\r\n            activityDiv.appendChild(activityDeleteButton);\r\n            this.activities.push(activityDiv);\r\n            document.querySelector('.activities-list').appendChild(activityDiv);\r\n        },\r\n        handleClickOutside(event) {\r\n            const userSection = event.target.closest('.user-section');\r\n            if (!userSection) {\r\n                this.showUserMenu = false;\r\n            }\r\n        },\r\n        setCurrentView(view) {\r\n            this.currentView = view;\r\n            // Automatically fetch activities when switching to view tab\r\n            if (view === 'view') {\r\n                this.fetchMyActivities();\r\n            }\r\n        },\r\n        refreshActivities() {\r\n            this.fetchMyActivities();\r\n        },\r\n        async loadCoordinatorName() {\r\n            try {\r\n                const token = localStorage.getItem('ndyt_token');\r\n                if (!token) return;\r\n\r\n                const response = await fetch('/api/v1/ndyt-activities/coordinator', {\r\n                    headers: {\r\n                        'Authorization': `Bearer ${token}`,\r\n                        'Content-Type': 'application/json'\r\n                    }\r\n                });\r\n\r\n                if (response.ok) {\r\n                    const data = await response.json();\r\n                    this.coordinatorName = data.coordinator_name || '';\r\n                } else if (response.status === 404) {\r\n                    // No coordinator found for user's governorate\r\n                    this.coordinatorName = '';\r\n                }\r\n            } catch (error) {\r\n                console.error('Error loading coordinator name:', error);\r\n                // Don't show alert for this error as it's not critical\r\n            }\r\n        },\r\n        exportToCSV() {\r\n            if (this.filteredActivities.length === 0) {\r\n                this.$toast.error('لا توجد نشاطات للتصدير');\r\n                return;\r\n            }\r\n\r\n            // Define CSV headers in Arabic\r\n            const headers = [\r\n                'عنوان النشاط',\r\n                'صاحب النشاط', \r\n                'وصف النشاط',\r\n                'تاريخ النشاط',\r\n                'حالة النشاط',\r\n                'المحافظة',\r\n                'منسق المحافظة',\r\n                'تاريخ الإرسال'\r\n            ];\r\n\r\n            // Convert activities data to CSV format\r\n            const csvData = this.filteredActivities.map(activity => {\r\n                return [\r\n                    `\"${activity.title || ''}\"`,\r\n                    `\"${activity.owner_name || ''}\"`,\r\n                    `\"${activity.short_description || ''}\"`,\r\n                    activity.activity_date ? new Date(activity.activity_date).toLocaleDateString('ar-EG') : '',\r\n                    `\"${activity.state || ''}\"`,\r\n                    `\"${activity.submission_info?.governorate || ''}\"`,\r\n                    `\"${activity.submission_info?.coordinator_name || ''}\"`,\r\n                    activity.submission_info?.created_at ? \r\n                    new Date(activity.submission_info.created_at).toLocaleDateString('ar-EG') : ''\r\n                ].join(',');\r\n            });\r\n\r\n            // Combine headers and data\r\n            const csvContent = [headers.join(','), ...csvData].join('\\n');\r\n\r\n            // Add BOM for proper Arabic text encoding in Excel\r\n            const BOM = '\\uFEFF';\r\n            const csvWithBOM = BOM + csvContent;\r\n\r\n            // Create and download the file\r\n            const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });\r\n            const link = document.createElement('a');\r\n            \r\n            if (link.download !== undefined) {\r\n                const url = URL.createObjectURL(blob);\r\n                link.setAttribute('href', url);\r\n                \r\n                // Generate filename with current date and filter info\r\n                const currentDate = new Date().toISOString().split('T')[0];\r\n                const filterText = this.selectedFilter ? `_${this.selectedFilter}` : '_جميع_النشاطات';\r\n                const filename = `نشاطات_الفريق_الوطني${filterText}_${currentDate}.csv`;\r\n                \r\n                link.setAttribute('download', filename);\r\n                link.style.visibility = 'hidden';\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                \r\n                this.$toast.success(`تم تصدير ${this.filteredActivities.length} نشاط بنجاح!`);\r\n            } else {\r\n                this.$toast.error('المتصفح لا يدعم تحميل الملفات');\r\n            }\r\n        },\r\n        // PIN Confirmation Methods\r\n        showPinConfirmationModal(action, activity, callback) {\r\n            this.pinConfirmationData = {\r\n                pin: '',\r\n                action: action,\r\n                activity: activity,\r\n                callback: callback\r\n            };\r\n            this.showPinConfirmation = true;\r\n            // Focus on PIN input after modal opens\r\n            this.$nextTick(() => {\r\n                const pinInput = document.getElementById('confirmPin');\r\n                if (pinInput) {\r\n                    pinInput.focus();\r\n                }\r\n            });\r\n        },\r\n        closePinConfirmation() {\r\n            this.showPinConfirmation = false;\r\n            this.pinConfirmationData = {\r\n                pin: '',\r\n                action: '',\r\n                activity: null,\r\n                callback: null\r\n            };\r\n        },\r\n        async confirmPinAction() {\r\n            const enteredPin = this.pinConfirmationData.pin;\r\n            const storedPin = localStorage.getItem('ndyt_team_pin');\r\n            \r\n            if (enteredPin !== storedPin) {\r\n                this.$toast.error('رمز الفريق الرقمي غير صحيح');\r\n                return;\r\n            }\r\n            \r\n            // Store callback before closing modal\r\n            const callback = this.pinConfirmationData.callback;\r\n            \r\n            // Close modal first\r\n            this.closePinConfirmation();\r\n            \r\n            // Execute the callback function after DOM update\r\n            if (callback) {\r\n                this.$nextTick(async () => {\r\n                    await callback();\r\n                });\r\n            }\r\n        },\r\n        downloadFile(file) {\r\n            try {\r\n                // Create a direct download link\r\n                const fileUrl = `/api/v1/ndyt-activities${file.file_url}`;\r\n                \r\n                // Create a temporary anchor element to trigger download\r\n                const link = document.createElement('a');\r\n                link.href = fileUrl;\r\n                link.download = file.file_name;\r\n                link.target = '_blank'; // Open in new tab as fallback\r\n                link.style.display = 'none';\r\n                \r\n                // Append to body, click, and remove\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                \r\n                this.$toast.success(`تم تحميل الملف: ${file.file_name}`);\r\n            } catch (error) {\r\n                console.error('Download error:', error);\r\n                this.$toast.error(`فشل في تحميل الملف: ${file.file_name}`);\r\n            }\r\n        },\r\n        getShortFileName(fileName) {\r\n            // Truncate long file names for display\r\n            if (fileName.length > 20) {\r\n                const extension = fileName.split('.').pop();\r\n                const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));\r\n                return nameWithoutExt.substring(0, 15) + '...' + '.' + extension;\r\n            }\r\n            return fileName;\r\n        },\r\n        openActivityModal(activity) {\r\n            this.selectedActivity = activity;\r\n            this.showActivityModal = true;\r\n            // Prevent body scrolling when modal is open\r\n            document.body.style.overflow = 'hidden';\r\n        },\r\n        closeActivityModal() {\r\n            this.showActivityModal = false;\r\n            this.selectedActivity = null;\r\n            // Restore body scrolling when modal is closed\r\n            document.body.style.overflow = 'auto';\r\n        },\r\n        formatDate(dateString) {\r\n            if (!dateString) return 'غير محدد';\r\n            const date = new Date(dateString);\r\n            return date.toLocaleDateString('ar-EG', {\r\n                year: 'numeric',\r\n                month: 'long',\r\n                day: 'numeric'\r\n            });\r\n        },\r\n        formatTime(timeString) {\r\n            if (!timeString) return 'غير محدد';\r\n            return timeString;\r\n        },\r\n        formatCurrency(amount) {\r\n            if (!amount) return 'غير محدد';\r\n            return new Intl.NumberFormat('ar-EG').format(amount) + ' د.ع';\r\n        },\r\n        async loadImageAsArrayBuffer(imagePath) {\r\n            try {\r\n                // Import the image as a module to get the correct URL\r\n                let imageUrl;\r\n                if (imagePath.includes('scy_logo')) {\r\n                    imageUrl = (await import('@/assets/scy_logo.jpg')).default;\r\n                } else if (imagePath.includes('ndyt_logo')) {\r\n                    imageUrl = (await import('@/assets/ndyt_logo.jpg')).default;\r\n                }\r\n\r\n                if (!imageUrl) throw new Error(`Could not resolve image: ${imagePath}`);\r\n\r\n                const response = await fetch(imageUrl);\r\n                if (!response.ok) throw new Error(`Failed to load image: ${imageUrl}`);\r\n                return await response.arrayBuffer();\r\n            } catch (error) {\r\n                console.error('Error loading image:', error);\r\n                return null;\r\n            }\r\n        },\r\n        async exportToDocx() {\r\n            if (!this.selectedActivity) return;\r\n\r\n            const activity = this.selectedActivity;\r\n\r\n            // Load logo images\r\n            const scyLogoBuffer = await this.loadImageAsArrayBuffer('scy_logo');\r\n            const ndytLogoBuffer = await this.loadImageAsArrayBuffer('ndyt_logo');\r\n\r\n            // Create document sections\r\n            const sections = [];\r\n\r\n            // Document header with logos and organization names\r\n            const headerParagraphs = [];\r\n\r\n            // Header with logos and organization names in one line\r\n            if (scyLogoBuffer && ndytLogoBuffer) {\r\n                // Create a table for proper logo and text alignment\r\n                const headerTable = new Table({\r\n                    width: {\r\n                        size: 100,\r\n                        type: WidthType.PERCENTAGE,\r\n                    },\r\n                    borders: {\r\n                        top: { style: BorderStyle.NONE },\r\n                        bottom: { style: BorderStyle.NONE },\r\n                        left: { style: BorderStyle.NONE },\r\n                        right: { style: BorderStyle.NONE },\r\n                        insideHorizontal: { style: BorderStyle.NONE },\r\n                        insideVertical: { style: BorderStyle.NONE },\r\n                    },\r\n                    rows: [\r\n                        new TableRow({\r\n                            children: [\r\n                                // SCY Logo\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [\r\n                                            new ImageRun({\r\n                                                data: scyLogoBuffer,\r\n                                                transformation: {\r\n                                                    width: 80,\r\n                                                    height: 80,\r\n                                                },\r\n                                            }),\r\n                                        ],\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                }),\r\n                                // Organization Names\r\n                                new TableCell({\r\n                                    children: [\r\n                                        new Paragraph({\r\n                                            children: [\r\n                                                new TextRun({\r\n                                                    text: \"المجلس الأعلى للشباب\",\r\n                                                    bold: true,\r\n                                                    size: 32,\r\n                                                    font: \"Arial\",\r\n                                                }),\r\n                                            ],\r\n                                            alignment: AlignmentType.CENTER,\r\n                                            spacing: { after: 200 },\r\n                                            bidirectional: true,\r\n                                        }),\r\n                                        new Paragraph({\r\n                                            children: [\r\n                                                new TextRun({\r\n                                                    text: \"الفريق الوطني للشباب الرقمي\",\r\n                                                    bold: true,\r\n                                                    size: 28,\r\n                                                    font: \"Arial\",\r\n                                                }),\r\n                                            ],\r\n                                            alignment: AlignmentType.CENTER,\r\n                                            spacing: { after: 200 },\r\n                                            bidirectional: true,\r\n                                        }),\r\n                                    ],\r\n                                    width: { size: 60, type: WidthType.PERCENTAGE },\r\n                                }),\r\n                                // NDYT Logo\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [\r\n                                            new ImageRun({\r\n                                                data: ndytLogoBuffer,\r\n                                                transformation: {\r\n                                                    width: 80,\r\n                                                    height: 80,\r\n                                                },\r\n                                            }),\r\n                                        ],\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                }),\r\n                            ],\r\n                        }),\r\n                    ],\r\n                });\r\n\r\n                headerParagraphs.push(headerTable);\r\n            } else {\r\n                // Fallback if images don't load\r\n                headerParagraphs.push(\r\n                    new Paragraph({\r\n                        children: [\r\n                            new TextRun({\r\n                                text: \"المجلس الأعلى للشباب\",\r\n                                bold: true,\r\n                                size: 32,\r\n                                font: \"Arial\",\r\n                            }),\r\n                        ],\r\n                        alignment: AlignmentType.CENTER,\r\n                        spacing: { after: 200 },\r\n                        bidirectional: true,\r\n                    }),\r\n                    new Paragraph({\r\n                        children: [\r\n                            new TextRun({\r\n                                text: \"الفريق الوطني للشباب الرقمي\",\r\n                                bold: true,\r\n                                size: 28,\r\n                                font: \"Arial\",\r\n                            }),\r\n                        ],\r\n                        alignment: AlignmentType.CENTER,\r\n                        spacing: { after: 400 },\r\n                        bidirectional: true,\r\n                    })\r\n                );\r\n            }\r\n\r\n            // Activity title\r\n            headerParagraphs.push(\r\n                new Paragraph({\r\n                    children: [\r\n                        new TextRun({\r\n                            text: activity.title,\r\n                            bold: true,\r\n                            size: 36,\r\n                            underline: {},\r\n                            font: \"Arial\",\r\n                        }),\r\n                    ],\r\n                    alignment: AlignmentType.CENTER,\r\n                    spacing: { before: 400, after: 600 },\r\n                    bidirectional: true,\r\n                })\r\n            );\r\n\r\n            // Activity details table\r\n            const detailsTable = new Table({\r\n                width: {\r\n                    size: 100,\r\n                    type: WidthType.PERCENTAGE,\r\n                },\r\n                borders: {\r\n                    top: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                    bottom: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                    left: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                    right: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                    insideHorizontal: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                    insideVertical: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                },\r\n                rows: [\r\n                    // Basic information rows\r\n                    this.createTableRow(\"الاسم\", `الفريق الوطني للشباب الرقمي - ${activity.governorate} - ${activity.owner_name}`),\r\n                    this.createTableRow(\"اسم النشاط\", activity.title),\r\n                    ...(activity.activity_idea ? [this.createTableRow(\"فكرة النشاط\", activity.activity_idea)] : []),\r\n                    ...(activity.activity_goals && activity.activity_goals.length > 0 ?\r\n                        [this.createTableRow(\"أهداف النشاط\", activity.activity_goals.map(g => `• ${g.text}`).join('\\n'))] : []),\r\n                    ...(activity.target_groups && activity.target_groups.length > 0 ?\r\n                        [this.createTableRow(\"الفئة المستهدفة\", activity.target_groups.map(g => g.text).join(', '))] : []),\r\n                    ...(activity.audience_count ? [this.createTableRow(\"عدد المستهدفين\", `${activity.audience_count} شخص`)] : []),\r\n                    this.createTableRow(\"تاريخ النشاط\", this.formatDate(activity.activity_date)),\r\n                    ...(activity.activity_time ? [this.createTableRow(\"وقت النشاط\", this.formatTime(activity.activity_time))] : []),\r\n                    ...(activity.activity_location ? [this.createTableRow(\"مكان النشاط\", activity.activity_location)] : []),\r\n                    ...(activity.activity_duration ? [this.createTableRow(\"مدة النشاط\", activity.activity_duration)] : []),\r\n                    ...(activity.activity_budget ? [this.createTableRow(\"ميزانية النشاط\", this.formatCurrency(activity.activity_budget))] : []),\r\n                    ...(activity.activity_levels && activity.activity_levels.length > 0 ?\r\n                        [this.createTableRow(\"مراحل تنفيذ النشاط\", activity.activity_levels.map(l => l.description).join('\\n'))] : []),\r\n                    this.createTableRow(\"حالة النشاط\", activity.state),\r\n                    this.createTableRow(\"المحافظة\", activity.governorate),\r\n                    this.createTableRow(\"منسق المحافظة\", activity.coordinator_name),\r\n                    this.createTableRow(\"تاريخ الإرسال\", this.formatDate(activity.created_at)),\r\n                ],\r\n            });\r\n\r\n            sections.push(...headerParagraphs, detailsTable);\r\n\r\n            // Budget details table if exists\r\n            if (activity.budget_details && activity.budget_details.length > 0) {\r\n                sections.push(\r\n                    new Paragraph({\r\n                        children: [\r\n                            new TextRun({\r\n                                text: \"تفاصيل الصرف\",\r\n                                bold: true,\r\n                                size: 28,\r\n                                font: \"Arial\",\r\n                            }),\r\n                        ],\r\n                        spacing: { before: 400, after: 200 },\r\n                        bidirectional: true,\r\n                        alignment: AlignmentType.CENTER,\r\n                    })\r\n                );\r\n\r\n                const budgetTable = new Table({\r\n                    width: {\r\n                        size: 100,\r\n                        type: WidthType.PERCENTAGE,\r\n                    },\r\n                    borders: {\r\n                        top: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                        bottom: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                        left: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                        right: { style: BorderStyle.SINGLE, size: 4, color: \"000000\" },\r\n                        insideHorizontal: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                        insideVertical: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                    },\r\n                    rows: [\r\n                        // Header row\r\n                        new TableRow({\r\n                            children: [\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"المادة\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                    shading: {\r\n                                        fill: \"4472C4\", // Blue header background\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                    },\r\n                                    margins: {\r\n                                        top: 100,\r\n                                        bottom: 100,\r\n                                        left: 100,\r\n                                        right: 100,\r\n                                    },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"النوع\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                    shading: { fill: \"4472C4\" },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"العدد\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                    shading: { fill: \"4472C4\" },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"السعر\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                    shading: { fill: \"4472C4\" },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"المجموع\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    width: { size: 20, type: WidthType.PERCENTAGE },\r\n                                    shading: { fill: \"4472C4\" },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 2, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                            ],\r\n                        }),\r\n                        // Data rows\r\n                        ...activity.budget_details.map((detail, index) => new TableRow({\r\n                            children: [\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: detail.name || '',\r\n                                            font: \"Arial\",\r\n                                            size: 22,\r\n                                            color: \"000000\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\", // Alternating row colors\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                    },\r\n                                    margins: { top: 80, bottom: 80, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: detail.type || '',\r\n                                            font: \"Arial\",\r\n                                            size: 22,\r\n                                            color: \"000000\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\",\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                    },\r\n                                    margins: { top: 80, bottom: 80, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: detail.amount?.toString() || '0',\r\n                                            font: \"Arial\",\r\n                                            size: 22,\r\n                                            color: \"000000\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\",\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                    },\r\n                                    margins: { top: 80, bottom: 80, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: this.formatCurrency(detail.price),\r\n                                            font: \"Arial\",\r\n                                            size: 22,\r\n                                            color: \"000000\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\",\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                    },\r\n                                    margins: { top: 80, bottom: 80, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: this.formatCurrency(detail.budgetPrice),\r\n                                            font: \"Arial\",\r\n                                            size: 22,\r\n                                            color: \"000000\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: index % 2 === 0 ? \"F2F2F2\" : \"FFFFFF\",\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 1, color: \"666666\" },\r\n                                    },\r\n                                    margins: { top: 80, bottom: 80, left: 100, right: 100 },\r\n                                }),\r\n                            ],\r\n                        })),\r\n                        // Total row if total budget exists\r\n                        ...(activity.total_budget ? [new TableRow({\r\n                            children: [\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: \"الإجمالي\",\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    columnSpan: 4,\r\n                                    shading: {\r\n                                        fill: \"2F5597\", // Dark blue for total row\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                                new TableCell({\r\n                                    children: [new Paragraph({\r\n                                        children: [new TextRun({\r\n                                            text: this.formatCurrency(activity.total_budget),\r\n                                            bold: true,\r\n                                            font: \"Arial\",\r\n                                            size: 24,\r\n                                            color: \"FFFFFF\",\r\n                                        })],\r\n                                        bidirectional: true,\r\n                                        alignment: AlignmentType.CENTER,\r\n                                    })],\r\n                                    shading: {\r\n                                        fill: \"2F5597\", // Dark blue for total row\r\n                                    },\r\n                                    borders: {\r\n                                        top: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        bottom: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        left: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                        right: { style: BorderStyle.SINGLE, size: 3, color: \"000000\" },\r\n                                    },\r\n                                    margins: { top: 100, bottom: 100, left: 100, right: 100 },\r\n                                }),\r\n                            ],\r\n                        })] : []),\r\n                    ],\r\n                });\r\n\r\n                sections.push(budgetTable);\r\n            }\r\n\r\n            // Create document with RTL support\r\n            const doc = new Document({\r\n                sections: [{\r\n                    children: sections,\r\n                    properties: {\r\n                        page: {\r\n                            margin: {\r\n                                top: 1440,    // 1 inch\r\n                                right: 1440,  // 1 inch\r\n                                bottom: 1440, // 1 inch\r\n                                left: 1440,   // 1 inch\r\n                            },\r\n                        },\r\n                    },\r\n                }],\r\n                styles: {\r\n                    default: {\r\n                        document: {\r\n                            run: {\r\n                                font: \"Arial\",\r\n                                size: 24,\r\n                            },\r\n                            paragraph: {\r\n                                spacing: {\r\n                                    line: 276,\r\n                                },\r\n                            },\r\n                        },\r\n                    },\r\n                },\r\n            });\r\n\r\n            // Generate and save file\r\n            try {\r\n                const blob = await Packer.toBlob(doc);\r\n                const fileName = `نشاط_${activity.title.replace(/[^\\w\\s]/gi, '')}_${new Date().toISOString().split('T')[0]}.docx`;\r\n                saveAs(blob, fileName);\r\n\r\n                this.$toast.success('تم تصدير الملف بنجاح');\r\n            } catch (error) {\r\n                console.error('Error exporting DOCX:', error);\r\n                this.$toast.error('حدث خطأ أثناء تصدير الملف');\r\n            }\r\n        },\r\n        createTableRow(label, value) {\r\n            return new TableRow({\r\n                children: [\r\n                    // Value cell first (right side in RTL)\r\n                    new TableCell({\r\n                        children: [new Paragraph({\r\n                            children: [new TextRun({\r\n                                text: value || 'غير محدد',\r\n                                font: \"Arial\",\r\n                                size: 24,\r\n                                color: \"000000\",\r\n                            })],\r\n                            bidirectional: true,\r\n                            alignment: AlignmentType.RIGHT,\r\n                        })],\r\n                        width: { size: 70, type: WidthType.PERCENTAGE },\r\n                        shading: {\r\n                            fill: \"FFFFFF\", // White background for values\r\n                        },\r\n                        borders: {\r\n                            top: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            bottom: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            left: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            right: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                        },\r\n                        margins: {\r\n                            top: 100,\r\n                            bottom: 100,\r\n                            left: 150,\r\n                            right: 150,\r\n                        },\r\n                    }),\r\n                    // Label cell second (left side in RTL)\r\n                    new TableCell({\r\n                        children: [new Paragraph({\r\n                            children: [new TextRun({\r\n                                text: label,\r\n                                bold: true,\r\n                                font: \"Arial\",\r\n                                size: 24,\r\n                                color: \"000000\",\r\n                            })],\r\n                            bidirectional: true,\r\n                            alignment: AlignmentType.RIGHT,\r\n                        })],\r\n                        width: { size: 30, type: WidthType.PERCENTAGE },\r\n                        shading: {\r\n                            fill: \"E6E6FA\", // Light lavender background for labels\r\n                        },\r\n                        borders: {\r\n                            top: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            bottom: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            left: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                            right: { style: BorderStyle.SINGLE, size: 2, color: \"666666\" },\r\n                        },\r\n                        margins: {\r\n                            top: 100,\r\n                            bottom: 100,\r\n                            left: 150,\r\n                            right: 150,\r\n                        },\r\n                    }),\r\n                ],\r\n            });\r\n        },\r\n        async printActivity() {\r\n            if (!this.selectedActivity) return;\r\n\r\n            try {\r\n                // Get the image URLs from the current page\r\n                const scyLogoImg = document.querySelector('img[src*=\"scy_logo\"]');\r\n                const ndytLogoImg = document.querySelector('img[src*=\"ndyt_logo\"]');\r\n\r\n                const scyLogoSrc = scyLogoImg ? scyLogoImg.src : '';\r\n                const ndytLogoSrc = ndytLogoImg ? ndytLogoImg.src : '';\r\n\r\n                // Create a new window for printing\r\n                const printWindow = window.open('', '_blank');\r\n\r\n                // Generate the print content with correct image URLs\r\n                const printContent = this.generatePrintContent(this.selectedActivity, scyLogoSrc, ndytLogoSrc);\r\n\r\n                // Write the content to the new window\r\n                printWindow.document.write(printContent);\r\n                printWindow.document.close();\r\n\r\n                // Wait for images to load, then print\r\n                printWindow.onload = () => {\r\n                    // Wait a bit longer for images to fully load\r\n                    setTimeout(() => {\r\n                        printWindow.print();\r\n                        printWindow.close();\r\n                    }, 1000);\r\n                };\r\n            } catch (error) {\r\n                console.error('Error printing activity:', error);\r\n                this.$toast.error('حدث خطأ أثناء الطباعة');\r\n            }\r\n        },\r\n        generatePrintContent(activity, scyLogoSrc = '', ndytLogoSrc = '') {\r\n            return `\r\n<!DOCTYPE html>\r\n<html dir=\"rtl\" lang=\"ar\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>تفاصيل النشاط - ${activity.title}</title>\r\n    <style>\r\n        @page {\r\n            size: A4;\r\n            margin: 2cm;\r\n        }\r\n\r\n        * {\r\n            margin: 0;\r\n            padding: 0;\r\n            box-sizing: border-box;\r\n        }\r\n\r\n        body {\r\n            font-family: 'Arial', sans-serif;\r\n            line-height: 1.6;\r\n            color: #000;\r\n            background: white;\r\n            direction: rtl;\r\n        }\r\n\r\n        .document-header {\r\n            text-align: center;\r\n            margin-bottom: 30px;\r\n            border-bottom: 3px solid #000;\r\n            padding-bottom: 20px;\r\n        }\r\n\r\n        .logo-section {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            margin-bottom: 20px;\r\n        }\r\n\r\n        .logo-container {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            width: 120px;\r\n        }\r\n\r\n        .organization-logo {\r\n            width: 80px;\r\n            height: 80px;\r\n            border-radius: 50%;\r\n            border: 2px solid #333;\r\n            object-fit: cover;\r\n        }\r\n\r\n        .logo-label {\r\n            font-size: 12px;\r\n            margin-top: 5px;\r\n            color: #666;\r\n        }\r\n\r\n        .organization-info {\r\n            flex: 1;\r\n            text-align: center;\r\n        }\r\n\r\n        .organization-info h2 {\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n            margin-bottom: 10px;\r\n            color: #000;\r\n        }\r\n\r\n        .organization-info h3 {\r\n            font-size: 24px;\r\n            font-weight: bold;\r\n            color: #333;\r\n        }\r\n\r\n        .document-title h1 {\r\n            font-size: 32px;\r\n            font-weight: bold;\r\n            text-decoration: underline;\r\n            margin-top: 20px;\r\n            color: #000;\r\n        }\r\n\r\n        .details-table {\r\n            width: 100%;\r\n            border-collapse: collapse;\r\n            margin-top: 30px;\r\n        }\r\n\r\n        .detail-row {\r\n            display: table-row;\r\n        }\r\n\r\n        .detail-label {\r\n            display: table-cell;\r\n            background-color: #f0f0f0;\r\n            border: 2px solid #333;\r\n            padding: 12px 15px;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            width: 30%;\r\n            vertical-align: top;\r\n            text-align: right;\r\n        }\r\n\r\n        .detail-value {\r\n            display: table-cell;\r\n            border: 2px solid #333;\r\n            padding: 12px 15px;\r\n            font-size: 16px;\r\n            width: 70%;\r\n            vertical-align: top;\r\n            text-align: right;\r\n        }\r\n\r\n        .goals-list, .target-groups-list, .levels-list {\r\n            list-style: none;\r\n            padding: 0;\r\n        }\r\n\r\n        .goals-list li, .levels-list li {\r\n            margin-bottom: 5px;\r\n            padding-right: 20px;\r\n            position: relative;\r\n        }\r\n\r\n        .goals-list li:before {\r\n            content: \"•\";\r\n            position: absolute;\r\n            right: 0;\r\n            font-weight: bold;\r\n        }\r\n\r\n        .target-groups-list li {\r\n            display: inline-block;\r\n            margin-left: 10px;\r\n        }\r\n\r\n        .target-groups-list li:not(:last-child):after {\r\n            content: \"،\";\r\n        }\r\n\r\n        .status-badge {\r\n            padding: 6px 12px;\r\n            border-radius: 20px;\r\n            font-weight: bold;\r\n            font-size: 14px;\r\n        }\r\n\r\n        .status-executed-paid { background-color: #d4edda; color: #155724; }\r\n        .status-executed-unpaid { background-color: #fff3cd; color: #856404; }\r\n        .status-accepted { background-color: #cce5ff; color: #004085; }\r\n        .status-rejected { background-color: #f8d7da; color: #721c24; }\r\n        .status-needs-edit { background-color: #ffeaa7; color: #6c5ce7; }\r\n        .status-paid-not-executed { background-color: #fab1a0; color: #e17055; }\r\n        .status-accepted-unpaid { background-color: #a8e6cf; color: #00b894; }\r\n        .status-sent { background-color: #e0e0e0; color: #636e72; }\r\n\r\n        .budget-table {\r\n            width: 100%;\r\n            border-collapse: collapse;\r\n            margin-top: 10px;\r\n        }\r\n\r\n        .budget-table th {\r\n            background-color: #4472C4;\r\n            color: white;\r\n            border: 2px solid #000;\r\n            padding: 10px;\r\n            text-align: center;\r\n            font-weight: bold;\r\n        }\r\n\r\n        .budget-table td {\r\n            border: 1px solid #666;\r\n            padding: 8px;\r\n            text-align: center;\r\n        }\r\n\r\n        .budget-table tbody tr:nth-child(even) {\r\n            background-color: #f2f2f2;\r\n        }\r\n\r\n        .budget-table .total-row {\r\n            background-color: #2F5597 !important;\r\n            color: white;\r\n            font-weight: bold;\r\n        }\r\n\r\n        .budget-table .total-row td {\r\n            border: 3px solid #000;\r\n        }\r\n\r\n        @media print {\r\n            body {\r\n                -webkit-print-color-adjust: exact;\r\n                print-color-adjust: exact;\r\n            }\r\n\r\n            .no-print {\r\n                display: none !important;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"document-header\">\r\n        <div class=\"logo-section\">\r\n            <div class=\"logo-container\">\r\n                ${scyLogoSrc ? `<img src=\"${scyLogoSrc}\" alt=\"شعار المجلس الأعلى للشباب\" class=\"organization-logo\" />` : ''}\r\n                <span class=\"logo-label\">المجلس الأعلى للشباب</span>\r\n            </div>\r\n            <div class=\"organization-info\">\r\n                <h2>المجلس الأعلى للشباب</h2>\r\n                <h3>الفريق الوطني للشباب الرقمي</h3>\r\n            </div>\r\n            <div class=\"logo-container\">\r\n                ${ndytLogoSrc ? `<img src=\"${ndytLogoSrc}\" alt=\"شعار الفريق الوطني للشباب الرقمي\" class=\"organization-logo\" />` : ''}\r\n                <span class=\"logo-label\">الفريق الوطني للشباب الرقمي</span>\r\n            </div>\r\n        </div>\r\n        <div class=\"document-title\">\r\n            <h1>${activity.title}</h1>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"details-table\">\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">الاسم</div>\r\n            <div class=\"detail-value\">الفريق الوطني للشباب الرقمي - ${activity.governorate} - ${activity.owner_name}</div>\r\n        </div>\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">اسم النشاط</div>\r\n            <div class=\"detail-value\">${activity.title}</div>\r\n        </div>\r\n\r\n        ${activity.activity_idea ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">فكرة النشاط</div>\r\n            <div class=\"detail-value\">${activity.activity_idea}</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.activity_goals && activity.activity_goals.length > 0 ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">أهداف النشاط</div>\r\n            <div class=\"detail-value\">\r\n                <ul class=\"goals-list\">\r\n                    ${activity.activity_goals.map(goal => `<li>${goal.text}</li>`).join('')}\r\n                </ul>\r\n            </div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.target_groups && activity.target_groups.length > 0 ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">الفئة المستهدفة</div>\r\n            <div class=\"detail-value\">\r\n                <ul class=\"target-groups-list\">\r\n                    ${activity.target_groups.map(group => `<li>${group.text}</li>`).join('')}\r\n                </ul>\r\n            </div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.audience_count ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">عدد المستهدفين</div>\r\n            <div class=\"detail-value\">${activity.audience_count} شخص</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">تاريخ النشاط</div>\r\n            <div class=\"detail-value\">${this.formatDate(activity.activity_date)}</div>\r\n        </div>\r\n\r\n        ${activity.activity_time ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">وقت النشاط</div>\r\n            <div class=\"detail-value\">${this.formatTime(activity.activity_time)}</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.activity_location ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">مكان النشاط</div>\r\n            <div class=\"detail-value\">${activity.activity_location}</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.activity_duration ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">مدة النشاط</div>\r\n            <div class=\"detail-value\">${activity.activity_duration}</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.activity_budget ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">ميزانية النشاط</div>\r\n            <div class=\"detail-value\">${this.formatCurrency(activity.activity_budget)}</div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        ${activity.activity_levels && activity.activity_levels.length > 0 ? `\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">مراحل تنفيذ النشاط</div>\r\n            <div class=\"detail-value\">\r\n                <ul class=\"levels-list\">\r\n                    ${activity.activity_levels.map(level => `<li>${level.description}</li>`).join('')}\r\n                </ul>\r\n            </div>\r\n        </div>\r\n        ` : ''}\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">حالة النشاط</div>\r\n            <div class=\"detail-value\">\r\n                <span class=\"status-badge ${this.getStatusClass(activity.state)}\">\r\n                    ${activity.state}\r\n                </span>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">المحافظة</div>\r\n            <div class=\"detail-value\">${activity.governorate}</div>\r\n        </div>\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">منسق المحافظة</div>\r\n            <div class=\"detail-value\">${activity.coordinator_name}</div>\r\n        </div>\r\n\r\n        <div class=\"detail-row\">\r\n            <div class=\"detail-label\">تاريخ الإرسال</div>\r\n            <div class=\"detail-value\">${this.formatDate(activity.created_at)}</div>\r\n        </div>\r\n    </div>\r\n\r\n    ${activity.budget_details && activity.budget_details.length > 0 ? `\r\n    <div style=\"margin-top: 30px;\">\r\n        <h3 style=\"text-align: center; margin-bottom: 20px; font-size: 24px;\">تفاصيل الصرف</h3>\r\n        <table class=\"budget-table\">\r\n            <thead>\r\n                <tr>\r\n                    <th>المادة</th>\r\n                    <th>النوع</th>\r\n                    <th>العدد</th>\r\n                    <th>السعر</th>\r\n                    <th>المجموع</th>\r\n                </tr>\r\n            </thead>\r\n            <tbody>\r\n                ${activity.budget_details.map((detail, index) => `\r\n                <tr ${index % 2 === 0 ? 'style=\"background-color: #f2f2f2;\"' : ''}>\r\n                    <td>${detail.name || ''}</td>\r\n                    <td>${detail.type || ''}</td>\r\n                    <td>${detail.amount || ''}</td>\r\n                    <td>${this.formatCurrency(detail.price)}</td>\r\n                    <td>${this.formatCurrency(detail.budgetPrice)}</td>\r\n                </tr>\r\n                `).join('')}\r\n            </tbody>\r\n            ${activity.total_budget ? `\r\n            <tfoot>\r\n                <tr class=\"total-row\">\r\n                    <td colspan=\"4\"><strong>الإجمالي</strong></td>\r\n                    <td><strong>${this.formatCurrency(activity.total_budget)}</strong></td>\r\n                </tr>\r\n            </tfoot>\r\n            ` : ''}\r\n        </table>\r\n    </div>\r\n    ` : ''}\r\n</body>\r\n</html>\r\n            `;\r\n        }\r\n    },\r\n    mounted() {\r\n        // Get user info from localStorage\r\n        const userStr = localStorage.getItem('ndyt_user');\r\n        if (userStr) {\r\n            this.user = JSON.parse(userStr);\r\n        }\r\n        // Add click outside listener\r\n        document.addEventListener('click', this.handleClickOutside);\r\n        // Load coordinator name from previous submissions\r\n        this.loadCoordinatorName();\r\n    },\r\n    beforeUnmount() {\r\n        document.removeEventListener('click', this.handleClickOutside);\r\n        // Ensure body scroll is restored if component is destroyed while modal is open\r\n        document.body.style.overflow = 'auto';\r\n    }\r\n}\r\n</script>\r\n<template>\r\n    <div class=\"navbar\">\r\n        <div class=\"navbar-content\">\r\n            <div class=\"navbar-text\">\r\n                <span class=\"org-name\">المجلس الأعلى للشباب</span>\r\n                <img class=\"logo\" src=\"../assets/ndyt_logo.jpg\" alt=\"شعار الفريق\">\r\n                <span class=\"team-name\">الفريق الوطني للشباب الرقمي</span>\r\n            </div>\r\n            <div class=\"nav-actions\">\r\n                <button class=\"nav-btn\" :class=\"{ active: currentView === 'submit' }\" @click=\"setCurrentView('submit')\">\r\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                        <path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/>\r\n                    </svg>\r\n                    <span>إرسال النشاطات</span>\r\n                </button>\r\n                <button class=\"nav-btn\" :class=\"{ active: currentView === 'view' }\" @click=\"setCurrentView('view')\">\r\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                        <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\r\n                    </svg>\r\n                    <span>النشاطات المرسلة</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"user-section\">\r\n                <div class=\"user-button\" @click=\"toggleUserMenu\">\r\n                    <div class=\"user-avatar\">\r\n                        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\r\n                        </svg>\r\n                    </div>\r\n                    <div class=\"user-info\">\r\n                        <span class=\"user-name\">{{ user?.full_name || user?.username || 'المستخدم' }}</span>\r\n                        <svg class=\"dropdown-arrow\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M7 10l5 5 5-5z\"/>\r\n                        </svg>\r\n                    </div>\r\n                </div>\r\n                <div v-if=\"showUserMenu\" class=\"user-menu\">\r\n                    <div class=\"user-menu-item\" @click=\"openAccountSettings\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"/>\r\n                        </svg>\r\n                        <span>إعدادات الحساب</span>\r\n                    </div>\r\n                    <div v-if=\"user && user.rank === 'admin'\" class=\"user-menu-item\" @click=\"goToAdmin\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4V6C15 7.1 14.1 8 13 8H11C9.9 8 9 7.1 9 6V4L3 7V9H21ZM12 17.5L18 14.5V9H6V14.5L12 17.5Z\"/>\r\n                        </svg>\r\n                        <span>لوحة الإدارة</span>\r\n                    </div>\r\n                    <div class=\"user-menu-item\" @click=\"logout\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\"/>\r\n                        </svg>\r\n                        <span>تسجيل الخروج</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"main-content\">\r\n        <!-- Submit Activities View -->\r\n        <div v-if=\"currentView === 'submit'\" class=\"submit-view\">\r\n            <div class=\"view-header\">\r\n                <h2 class=\"view-title\">إرسال النشاطات الجديدة</h2>\r\n                <p class=\"view-description\">قم بإدخال المعلومات الأساسية ونشاطاتك لإرسالها للمراجعة</p>\r\n            </div>\r\n            <div class=\"base-info-container\">\r\n                <span class=\"base-info-label\">المعلومات الأساسية</span>\r\n                <label for=\"coordinator-name\" class=\"field-label\">منسق المحافظة</label>\r\n                <input type=\"text\" id=\"coordinator-name\" placeholder=\"اسم منسق المحافظة\" class=\"base-info-input\" v-model=\"coordinatorName\" disabled required>\r\n            </div>\r\n            \r\n            <div class=\"splitter\"></div>\r\n            \r\n            <div class=\"activities-info-container\">\r\n                <span class=\"base-info-label\">إضافة نشاطات جديدة</span>\r\n                <div class=\"activities-list\">\r\n                </div>\r\n                <button style=\"margin: 10px; max-width: 250px;\" @click=\"AddActivityItem\">\r\n                    <span>إضافة نشاط جديد</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"splitter\"></div>\r\n            <button style=\"margin: 0 50px; background-color: orange; max-width: 180px;\" @click=\"submitCV\">\r\n                <span>إرسال النشاطات</span>\r\n            </button>\r\n        </div>\r\n        \r\n        <!-- View Activities Section -->\r\n        <div v-if=\"currentView === 'view'\" class=\"view-activities\">\r\n            <div class=\"view-header\">\r\n                <div class=\"view-header-content\">\r\n                    <h2 class=\"view-title\">النشاطات</h2>\r\n                    <p class=\"view-description\">عرض وإدارة النشاطات حسب صلاحياتك (المدير: جميع النشاطات، المنسق: نشاطات المحافظة، العضو: نشاطاتك الشخصية)</p>\r\n                </div>\r\n                <div class=\"header-actions\">\r\n                    <button @click=\"exportToCSV\" class=\"export-btn\" :disabled=\"loadingActivities || filteredActivities.length === 0\" title=\"تصدير إلى Excel\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                            <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\r\n                        </svg>\r\n                        <span>تصدير CSV</span>\r\n                    </button>\r\n                    <button @click=\"refreshActivities\" class=\"refresh-btn\" :disabled=\"loadingActivities\">\r\n                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\" :class=\"{ 'spinning': loadingActivities }\">\r\n                            <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"/>\r\n                        </svg>\r\n                        <span>تحديث</span>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            \r\n            <div class=\"activities-container\">\r\n                <div v-if=\"loadingActivities\" class=\"loading-message\">\r\n                    <div class=\"loading-spinner\"></div>\r\n                    <span>جاري تحميل النشاطات...</span>\r\n                </div>\r\n                \r\n                <div v-else-if=\"myActivities.length === 0\" class=\"no-activities-message\">\r\n                    <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\" class=\"empty-icon\">\r\n                        <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\r\n                    </svg>\r\n                    <h3>لا توجد نشاطات</h3>\r\n                    <p>لا توجد نشاطات متاحة حسب صلاحياتك حالياً</p>\r\n                </div>\r\n                \r\n                <!-- Edit Form Modal -->\r\n                <div v-if=\"editingActivity\" class=\"edit-modal-overlay\" @click=\"cancelEdit\">\r\n                    <div class=\"edit-modal\" @click.stop>\r\n                        <div class=\"edit-modal-header\">\r\n                            <h3>تعديل النشاط</h3>\r\n                            <button @click=\"cancelEdit\" class=\"close-btn\">\r\n                                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                    <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\r\n                                </svg>\r\n                            </button>\r\n                        </div>\r\n                        <div class=\"edit-form\">\r\n                            <div class=\"form-row\">\r\n                                <div class=\"form-group\">\r\n                                    <label>اسم صاحب النشاط:</label>\r\n                                    <input v-model=\"editingActivity.owner_name\" type=\"text\" class=\"form-input\">\r\n                                </div>\r\n                                <div class=\"form-group\">\r\n                                    <label>عنوان النشاط:</label>\r\n                                    <input v-model=\"editingActivity.title\" type=\"text\" class=\"form-input\">\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"form-row\">\r\n                                <div class=\"form-group\">\r\n                                    <label>تاريخ النشاط:</label>\r\n                                    <input v-model=\"editingActivity.activity_date\" type=\"date\" class=\"form-input\">\r\n                                </div>\r\n                                <div class=\"form-group\">\r\n                                    <label>حالة النشاط:</label>\r\n                                    <select v-model=\"editingActivity.state\" class=\"form-select\">\r\n                                        <option value=\"مرسل\">مرسل</option>\r\n                                        <option value=\"منفذ بصرف\">منفذ بصرف</option>\r\n                                        <option value=\"منفذ بدون صرف\">منفذ بدون صرف</option>\r\n                                        <option value=\"مقبول\">مقبول</option>\r\n                                        <option value=\"مرفوض\">مرفوض</option>\r\n                                        <option value=\"يحتاج تعديل\">يحتاج تعديل</option>\r\n                                        <option value=\"صرف و لم ينفذ\">صرف و لم ينفذ</option>\r\n                                        <option value=\"مقبول دون صرف\">مقبول دون صرف</option>\r\n                                    </select>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"form-row\">\r\n                                <div class=\"form-group full-width\">\r\n                                    <label>وصف مختصر:</label>\r\n                                    <textarea v-model=\"editingActivity.short_description\" class=\"form-textarea\" rows=\"3\"></textarea>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"form-actions\">\r\n                                <button @click=\"saveActivity\" class=\"save-btn\">\r\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                        <path d=\"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\"/>\r\n                                    </svg>\r\n                                    حفظ التغييرات\r\n                                </button>\r\n                                <button @click=\"cancelEdit\" class=\"cancel-btn\">\r\n                                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                        <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\r\n                                    </svg>\r\n                                    إلغاء\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                \r\n                <!-- Activities Table -->\r\n                <div v-else-if=\"myActivities.length > 0\" class=\"activities-table-container\">\r\n                    <div class=\"table-header\">\r\n                        <div class=\"table-stats\">\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === null }\" @click=\"selectFilter(null)\">\r\n                                <span class=\"stat-number\">{{ myActivities.length }}</span>\r\n                                <span class=\"stat-label\">إجمالي النشاطات</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'منفذ بصرف' }\" @click=\"selectFilter('منفذ بصرف')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'منفذ بصرف').length }}</span>\r\n                                <span class=\"stat-label\">منفذ بصرف</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'منفذ بدون صرف' }\" @click=\"selectFilter('منفذ بدون صرف')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'منفذ بدون صرف').length }}</span>\r\n                                <span class=\"stat-label\">منفذ بدون صرف</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مقبول' }\" @click=\"selectFilter('مقبول')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مقبول').length }}</span>\r\n                                <span class=\"stat-label\">مقبول</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مرفوض' }\" @click=\"selectFilter('مرفوض')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مرفوض').length }}</span>\r\n                                <span class=\"stat-label\">مرفوض</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'يحتاج تعديل' }\" @click=\"selectFilter('يحتاج تعديل')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'يحتاج تعديل').length }}</span>\r\n                                <span class=\"stat-label\">يحتاج تعديل</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'صرف و لم ينفذ' }\" @click=\"selectFilter('صرف و لم ينفذ')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'صرف و لم ينفذ').length }}</span>\r\n                                <span class=\"stat-label\">صرف و لم ينفذ</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مقبول دون صرف' }\" @click=\"selectFilter('مقبول دون صرف')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مقبول دون صرف').length }}</span>\r\n                                <span class=\"stat-label\">مقبول دون صرف</span>\r\n                            </div>\r\n                            <div class=\"stat-item clickable\" :class=\"{ selected: selectedFilter === 'مرسل' }\" @click=\"selectFilter('مرسل')\">\r\n                                <span class=\"stat-number\">{{ myActivities.filter(a => a.state === 'مرسل').length }}</span>\r\n                                <span class=\"stat-label\">مرسل</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div class=\"table-wrapper\">\r\n                        <table class=\"activities-table\">\r\n                            <thead>\r\n                                <tr>\r\n                                    <th class=\"col-title\">عنوان النشاط</th>\r\n                                    <th class=\"col-owner\">صاحب النشاط</th>\r\n                                    <th class=\"col-date\">تاريخ النشاط</th>\r\n                                    <th class=\"col-status\">الحالة</th>\r\n                                    <th class=\"col-governorate\">المحافظة</th>\r\n                                    <th class=\"col-coordinator\">المنسق</th>\r\n                                    <th class=\"col-actions\">الإجراءات</th>\r\n                                </tr>\r\n                            </thead>\r\n                            <tbody>\r\n                                <tr v-for=\"activity in filteredActivities\" :key=\"activity.id\" class=\"activity-row clickable-row\" @click=\"openActivityModal(activity)\">\r\n                                    <td class=\"col-title\">\r\n                                        <div class=\"activity-title\">\r\n                                            <h4>{{ activity.title }}</h4>\r\n                                            <p class=\"activity-description\">{{ activity.short_description || 'لا يوجد وصف' }}</p>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td class=\"col-owner\">\r\n                                        <div class=\"owner-info\">\r\n                                            <span>{{ activity.owner_name }}</span>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td class=\"col-date\">\r\n                                        <div class=\"date-info\">\r\n                                            <span class=\"date-main\">{{ new Date(activity.activity_date).toLocaleDateString('ar-EG', { day: 'numeric', month: 'short' }) }}</span>\r\n                                            <span class=\"date-year\">{{ new Date(activity.activity_date).getFullYear() }}</span>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td class=\"col-status\">\r\n                                        <span class=\"status-badge\" :class=\"getStatusClass(activity.state)\">\r\n                                            <div class=\"status-indicator\"></div>\r\n                                            {{ activity.state }}\r\n                                        </span>\r\n                                    </td>\r\n                                    <td class=\"col-governorate\">\r\n                                        <span class=\"governorate-name\">{{ activity.submission_info.governorate }}</span>\r\n                                    </td>\r\n                                    <td class=\"col-coordinator\">\r\n                                        <span class=\"coordinator-name\">{{ activity.submission_info.coordinator_name }}</span>\r\n                                    </td>\r\n                                    <td class=\"col-actions\" @click.stop>\r\n                                        <div class=\"action-buttons\">\r\n                                            <button @click=\"editActivity(activity)\" class=\"action-btn edit-btn\" title=\"تعديل\">\r\n                                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                                    <path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/>\r\n                                                </svg>\r\n                                            </button>\r\n                                            <button @click=\"deleteActivity(activity.id)\" class=\"action-btn delete-btn\" title=\"حذف\">\r\n                                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                                                    <path d=\"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z\"/>\r\n                                                </svg>\r\n                                            </button>\r\n                                        </div>\r\n                                    </td>\r\n                                </tr>\r\n                            </tbody>\r\n                        </table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- Account Settings Modal -->\r\n    <div v-if=\"showAccountSettings\" class=\"modal-overlay\" @click=\"closeAccountSettings\">\r\n        <div class=\"modal-content account-settings-modal\" @click.stop>\r\n            <div class=\"modal-header\">\r\n                <h3>إعدادات الحساب</h3>\r\n                <button @click=\"closeAccountSettings\" class=\"close-btn\">&times;</button>\r\n            </div>\r\n            \r\n            <form @submit.prevent=\"updateAccountSettings\" class=\"account-form\">\r\n                <div class=\"form-group\">\r\n                    <label for=\"fullName\">الاسم الكامل:</label>\r\n                    <input \r\n                        type=\"text\" \r\n                        id=\"fullName\" \r\n                        v-model=\"accountForm.fullName\" \r\n                        required \r\n                        class=\"form-input\"\r\n                        placeholder=\"أدخل اسمك الكامل\"\r\n                    >\r\n                </div>\r\n                \r\n                <div class=\"form-group\">\r\n                    <label for=\"teamPin\">رمز الفريق الرقمي:</label>\r\n                    <input \r\n                        type=\"text\" \r\n                        id=\"teamPin\" \r\n                        v-model=\"accountForm.teamPin\" \r\n                        class=\"form-input\"\r\n                        placeholder=\"أدخل رمز الفريق الرقمي\"\r\n                    >\r\n                </div>\r\n                \r\n                <div class=\"password-section\">\r\n                    <h4>تغيير كلمة المرور (اختياري)</h4>\r\n                    \r\n                    <div class=\"form-group\">\r\n                        <label for=\"currentPassword\">كلمة المرور الحالية:</label>\r\n                        <input \r\n                            type=\"password\" \r\n                            id=\"currentPassword\" \r\n                            v-model=\"accountForm.currentPassword\" \r\n                            class=\"form-input\"\r\n                            placeholder=\"أدخل كلمة المرور الحالية\"\r\n                        >\r\n                    </div>\r\n                    \r\n                    <div class=\"form-group\">\r\n                        <label for=\"newPassword\">كلمة المرور الجديدة:</label>\r\n                        <input \r\n                            type=\"password\" \r\n                            id=\"newPassword\" \r\n                            v-model=\"accountForm.newPassword\" \r\n                            class=\"form-input\"\r\n                            placeholder=\"أدخل كلمة المرور الجديدة (6 أحرف على الأقل)\"\r\n                        >\r\n                    </div>\r\n                    \r\n                    <div class=\"form-group\">\r\n                        <label for=\"confirmPassword\">تأكيد كلمة المرور الجديدة:</label>\r\n                        <input \r\n                            type=\"password\" \r\n                            id=\"confirmPassword\" \r\n                            v-model=\"accountForm.confirmPassword\" \r\n                            class=\"form-input\"\r\n                            placeholder=\"أعد إدخال كلمة المرور الجديدة\"\r\n                        >\r\n                    </div>\r\n                </div>\r\n                \r\n                <div class=\"form-actions\">\r\n                    <button type=\"button\" @click=\"closeAccountSettings\" class=\"cancel-btn\">إلغاء</button>\r\n                    <button type=\"submit\" :disabled=\"updatingAccount\" class=\"save-btn\">\r\n                        <span v-if=\"updatingAccount\">جاري الحفظ...</span>\r\n                        <span v-else>حفظ التغييرات</span>\r\n                    </button>\r\n                </div>\r\n            </form>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- PIN Confirmation Modal -->\r\n    <div v-if=\"showPinConfirmation\" class=\"modal-overlay\" @click=\"closePinConfirmation\">\r\n        <div class=\"modal-content pin-modal\" @click.stop>\r\n            <div class=\"modal-header\">\r\n                <h3>تأكيد العملية</h3>\r\n                <button @click=\"closePinConfirmation\" class=\"close-btn\">&times;</button>\r\n            </div>\r\n            <div class=\"modal-body\">\r\n                <p class=\"pin-message\">\r\n                    {{ pinConfirmationData.action === 'delete' ? 'لحذف هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' : 'لتعديل هذا النشاط، يرجى إدخال رمز الفريق الرقمي:' }}\r\n                </p>\r\n                <div class=\"form-group\">\r\n                    <label for=\"confirmPin\">رمز الفريق الرقمي:</label>\r\n                    <input \r\n                        type=\"password\" \r\n                        id=\"confirmPin\"\r\n                        v-model=\"pinConfirmationData.pin\" \r\n                        placeholder=\"أدخل رمز الفريق الرقمي\"\r\n                        @keyup.enter=\"confirmPinAction\"\r\n                        class=\"form-control\"\r\n                    >\r\n                </div>\r\n            </div>\r\n            <div class=\"modal-footer\">\r\n                <button @click=\"closePinConfirmation\" class=\"cancel-btn\">إلغاء</button>\r\n                <button @click=\"confirmPinAction\" class=\"confirm-btn\" :disabled=\"!pinConfirmationData.pin\">\r\n                    {{ pinConfirmationData.action === 'delete' ? 'حذف' : 'تعديل' }}\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- Activity Details Modal -->\r\n    <div v-if=\"showActivityModal && selectedActivity\" class=\"modal-overlay\" @click=\"closeActivityModal\">\r\n        <div class=\"modal-content activity-details-modal\" @click.stop>\r\n            <div class=\"modal-header\">\r\n                <h3>تفاصيل النشاط</h3>\r\n                <button @click=\"closeActivityModal\" class=\"close-btn\">&times;</button>\r\n            </div>\r\n\r\n            <div class=\"modal-body activity-document\">\r\n                <!-- Document Header -->\r\n                <div class=\"document-header\">\r\n                    <div class=\"logo-section\">\r\n                        <div class=\"logo-container\">\r\n                            <img src=\"@/assets/scy_logo.jpg\" alt=\"شعار المجلس الأعلى للشباب\" class=\"organization-logo\" />\r\n                            <span class=\"logo-label\">شعار المجلس</span>\r\n                        </div>\r\n                        <div class=\"organization-info\">\r\n                            <h2>المجلس الأعلى للشباب</h2>\r\n                            <h3>الفريق الوطني للشباب الرقمي</h3>\r\n                        </div>\r\n                        <div class=\"logo-container\">\r\n                            <img src=\"@/assets/ndyt_logo.jpg\" alt=\"شعار الفريق الوطني للشباب الرقمي\" class=\"organization-logo\" />\r\n                            <span class=\"logo-label\">شعار الفريق</span>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"document-title\">\r\n                        <h1>{{ selectedActivity.title }}</h1>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- Activity Details Table -->\r\n                <div class=\"details-table\">\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">الاسم</div>\r\n                        <div class=\"detail-value\">الفريق الوطني للشباب الرقمي - {{ selectedActivity.governorate }} - {{ selectedActivity.owner_name }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">اسم النشاط</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.title }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_idea\">\r\n                        <div class=\"detail-label\">فكرة النشاط</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.activity_idea }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_goals && selectedActivity.activity_goals.length > 0\">\r\n                        <div class=\"detail-label\">أهداف النشاط</div>\r\n                        <div class=\"detail-value\">\r\n                            <ul class=\"goals-list\">\r\n                                <li v-for=\"goal in selectedActivity.activity_goals\" :key=\"goal.text\">- {{ goal.text }}</li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.target_groups && selectedActivity.target_groups.length > 0\">\r\n                        <div class=\"detail-label\">الفئة المستهدفة</div>\r\n                        <div class=\"detail-value\">\r\n                            <ul class=\"target-groups-list\">\r\n                                <li v-for=\"group in selectedActivity.target_groups\" :key=\"group.text\">{{ group.text }}</li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.audience_count\">\r\n                        <div class=\"detail-label\">عدد المستهدفين</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.audience_count }} شخص</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">تاريخ النشاط</div>\r\n                        <div class=\"detail-value\">{{ formatDate(selectedActivity.activity_date) }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_time\">\r\n                        <div class=\"detail-label\">وقت النشاط</div>\r\n                        <div class=\"detail-value\">{{ formatTime(selectedActivity.activity_time) }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_location\">\r\n                        <div class=\"detail-label\">مكان النشاط</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.activity_location }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_duration\">\r\n                        <div class=\"detail-label\">مدة النشاط</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.activity_duration }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_budget\">\r\n                        <div class=\"detail-label\">ميزانية النشاط</div>\r\n                        <div class=\"detail-value\">{{ formatCurrency(selectedActivity.activity_budget) }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.budget_details && selectedActivity.budget_details.length > 0\">\r\n                        <div class=\"detail-label\">تفاصيل الصرف</div>\r\n                        <div class=\"detail-value\">\r\n                            <table class=\"budget-table\">\r\n                                <thead>\r\n                                    <tr>\r\n                                        <th>المادة</th>\r\n                                        <th>النوع</th>\r\n                                        <th>العدد</th>\r\n                                        <th>السعر</th>\r\n                                        <th>المجموع</th>\r\n                                    </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                    <tr v-for=\"detail in selectedActivity.budget_details\" :key=\"detail.name\">\r\n                                        <td>{{ detail.name }}</td>\r\n                                        <td>{{ detail.type }}</td>\r\n                                        <td>{{ detail.amount }}</td>\r\n                                        <td>{{ formatCurrency(detail.price) }}</td>\r\n                                        <td>{{ formatCurrency(detail.budgetPrice) }}</td>\r\n                                    </tr>\r\n                                </tbody>\r\n                                <tfoot v-if=\"selectedActivity.total_budget\">\r\n                                    <tr class=\"total-row\">\r\n                                        <td colspan=\"4\"><strong>الإجمالي</strong></td>\r\n                                        <td><strong>{{ formatCurrency(selectedActivity.total_budget) }}</strong></td>\r\n                                    </tr>\r\n                                </tfoot>\r\n                            </table>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\" v-if=\"selectedActivity.activity_levels && selectedActivity.activity_levels.length > 0\">\r\n                        <div class=\"detail-label\">مراحل تنفيذ النشاط</div>\r\n                        <div class=\"detail-value\">\r\n                            <ul class=\"levels-list\">\r\n                                <li v-for=\"level in selectedActivity.activity_levels\" :key=\"level.description\">{{ level.description }}</li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">حالة النشاط</div>\r\n                        <div class=\"detail-value\">\r\n                            <span class=\"status-badge\" :class=\"getStatusClass(selectedActivity.state)\">\r\n                                {{ selectedActivity.state }}\r\n                            </span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">المحافظة</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.governorate }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">منسق المحافظة</div>\r\n                        <div class=\"detail-value\">{{ selectedActivity.coordinator_name }}</div>\r\n                    </div>\r\n\r\n                    <div class=\"detail-row\">\r\n                        <div class=\"detail-label\">تاريخ الإرسال</div>\r\n                        <div class=\"detail-value\">{{ formatDate(selectedActivity.created_at) }}</div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"modal-footer\">\r\n                <button @click=\"printActivity\" class=\"print-btn\">\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\" style=\"margin-left: 8px;\">\r\n                        <path d=\"M18,3H6V7H18M19,12A1,1 0 0,1 18,11A1,1 0 0,1 19,10A1,1 0 0,1 20,11A1,1 0 0,1 19,12M16,19H8V14H16M19,8H5A3,3 0 0,0 2,11V17H6V21H18V17H22V11A3,3 0 0,0 19,8Z\"/>\r\n                    </svg>\r\n                    طباعة\r\n                </button>\r\n                <button @click=\"exportToDocx\" class=\"export-docx-btn\">\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\" style=\"margin-left: 8px;\">\r\n                        <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"/>\r\n                    </svg>\r\n                    تصدير كملف Word\r\n                </button>\r\n                <button @click=\"closeActivityModal\" class=\"close-modal-btn\">\r\n                    إغلاق\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<style>\r\nbody {\r\n    background-color: #121212;\r\n    color: #e0e0e0;\r\n    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\n.navbar {\r\n    background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n    border: 2px solid #3a3a5e;\r\n    border-radius: 12px;\r\n    margin: 16px;\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.navbar-content {\r\n    padding: 16px 24px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    flex-wrap: wrap;\r\n    gap: 16px;\r\n}\r\n\r\n.navbar-text {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20px;\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n    flex: 1;\r\n}\r\n\r\n.org-name, .team-name {\r\n    font-weight: 700;\r\n    font-size: clamp(16px, 3vw, 24px);\r\n    color: #f5f5f5;\r\n    text-align: center;\r\n    line-height: 1.4;\r\n}\r\n\r\n.logo {\r\n    height: clamp(50px, 8vw, 70px);\r\n    width: auto;\r\n    border: 2px solid #4a5568;\r\n    border-radius: 50%;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.logo:hover {\r\n    transform: scale(1.05);\r\n}\r\n\r\n.user-section {\r\n    position: relative;\r\n    z-index: 100;\r\n}\r\n\r\n.user-button {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 1px solid rgba(255, 255, 255, 0.3);\r\n    border-radius: 8px;\r\n    padding: 8px 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.user-button:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.user-avatar {\r\n    width: 32px;\r\n    height: 32px;\r\n    background: linear-gradient(135deg, #667eea, #764ba2);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n}\r\n\r\n.user-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.user-name {\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n    color: #f5f5f5;\r\n    white-space: nowrap;\r\n}\r\n\r\n.dropdown-arrow {\r\n    transition: transform 0.3s ease;\r\n    color: #a0aec0;\r\n}\r\n\r\n.user-button:hover .dropdown-arrow {\r\n    transform: rotate(180deg);\r\n}\r\n\r\n.user-menu {\r\n    position: absolute;\r\n    top: 100%;\r\n    right: auto;\r\n    left: 0;\r\n    margin-top: 8px;\r\n    background: #1a202c;\r\n    border: 2px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 8px;\r\n    min-width: 180px;\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);\r\n    overflow: hidden;\r\n    animation: slideDown 0.3s ease;\r\n}\r\n\r\n/* Navigation Buttons */\r\n.nav-buttons {\r\n    display: flex;\r\n    gap: 16px;\r\n    align-items: center;\r\n}\r\n\r\n/* Arabic RTL alignment for navbar actions */\r\n.nav-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.nav-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    min-width: 160px; /* unified width with global buttons */\r\n    justify-content: center;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 8px;\r\n    color: #ffffff;\r\n    font-weight: 700;\r\n    font-size: 14px;\r\n    letter-spacing: 0.2px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    text-decoration: none;\r\n}\r\n\r\n/* Compact button utility for small actions */\r\n.btn-compact {\r\n    min-width: 96px;\r\n    height: 40px;\r\n    padding: 8px 14px;\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n}\r\n\r\n.nav-btn:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    border-color: rgba(255, 255, 255, 0.3);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.nav-btn.active {\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    border-color: #4f46e5;\r\n    color: #ffffff;\r\n    box-shadow: 0 2px 12px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n.nav-btn.active:hover {\r\n    background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n/* Main Content */\r\n.main-content {\r\n    margin: 24px;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n}\r\n\r\n.submit-view {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 24px;\r\n}\r\n\r\n.submit-header {\r\n    text-align: center;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 12px;\r\n}\r\n\r\n.submit-header h2 {\r\n    color: #ffffff;\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    margin: 0;\r\n}\r\n\r\n/* View Activities */\r\n.view-activities {\r\n    margin-top: 32px;\r\n    padding: 0 8px;\r\n}\r\n\r\n.view-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 32px 24px;\r\n    background: linear-gradient(135deg, rgba(79, 70, 229, 0.15), rgba(124, 58, 237, 0.15));\r\n    border: 2px solid rgba(79, 70, 229, 0.3);\r\n    border-radius: 16px;\r\n    margin-bottom: 32px;\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.view-header::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 2px;\r\n    background: linear-gradient(90deg, #4f46e5, #7c3aed, #4f46e5);\r\n}\r\n\r\n.view-header-content {\r\n    text-align: center;\r\n    flex: 1;\r\n}\r\n\r\n.view-title {\r\n    color: #ffffff;\r\n    font-size: 28px;\r\n    font-weight: 700;\r\n    margin: 0 0 12px 0;\r\n    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.view-description {\r\n    color: #e2e8f0;\r\n    font-size: 16px;\r\n    margin: 0;\r\n    opacity: 0.9;\r\n}\r\n\r\n.header-actions {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n}\r\n\r\n.export-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    background: linear-gradient(135deg, #f59e0b, #d97706);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);\r\n    min-width: 120px;\r\n    height: auto;\r\n}\r\n\r\n.export-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #d97706, #b45309);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);\r\n}\r\n\r\n.export-btn:disabled {\r\n    opacity: 0.7;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n    background: linear-gradient(135deg, #6b7280, #4b5563);\r\n}\r\n\r\n.refresh-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    padding: 12px 20px;\r\n    background: linear-gradient(135deg, #10b981, #059669);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\r\n    min-width: 100px;\r\n    height: auto;\r\n}\r\n\r\n.refresh-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #059669, #047857);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);\r\n}\r\n\r\n.refresh-btn:disabled {\r\n    opacity: 0.7;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.refresh-btn svg.spinning {\r\n    animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n    from { transform: rotate(0deg); }\r\n    to { transform: rotate(360deg); }\r\n}\r\n\r\n.my-activities-container {\r\n    margin-top: 24px;\r\n}\r\n\r\n.activities-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));\r\n    gap: 24px;\r\n    margin-top: 24px;\r\n    padding: 0 4px;\r\n}\r\n\r\n.activity-card {\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 16px;\r\n    padding: 24px;\r\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n    position: relative;\r\n    overflow: hidden;\r\n}\r\n\r\n.activity-card::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 3px;\r\n    background: linear-gradient(90deg, #4f46e5, #7c3aed);\r\n    transform: scaleX(0);\r\n    transition: transform 0.4s ease;\r\n}\r\n\r\n.activity-card:hover {\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.08));\r\n    transform: translateY(-4px);\r\n    box-shadow: 0 8px 32px rgba(79, 70, 229, 0.2);\r\n    border-color: rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n.activity-card:hover::before {\r\n    transform: scaleX(1);\r\n}\r\n\r\n.activity-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    margin-bottom: 20px;\r\n    padding-bottom: 16px;\r\n    border-bottom: 2px solid rgba(79, 70, 229, 0.2);\r\n}\r\n\r\n.activity-header h4 {\r\n    color: #ffffff;\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    margin: 0;\r\n    line-height: 1.3;\r\n    flex: 1;\r\n    margin-right: 16px;\r\n}\r\n\r\n.activity-actions {\r\n    display: flex;\r\n    gap: 10px;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.edit-btn, .delete-btn {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 40px;\r\n    min-width: 120px;\r\n    padding: 10px 16px;\r\n    border-radius: 10px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    border: none;\r\n    text-align: center;\r\n}\r\n\r\n.edit-btn {\r\n    background: linear-gradient(135deg, #3182ce, #2c5aa0);\r\n    color: white;\r\n    box-shadow: 0 2px 8px rgba(49, 130, 206, 0.3);\r\n}\r\n\r\n.edit-btn:hover {\r\n    background: linear-gradient(135deg, #2c5aa0, #2a4d8a);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.4);\r\n}\r\n\r\n.delete-btn {\r\n    background: linear-gradient(135deg, #e53e3e, #c53030);\r\n    color: white;\r\n    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);\r\n}\r\n\r\n.delete-btn:hover {\r\n    background: linear-gradient(135deg, #c53030, #a02626);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);\r\n}\r\n\r\n.activity-details {\r\n    margin-top: 16px;\r\n}\r\n\r\n.activity-details p {\r\n    color: #e2e8f0;\r\n    font-size: 15px;\r\n    margin: 12px 0;\r\n    line-height: 1.6;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.activity-details strong {\r\n    color: #ffffff;\r\n    font-weight: 700;\r\n    min-width: 100px;\r\n    display: inline-block;\r\n}\r\n\r\n.status-badge {\r\n    padding: 6px 12px;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    color: #ffffff;\r\n    text-align: center;\r\n    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);\r\n    border: 1px solid rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n.no-activities-message, .loading-message {\r\n    text-align: center;\r\n    padding: 48px 24px;\r\n    color: #cbd5e0;\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));\r\n    border: 2px solid rgba(255, 255, 255, 0.1);\r\n    border-radius: 16px;\r\n    margin: 24px 0;\r\n}\r\n\r\n.loading-message {\r\n    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));\r\n    border-color: rgba(79, 70, 229, 0.2);\r\n    animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n    0%, 100% { opacity: 1; }\r\n    50% { opacity: 0.7; }\r\n}\r\n\r\n@keyframes slideDown {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(-10px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n.user-menu-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n    padding: 12px 16px;\r\n    cursor: pointer;\r\n    transition: background-color 0.2s ease;\r\n    color: #f5f5f5;\r\n    font-size: 14px;\r\n}\r\n\r\n.user-menu-item:hover {\r\n    background: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.user-menu-item svg {\r\n    color: #e53e3e;\r\n}\r\n\r\nspan {\r\n    font-weight: 600;\r\n    color: #f5f5f5;\r\n}\r\n\r\n.view {\r\n    padding: 16px;\r\n    background: linear-gradient(135deg, #1e1e2e, #2a2a3e);\r\n    border: 1px solid #3a3a4e;\r\n    border-radius: 20px;\r\n    max-width: 90%;\r\n    margin: 16px auto;\r\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\nbutton {\r\n    background: linear-gradient(135deg, #4f46e5, #7c3aed);\r\n    color: #ffffff;\r\n    height: 48px;\r\n    min-width: 160px;\r\n    border: none;\r\n    border-radius: 12px;\r\n    padding: 12px 24px;\r\n    cursor: pointer;\r\n    font-weight: 700;\r\n    font-size: 15px;\r\n    letter-spacing: 0.2px;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n/* Make icon+label buttons look consistent */\r\nbutton svg { flex-shrink: 0; }\r\nbutton span { display: inline-block; }\r\n\r\n/* Inputs: improve placeholder visibility */\r\ninput::placeholder,\r\nselect::placeholder,\r\ntextarea::placeholder {\r\n    color: #cbd5e1; /* brighter placeholder */\r\n    opacity: 1;\r\n}\r\n\r\n/* Edge/Firefox vendor prefixes */\r\ninput::-ms-input-placeholder { color: #cbd5e1; }\r\ninput::-webkit-input-placeholder { color: #cbd5e1; }\r\ntextarea::-webkit-input-placeholder { color: #cbd5e1; }\r\nselect::-ms-input-placeholder { color: #cbd5e1; }\r\n\r\nbutton:hover {\r\n    background: linear-gradient(135deg, #6366f1, #8b5cf6);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\nbutton:active {\r\n    transform: translateY(0);\r\n}\r\n\r\nbutton span {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n\r\n.info {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n    gap: 24px;\r\n    margin-top: 24px;\r\n}\r\n\r\n.base-info-container,\r\n.activities-info-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    direction: rtl;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 12px;\r\n    padding: 24px;\r\n}\r\n\r\n.base-info-label {\r\n    font-size: 18px;\r\n    font-weight: 700;\r\n    color: #f1f5f9;\r\n    margin-bottom: 16px;\r\n    text-align: right;\r\n    border-bottom: 2px solid #4f46e5;\r\n    padding-bottom: 8px;\r\n}\r\n\r\n.field-label {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #cbd5e1;\r\n    margin-bottom: 8px;\r\n    text-align: right;\r\n    display: block;\r\n}\r\n\r\ninput[type=\"text\"],\r\ninput[type=\"date\"],\r\ninput[type=\"time\"],\r\ninput[type='stat-number'],\r\ntextarea, select {\r\n    direction: rtl;\r\n    width: 100%;\r\n    max-width: 400px;\r\n    margin: 8px 0;\r\n    padding: 12px 16px;\r\n    background: rgba(255, 255, 255, 0.12);\r\n    color: #ffffff;\r\n    border: 2px solid rgba(255, 255, 255, 0.25);\r\n    border-radius: 8px;\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\ninput[type='stat-number'] {\r\n    width: 300px;\r\n}\r\n\r\ninput[type=\"text\"]:focus,\r\ninput[type=\"date\"]:focus,\r\nselect:focus {\r\n    outline: none;\r\n    border-color: #4f46e5;\r\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);\r\n    background: rgba(255, 255, 255, 0.18);\r\n    color: #ffffff;\r\n}\r\n\r\nselect {\r\n    cursor: pointer;\r\n}\r\n\r\nselect option {\r\n    background: #1a1a2e;\r\n    color: #f0f0f0;\r\n    padding: 8px;\r\n}\r\n.activity-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    margin-bottom: 16px;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.08);\r\n    border: 2px solid rgba(255, 255, 255, 0.15);\r\n    border-radius: 8px;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.activity-item:hover {\r\n    background: rgba(255, 255, 255, 0.12);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.activity-file-input {\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    border-radius: 12px;\r\n    width: fit-content;\r\n    max-width: 200px;\r\n    text-align: center;\r\n    padding: 12px 16px;\r\n    font-size: 14px;\r\n    background: rgba(255, 255, 255, 0.1);\r\n    color: #f0f0f0;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    backdrop-filter: blur(10px);\r\n    align-self: flex-start;\r\n}\r\n\r\n.activity-file-input:hover {\r\n    background: rgba(255, 255, 255, 0.15);\r\n    border-color: #4f46e5;\r\n}\r\n\r\n.activity-delete-button {\r\n    background: linear-gradient(135deg, #ef4444, #dc2626);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 12px;\r\n    margin: 8px 0;\r\n    padding: 10px 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    font-weight: 600;\r\n    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.activity-delete-button:hover {\r\n    background: linear-gradient(135deg, #f87171, #ef4444);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n    .navbar-content {\r\n        flex-direction: column;\r\n        text-align: center;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .navbar-text {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 18px;\r\n    }\r\n    \r\n    .user-button {\r\n        width: 100%;\r\n        justify-content: center;\r\n    }\r\n    \r\n    .info {\r\n        grid-template-columns: 1fr;\r\n        gap: 16px;\r\n    }\r\n    \r\n    .base-info-container,\r\n    .activities-info-container {\r\n        padding: 16px;\r\n    }\r\n    \r\n    .view {\r\n        margin: 8px;\r\n        padding: 12px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .navbar {\r\n        margin: 8px;\r\n    }\r\n    \r\n    .navbar-content {\r\n        padding: 12px 16px;\r\n    }\r\n    \r\n    .org-name, .team-name {\r\n        font-size: 16px;\r\n    }\r\n    \r\n    .logo {\r\n        height: 50px;\r\n    }\r\n    \r\n    .user-name {\r\n        font-size: 12px;\r\n    }\r\n    \r\n    input[type=\"text\"],\r\n    input[type=\"date\"],\r\n    select {\r\n        font-size: 16px; /* Prevents zoom on iOS */\r\n    }\r\n}\r\n\r\n/* RTL Improvements */\r\n.view {\r\n    direction: rtl;\r\n}\r\n\r\n.navbar-content {\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.navbar-text {\r\n    flex-direction: row-reverse;\r\n    text-align: right;\r\n}\r\n\r\n.nav-actions .nav-btn {\r\n    flex-direction: row-reverse;\r\n}\r\n\r\n.view-header-content,\r\n.view-title,\r\n.view-description {\r\n    text-align: right;\r\n}\r\n\r\n.activities-list {\r\n    direction: rtl;\r\n}\r\n\r\nlabel {\r\n    text-align: right;\r\n    font-weight: 600;\r\n    color: #cbd5e1;\r\n    margin-bottom: 4px;\r\n    display: block;\r\n}\r\n\r\n/* Inputs RTL */\r\ninput[type=\"text\"],\r\ninput[type=\"date\"],\r\nselect,\r\ntextarea {\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n/* Fine color and spacing tweaks for Arabic */\r\n.navbar {\r\n    border-color: #4b5563;\r\n}\r\n.nav-btn {\r\n    letter-spacing: 0.2px; /* tighter Arabic rhythm */\r\n}\r\n.view-header {\r\n    border-color: rgba(79, 70, 229, 0.35);\r\n}\r\n.activity-card {\r\n    padding-inline: 24px;\r\n}\r\n.activity-header h4 {\r\n    margin-left: 0;\r\n    margin-right: 15px; /* move spacing to the right for RTL */\r\n}\r\n.activity-details,\r\n.activity-details p {\r\n    text-align: right;\r\n}\r\n\r\n.splitter {\r\n    height: 2px;\r\n    background: linear-gradient(90deg, transparent, #4f46e5, transparent);\r\n    margin: 24px 0;\r\n    border-radius: 1px;\r\n}\r\n\r\n/* My Activities Section Styles */\r\n.my-activities-section {\r\n    margin: 30px 0;\r\n    direction: rtl;\r\n    text-align: right;\r\n}\r\n\r\n.toggle-activities-btn {\r\n    width: 100%;\r\n    padding: 15px 20px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    border: none;\r\n    border-radius: 12px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.toggle-activities-btn:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.toggle-icon {\r\n    font-size: 14px;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.my-activities-container {\r\n    margin-top: 20px;\r\n    padding: 20px;\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: 15px;\r\n    backdrop-filter: blur(10px);\r\n    border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.loading-message, .no-activities-message {\r\n    text-align: center;\r\n    padding: 40px 20px;\r\n    color: #8892b0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n}\r\n\r\n/* Activities Container */\r\n.activities-container {\r\n    margin-top: 20px;\r\n    padding: 0;\r\n}\r\n\r\n/* Loading and Empty States */\r\n.loading-message {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 60px 20px;\r\n    color: #8892b0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    gap: 16px;\r\n}\r\n\r\n.loading-spinner {\r\n    width: 40px;\r\n    height: 40px;\r\n    border: 3px solid rgba(255, 255, 255, 0.1);\r\n    border-top: 3px solid #4f46e5;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n    0% { transform: rotate(0deg); }\r\n    100% { transform: rotate(360deg); }\r\n}\r\n\r\n.no-activities-message {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 80px 20px;\r\n    text-align: center;\r\n    color: #8892b0;\r\n    gap: 16px;\r\n}\r\n\r\n.empty-icon {\r\n    opacity: 0.6;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.no-activities-message h3 {\r\n    margin: 0;\r\n    color: #e6f1ff;\r\n    font-size: 24px;\r\n    font-weight: 600;\r\n}\r\n\r\n.no-activities-message p {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    opacity: 0.8;\r\n}\r\n\r\n/* Edit Modal - Clean Flexbox Design */\r\n.edit-modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.75);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n    padding: 20px;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.edit-modal {\r\n    background: #1e293b;\r\n    border: 1px solid #334155;\r\n    border-radius: 12px;\r\n    width: 100%;\r\n    max-width: 600px;\r\n    max-height: calc(100vh - 40px);\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.edit-modal-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20px 24px;\r\n    border-bottom: 1px solid #334155;\r\n    background: #0f172a;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.edit-modal-header h3 {\r\n    margin: 0;\r\n    color: #f1f5f9;\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n}\r\n\r\n.close-btn {\r\n    background: none;\r\n    border: none;\r\n    color: #64748b;\r\n    cursor: pointer;\r\n    padding: 8px;\r\n    border-radius: 6px;\r\n    transition: all 0.2s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.close-btn:hover {\r\n    background: #334155;\r\n    color: #f1f5f9;\r\n}\r\n\r\n.edit-form {\r\n    padding: 24px;\r\n    direction: rtl;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    overflow-y: auto;\r\n    flex: 1;\r\n    min-height: 0;\r\n}\r\n\r\n.form-row {\r\n    display: flex;\r\n    gap: 16px;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.form-row.single {\r\n    flex-direction: column;\r\n}\r\n\r\n.form-row.double {\r\n    flex-direction: row;\r\n}\r\n\r\n.form-row.double .form-group {\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.form-group.full-width {\r\n    grid-column: 1 / -1;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n/* Table Styles */\r\n.activities-table-container {\r\n    background: rgba(255, 255, 255, 0.05);\r\n    border-radius: 16px;\r\n    border: 1px solid rgba(255, 255, 255, 0.1);\r\n    backdrop-filter: blur(10px);\r\n    overflow: hidden;\r\n}\r\n\r\n.table-header {\r\n    padding: 24px;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.table-stats {\r\n    display: flex;\r\n    gap: 32px;\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.stat-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 4px;\r\n}\r\n\r\n.stat-item.clickable {\r\n    cursor: pointer;\r\n    padding: 12px 16px;\r\n    border-radius: 12px;\r\n    transition: all 0.3s ease;\r\n    border: 2px solid transparent;\r\n}\r\n\r\n.stat-item.clickable:hover {\r\n    background: rgba(79, 70, 229, 0.1);\r\n    border-color: rgba(79, 70, 229, 0.3);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.2);\r\n}\r\n\r\n.stat-item.selected {\r\n    background: rgba(79, 70, 229, 0.2);\r\n    border-color: #4f46e5;\r\n    box-shadow: 0 4px 20px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n.stat-item.selected .stat-number {\r\n    color: #6366f1;\r\n}\r\n\r\n.stat-item.selected .stat-label {\r\n    color: #c7d2fe;\r\n}\r\n\r\n.stat-number {\r\n    font-size: 32px;\r\n    font-weight: 700;\r\n    color: #4f46e5;\r\n    line-height: 1;\r\n}\r\n\r\n.stat-label {\r\n    font-size: 14px;\r\n    color: #8892b0;\r\n    font-weight: 500;\r\n}\r\n\r\n.table-wrapper {\r\n    overflow-x: auto;\r\n}\r\n\r\n.activities-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    direction: rtl;\r\n}\r\n\r\n.activities-table th {\r\n    background: rgba(255, 255, 255, 0.08);\r\n    padding: 16px 12px;\r\n    text-align: right;\r\n    font-weight: 600;\r\n    color: #e6f1ff;\r\n    font-size: 14px;\r\n    border-bottom: 2px solid rgba(255, 255, 255, 0.1);\r\n    white-space: nowrap;\r\n}\r\n\r\n.activities-table td {\r\n    padding: 16px 12px;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\r\n    vertical-align: middle;\r\n}\r\n\r\n.activity-row {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.activity-row:hover {\r\n    background: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n/* Column Specific Styles */\r\n.col-title {\r\n    min-width: 250px;\r\n}\r\n\r\n.activity-title h4 {\r\n    margin: 0 0 4px 0;\r\n    color: #e6f1ff;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    line-height: 1.3;\r\n}\r\n\r\n.activity-description {\r\n    margin: 0;\r\n    color: #8892b0;\r\n    font-size: 13px;\r\n    line-height: 1.4;\r\n    display: -webkit-box;\r\n    line-clamp: 2;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n}\r\n\r\n.owner-info {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.date-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 2px;\r\n}\r\n\r\n.date-main {\r\n    color: #e6f1ff;\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n}\r\n\r\n.date-year {\r\n    color: #8892b0;\r\n    font-size: 12px;\r\n}\r\n\r\n.status-badge {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    padding: 6px 12px;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    white-space: nowrap;\r\n}\r\n\r\n.status-indicator {\r\n    width: 6px;\r\n    height: 6px;\r\n    border-radius: 50%;\r\n    background: currentColor;\r\n}\r\n\r\n/* Status Colors */\r\n.status-executed-paid {\r\n    background: rgba(34, 197, 94, 0.2);\r\n    color: #22c55e;\r\n    border: 1px solid rgba(34, 197, 94, 0.3);\r\n}\r\n\r\n.status-executed-unpaid {\r\n    background: rgba(59, 130, 246, 0.2);\r\n    color: #3b82f6;\r\n    border: 1px solid rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.status-accepted {\r\n    background: rgba(16, 185, 129, 0.2);\r\n    color: #10b981;\r\n    border: 1px solid rgba(16, 185, 129, 0.3);\r\n}\r\n\r\n.status-rejected {\r\n    background: rgba(239, 68, 68, 0.2);\r\n    color: #ef4444;\r\n    border: 1px solid rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.status-needs-edit {\r\n    background: rgba(245, 158, 11, 0.2);\r\n    color: #f59e0b;\r\n    border: 1px solid rgba(245, 158, 11, 0.3);\r\n}\r\n\r\n.status-paid-not-executed {\r\n    background: rgba(168, 85, 247, 0.2);\r\n    color: #a855f7;\r\n    border: 1px solid rgba(168, 85, 247, 0.3);\r\n}\r\n\r\n.status-accepted-unpaid {\r\n    background: rgba(6, 182, 212, 0.2);\r\n    color: #06b6d4;\r\n    border: 1px solid rgba(6, 182, 212, 0.3);\r\n}\r\n\r\n.status-sent {\r\n    background: rgba(139, 92, 246, 0.2);\r\n    color: #8b5cf6;\r\n    border: 1px solid rgba(139, 92, 246, 0.3);\r\n}\r\n\r\n.governorate-name, .coordinator-name {\r\n    color: #ccd6f6;\r\n    font-weight: 500;\r\n}\r\n\r\n.action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n    justify-content: center;\r\n}\r\n\r\n.action-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 36px;\r\n    height: 36px;\r\n    border: none;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn.edit-btn {\r\n    background: rgba(59, 130, 246, 0.2);\r\n    color: #3b82f6;\r\n    border: 1px solid rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.action-btn.edit-btn:hover {\r\n    background: rgba(59, 130, 246, 0.3);\r\n    transform: scale(1.1);\r\n}\r\n\r\n.action-btn.delete-btn {\r\n    background: rgba(239, 68, 68, 0.2);\r\n    color: #ef4444;\r\n    border: 1px solid rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n.action-btn.delete-btn:hover {\r\n    background: rgba(239, 68, 68, 0.3);\r\n    transform: scale(1.1);\r\n}\r\n\r\n/* File Download Styles */\r\n.files-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n    min-width: 120px;\r\n}\r\n\r\n.file-buttons {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n}\r\n\r\n.file-download-btn {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 6px;\r\n    padding: 6px 10px;\r\n    background: rgba(34, 197, 94, 0.15);\r\n    border: 1px solid rgba(34, 197, 94, 0.3);\r\n    border-radius: 6px;\r\n    color: #22c55e;\r\n    font-size: 12px;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    text-decoration: none;\r\n    min-height: 28px;\r\n    max-width: 140px;\r\n}\r\n\r\n.file-download-btn:hover {\r\n    background: rgba(34, 197, 94, 0.25);\r\n    border-color: rgba(34, 197, 94, 0.5);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);\r\n}\r\n\r\n.file-download-btn svg {\r\n    flex-shrink: 0;\r\n    color: #22c55e;\r\n}\r\n\r\n.file-name {\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    flex: 1;\r\n    min-width: 0;\r\n}\r\n\r\n.no-files {\r\n    color: #64748b;\r\n    font-size: 12px;\r\n    font-style: italic;\r\n    text-align: center;\r\n    padding: 8px;\r\n}\r\n\r\n@keyframes fadeIn {\r\n    from { opacity: 0; }\r\n    to { opacity: 1; }\r\n}\r\n\r\n@keyframes slideUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* Edit Form Styles */\r\n.edit-form {\r\n    direction: rtl;\r\n}\r\n\r\n.form-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.form-group label {\r\n    color: #cbd5e1;\r\n    font-weight: 500;\r\n    font-size: 14px;\r\n    margin-bottom: 0;\r\n    text-align: right;\r\n}\r\n\r\n.form-input, .form-textarea, .form-select {\r\n    width: 100%;\r\n    padding: 12px 16px;\r\n    border: 1px solid #475569;\r\n    border-radius: 6px;\r\n    background: #334155;\r\n    color: #f1f5f9;\r\n    font-size: 14px;\r\n    font-family: inherit;\r\n    transition: all 0.2s ease;\r\n    direction: rtl;\r\n    text-align: right;\r\n    box-sizing: border-box;\r\n    outline: none;\r\n}\r\n\r\n.form-input:focus, .form-textarea:focus, .form-select:focus {\r\n    border-color: #3b82f6;\r\n    background: #1e293b;\r\n    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);\r\n}\r\n\r\n.form-input::placeholder, .form-textarea::placeholder {\r\n    color: #94a3b8;\r\n}\r\n\r\n.form-select {\r\n    cursor: pointer;\r\n    appearance: none;\r\n    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 4 5\"><path fill=\"%23f1f5f9\" d=\"M2 0L0 2h4zm0 5L0 3h4z\"/></svg>');\r\n    background-repeat: no-repeat;\r\n    background-position: left 12px center;\r\n    background-size: 12px;\r\n    padding-left: 32px;\r\n}\r\n\r\n.form-group.full-width {\r\n    grid-column: 1 / -1;\r\n}\r\n\r\n.form-textarea {\r\n    min-height: 100px;\r\n    resize: vertical;\r\n    line-height: 1.5;\r\n}\r\n\r\n.form-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    margin-top: 24px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #475569;\r\n}\r\n\r\n.save-btn, .cancel-btn {\r\n    padding: 10px 20px;\r\n    border-radius: 6px;\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: all 0.2s ease;\r\n    border: 1px solid transparent;\r\n    outline: none;\r\n}\r\n\r\n.save-btn {\r\n    background: #3b82f6;\r\n    color: white;\r\n    border-color: #3b82f6;\r\n}\r\n\r\n.save-btn:hover {\r\n    background: #2563eb;\r\n    border-color: #2563eb;\r\n}\r\n\r\n.cancel-btn {\r\n    background: transparent;\r\n    color: #94a3b8;\r\n    border-color: #475569;\r\n}\r\n\r\n.cancel-btn:hover {\r\n    background: #374151;\r\n    border-color: #6b7280;\r\n}\r\n\r\n    /* Responsive Design for My Activities */\r\n    @media (max-width: 768px) {\r\n        .view-activities {\r\n            padding: 0 4px;\r\n        }\r\n        \r\n        .view-header {\r\n            flex-direction: column;\r\n            gap: 16px;\r\n            padding: 24px 16px;\r\n            margin-bottom: 24px;\r\n        }\r\n        \r\n        .view-header-content {\r\n            order: 1;\r\n        }\r\n        \r\n        .refresh-btn {\r\n            order: 2;\r\n            align-self: center;\r\n            min-width: 120px;\r\n            padding: 10px 16px;\r\n            font-size: 13px;\r\n        }\r\n        \r\n        .view-title {\r\n            font-size: 24px;\r\n        }\r\n        \r\n        .view-description {\r\n            font-size: 14px;\r\n        }\r\n        \r\n        .activities-grid {\r\n            grid-template-columns: 1fr;\r\n            gap: 16px;\r\n            padding: 0;\r\n        }\r\n        \r\n        .activity-card {\r\n            padding: 20px;\r\n            margin: 0;\r\n        }\r\n        \r\n        .activity-header {\r\n            flex-direction: column;\r\n            gap: 12px;\r\n            align-items: stretch;\r\n        }\r\n        \r\n        .activity-header h4 {\r\n            font-size: 18px;\r\n            margin-right: 0;\r\n        }\r\n        \r\n        .activity-actions {\r\n            justify-content: flex-end;\r\n            gap: 8px;\r\n        }\r\n        \r\n        .edit-btn, .delete-btn {\r\n            padding: 8px 12px;\r\n            font-size: 13px;\r\n            min-width: 96px;\r\n            height: 40px;\r\n        }\r\n        \r\n        .activity-details p {\r\n            font-size: 14px;\r\n            flex-direction: column;\r\n            align-items: flex-start;\r\n            gap: 4px;\r\n        }\r\n        \r\n        .activity-details strong {\r\n            min-width: auto;\r\n        }\r\n        \r\n        .no-activities-message, .loading-message {\r\n            padding: 32px 16px;\r\n            font-size: 16px;\r\n        }\r\n    }\r\n    \r\n    @media (max-width: 480px) {\r\n        .activity-card {\r\n            padding: 12px;\r\n        }\r\n        \r\n        .activity-header h4 {\r\n            font-size: 16px;\r\n        }\r\n        \r\n        .activity-details p {\r\n            font-size: 13px;\r\n        }\r\n        \r\n        .edit-btn, .delete-btn {\r\n            padding: 5px 10px;\r\n            font-size: 11px;\r\n        }\r\n        \r\n        .edit-modal {\r\n            width: 95%;\r\n            max-width: none;\r\n            margin: 20px;\r\n            border-radius: 16px;\r\n        }\r\n        \r\n        .edit-modal-header {\r\n            padding: 20px 24px;\r\n        }\r\n        \r\n        .edit-form {\r\n            padding: 24px;\r\n            gap: 20px;\r\n        }\r\n        \r\n        .form-row {\r\n            grid-template-columns: 1fr;\r\n            gap: 20px;\r\n            padding: 12px;\r\n            margin-bottom: 16px;\r\n            border-radius: 16px;\r\n        }\r\n        \r\n        .form-row:hover {\r\n            transform: none;\r\n            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n        }\r\n        \r\n        .form-input, .form-textarea, .form-select {\r\n            padding: 16px 20px;\r\n            font-size: 16px;\r\n            border-radius: 14px;\r\n        }\r\n        \r\n        .form-textarea {\r\n            min-height: 100px;\r\n            padding-top: 18px;\r\n            padding-bottom: 18px;\r\n        }\r\n        \r\n        .form-group label {\r\n            font-size: 13px;\r\n            margin-right: 2px;\r\n        }\r\n        \r\n        .form-select {\r\n            padding-left: 35px;\r\n            background-position: calc(100% - 18px) calc(1em + 2px), \r\n                                 calc(100% - 13px) calc(1em + 2px);\r\n        }\r\n        \r\n        .form-actions {\r\n            flex-direction: column-reverse;\r\n            align-items: stretch;\r\n            gap: 12px;\r\n            padding: 20px 0 0 0;\r\n        }\r\n        \r\n        .save-btn, .cancel-btn {\r\n            width: 100%;\r\n            padding: 14px;\r\n            min-width: auto;\r\n        }\r\n    }\r\n\r\n/* Modal Overlay Styles */\r\n.modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.8);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n    backdrop-filter: blur(5px);\r\n}\r\n\r\n.modal-content {\r\n    background: #1a1a2e;\r\n    border-radius: 20px;\r\n    width: 90%;\r\n    max-width: 500px;\r\n    max-height: 90vh;\r\n    overflow-y: auto;\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\r\n    animation: modalSlideIn 0.3s ease;\r\n    border: 2px solid #4facfe;\r\n    position: relative;\r\n    z-index: 1001;\r\n    padding: 10px;\r\n}\r\n\r\n@keyframes modalSlideIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(-50px) scale(0.9);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0) scale(1);\r\n    }\r\n}\r\n\r\n/* Modal Header Styles */\r\n.modal-header {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    border-radius: 20px 20px 0 0;\r\n}\r\n\r\n.modal-header h3 {\r\n    margin: 0;\r\n    font-size: 1.3rem;\r\n}\r\n\r\n.close-btn {\r\n    background: none;\r\n    border: none;\r\n    color: white;\r\n    font-size: 1.5rem;\r\n    cursor: pointer;\r\n    padding: 0;\r\n    width: 30px;\r\n    height: 30px;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transition: background 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n    background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* Account Settings Modal Styles */\r\n.account-settings-modal {\r\n    max-width: 600px;\r\n    width: 90%;\r\n    max-height: 90vh;\r\n    overflow-y: auto;\r\n    padding: 10px;\r\n}\r\n\r\n.account-form {\r\n    padding: 20px 0;\r\n}\r\n\r\n.form-group {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.form-group label {\r\n    display: block;\r\n    margin-bottom: 8px;\r\n    font-weight: 600;\r\n    color: #f5f5f5;\r\n    font-size: 14px;\r\n}\r\n\r\n.form-input {\r\n    width: 100%;\r\n    padding: 12px 16px;\r\n    background: #2d3748;\r\n    border: 2px solid #4a5568;\r\n    border-radius: 8px;\r\n    color: #f5f5f5;\r\n    font-size: 14px;\r\n    transition: all 0.3s ease;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.form-input:focus {\r\n    outline: none;\r\n    border-color: #4facfe;\r\n    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);\r\n    background: #374151;\r\n}\r\n\r\n.form-input::placeholder {\r\n    color: #a0aec0;\r\n}\r\n\r\n.password-section {\r\n    margin-top: 30px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.password-section h4 {\r\n    margin: 0 0 20px 0;\r\n    color: #4facfe;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n}\r\n\r\n.form-actions {\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    margin-top: 30px;\r\n    padding-top: 20px;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.save-btn {\r\n    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n    color: white;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n    min-width: 120px;\r\n}\r\n\r\n.save-btn:hover:not(:disabled) {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);\r\n}\r\n\r\n.save-btn:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.cancel-btn {\r\n    background: transparent;\r\n    color: #a0aec0;\r\n    border: 2px solid #4a5568;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n    min-width: 120px;\r\n}\r\n\r\n.cancel-btn:hover {\r\n    background: #374151;\r\n    border-color: #6b7280;\r\n    color: #f5f5f5;\r\n}\r\n\r\n/* Responsive styles for account modal */\r\n@media (max-width: 768px) {\r\n    .account-settings-modal {\r\n        width: 95%;\r\n        margin: 20px;\r\n    }\r\n    \r\n    .form-actions {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .save-btn,\r\n    .cancel-btn {\r\n        width: 100%;\r\n    }\r\n}\r\n\r\n/* Clickable Row Styles */\r\n.clickable-row {\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.clickable-row:hover {\r\n    background-color: rgba(74, 85, 104, 0.1);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.clickable-row:active {\r\n    transform: translateY(0);\r\n}\r\n\r\n/* PIN Confirmation Modal Styles */\r\n.pin-modal {\r\n    max-width: 450px;\r\n    background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n    border: 2px solid #4a5568;\r\n    border-radius: 16px;\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n.pin-modal .modal-header {\r\n    background: linear-gradient(135deg, #2d3748, #4a5568);\r\n    border-bottom: 2px solid #4a5568;\r\n    border-radius: 14px 14px 0 0;\r\n    padding: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.pin-modal .modal-header h3 {\r\n    color: #4fc3f7;\r\n    margin: 0;\r\n    font-size: 1.4rem;\r\n    font-weight: 600;\r\n}\r\n\r\n.pin-modal .modal-body {\r\n    padding: 25px;\r\n}\r\n\r\n.pin-message {\r\n    color: #e2e8f0;\r\n    font-size: 1.1rem;\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n    line-height: 1.6;\r\n}\r\n\r\n.pin-modal .form-group {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.pin-modal .form-group label {\r\n    color: #4fc3f7;\r\n    font-weight: 600;\r\n    margin-bottom: 8px;\r\n    display: block;\r\n}\r\n\r\n.pin-modal .form-control {\r\n    background: #2d3748;\r\n    border: 2px solid #4a5568;\r\n    color: #e2e8f0;\r\n    padding: 12px 16px;\r\n    border-radius: 8px;\r\n    font-size: 1rem;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.pin-modal .form-control:focus {\r\n    border-color: #4fc3f7;\r\n    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);\r\n    outline: none;\r\n}\r\n\r\n.pin-modal .modal-footer {\r\n    padding: 20px 25px;\r\n    display: flex;\r\n    gap: 12px;\r\n    justify-content: flex-end;\r\n    border-top: 1px solid #4a5568;\r\n}\r\n\r\n.confirm-btn {\r\n    background: linear-gradient(135deg, #e53e3e, #c53030);\r\n    color: white;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    min-width: 100px;\r\n}\r\n\r\n.confirm-btn:hover:not(:disabled) {\r\n    background: linear-gradient(135deg, #c53030, #9c2626);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(229, 62, 62, 0.4);\r\n}\r\n\r\n.confirm-btn:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n}\r\n\r\n.pin-modal .cancel-btn {\r\n    background: transparent;\r\n    color: #a0aec0;\r\n    border: 2px solid #4a5568;\r\n    padding: 10px 20px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    min-width: 100px;\r\n}\r\n\r\n.pin-modal .cancel-btn:hover {\r\n    background: #4a5568;\r\n    color: #e2e8f0;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n/* Activity Details Modal Styles */\r\n.activity-details-modal {\r\n    max-width: 900px;\r\n    width: 95%;\r\n    max-height: 90vh;\r\n    background: #ffffff;\r\n    color: #333333;\r\n    border-radius: 16px;\r\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);\r\n    overflow: hidden;\r\n    position: relative;\r\n    z-index: 1001;\r\n}\r\n\r\n/* Enhanced modal overlay to prevent background scrolling */\r\n.modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: rgba(0, 0, 0, 0.8);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n    overflow: hidden;\r\n}\r\n\r\n.activity-details-modal .modal-header {\r\n    background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n    color: #ffffff;\r\n    padding: 20px 30px;\r\n    border-bottom: 2px solid #4a5568;\r\n}\r\n\r\n.activity-details-modal .modal-header h3 {\r\n    margin: 0;\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n}\r\n\r\n.activity-details-modal .modal-body {\r\n    padding: 0;\r\n    max-height: calc(90vh - 140px);\r\n    overflow-y: auto;\r\n}\r\n\r\n.activity-document {\r\n    padding: 30px;\r\n    font-family: 'Arial', sans-serif;\r\n    line-height: 1.6;\r\n}\r\n\r\n.document-header {\r\n    text-align: center;\r\n    margin-bottom: 40px;\r\n    border-bottom: 3px solid #1a1a2e;\r\n    padding-bottom: 30px;\r\n}\r\n\r\n.logo-section {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.logo-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.organization-logo {\r\n    width: 80px;\r\n    height: 80px;\r\n    object-fit: contain;\r\n    border-radius: 8px;\r\n    border: 2px solid #1a1a2e;\r\n    background: #ffffff;\r\n    padding: 4px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.logo-label {\r\n    font-size: 12px;\r\n    font-weight: bold;\r\n    color: #1a1a2e;\r\n    text-align: center;\r\n}\r\n\r\n.organization-info h2 {\r\n    margin: 0;\r\n    font-size: 28px;\r\n    font-weight: bold;\r\n    color: #1a1a2e;\r\n}\r\n\r\n.organization-info h3 {\r\n    margin: 5px 0 0 0;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    color: #4a5568;\r\n}\r\n\r\n.document-title h1 {\r\n    margin: 0;\r\n    font-size: 32px;\r\n    font-weight: bold;\r\n    color: #1a1a2e;\r\n    text-decoration: underline;\r\n}\r\n\r\n.details-table {\r\n    width: 100%;\r\n}\r\n\r\n.detail-row {\r\n    display: flex;\r\n    margin-bottom: 20px;\r\n    border-bottom: 1px solid #e2e8f0;\r\n    padding-bottom: 15px;\r\n}\r\n\r\n.detail-label {\r\n    flex: 0 0 200px;\r\n    font-weight: bold;\r\n    color: #1a1a2e;\r\n    padding-right: 20px;\r\n    font-size: 16px;\r\n}\r\n\r\n.detail-value {\r\n    flex: 1;\r\n    color: #4a5568;\r\n    font-size: 16px;\r\n}\r\n\r\n.goals-list, .target-groups-list, .levels-list {\r\n    margin: 0;\r\n    padding-right: 20px;\r\n}\r\n\r\n.goals-list li, .target-groups-list li, .levels-list li {\r\n    margin-bottom: 8px;\r\n    color: #4a5568;\r\n}\r\n\r\n.budget-table {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    margin-top: 10px;\r\n    border: 2px solid #1a1a2e;\r\n}\r\n\r\n.budget-table th,\r\n.budget-table td {\r\n    border: 1px solid #cbd5e0;\r\n    padding: 12px;\r\n    text-align: center;\r\n}\r\n\r\n.budget-table th {\r\n    background: #1a1a2e;\r\n    color: #ffffff;\r\n    font-weight: bold;\r\n}\r\n\r\n.budget-table tbody tr:nth-child(even) {\r\n    background: #f8f9fa;\r\n}\r\n\r\n.budget-table .total-row {\r\n    background: #e2e8f0 !important;\r\n    font-weight: bold;\r\n}\r\n\r\n.status-badge {\r\n    padding: 8px 16px;\r\n    border-radius: 20px;\r\n    font-weight: bold;\r\n    font-size: 14px;\r\n}\r\n\r\n.activity-details-modal .modal-footer {\r\n    background: #f8f9fa;\r\n    padding: 20px 30px;\r\n    border-top: 1px solid #e2e8f0;\r\n    text-align: center;\r\n}\r\n\r\n.close-modal-btn {\r\n    background: linear-gradient(135deg, #1a1a2e, #16213e);\r\n    color: #ffffff;\r\n    border: none;\r\n    padding: 12px 30px;\r\n    border-radius: 8px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    margin-left: 10px;\r\n}\r\n\r\n.close-modal-btn:hover {\r\n    background: linear-gradient(135deg, #16213e, #1a1a2e);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.export-docx-btn {\r\n    background: linear-gradient(135deg, #2563eb, #1d4ed8);\r\n    color: #ffffff;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.export-docx-btn:hover {\r\n    background: linear-gradient(135deg, #1d4ed8, #1e40af);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);\r\n}\r\n\r\n.export-docx-btn:active {\r\n    transform: translateY(0);\r\n}\r\n\r\n.print-btn {\r\n    background: linear-gradient(135deg, #059669, #047857);\r\n    color: #ffffff;\r\n    border: none;\r\n    padding: 12px 24px;\r\n    border-radius: 8px;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 10px;\r\n}\r\n\r\n.print-btn:hover {\r\n    background: linear-gradient(135deg, #047857, #065f46);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);\r\n}\r\n\r\n.print-btn:active {\r\n    transform: translateY(0);\r\n}\r\n\r\n/* Responsive styles for modal footer buttons */\r\n@media (max-width: 768px) {\r\n    .activity-details-modal .modal-footer {\r\n        flex-direction: column;\r\n        gap: 10px;\r\n    }\r\n\r\n    .print-btn,\r\n    .export-docx-btn,\r\n    .close-modal-btn {\r\n        width: 100%;\r\n        margin: 0;\r\n        justify-content: center;\r\n    }\r\n\r\n    .print-btn {\r\n        margin-right: 0;\r\n    }\r\n}\r\n\r\n.activity-details-modal .modal-footer {\r\n    background: #f8f9fa;\r\n    padding: 20px 30px;\r\n    border-top: 1px solid #e2e8f0;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    gap: 15px;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n/* Responsive Design for Activity Modal */\r\n@media (max-width: 768px) {\r\n    .activity-details-modal {\r\n        width: 98%;\r\n        max-height: 95vh;\r\n    }\r\n\r\n    .activity-document {\r\n        padding: 20px;\r\n    }\r\n\r\n    .logo-section {\r\n        flex-direction: column;\r\n        gap: 15px;\r\n    }\r\n\r\n    .organization-logo {\r\n        width: 60px;\r\n        height: 60px;\r\n    }\r\n\r\n    .logo-label {\r\n        font-size: 10px;\r\n    }\r\n\r\n    .detail-row {\r\n        flex-direction: column;\r\n    }\r\n\r\n    .detail-label {\r\n        flex: none;\r\n        margin-bottom: 5px;\r\n        padding-right: 0;\r\n    }\r\n\r\n    .budget-table {\r\n        font-size: 12px;\r\n    }\r\n\r\n    .budget-table th,\r\n    .budget-table td {\r\n        padding: 8px 4px;\r\n    }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;AACA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAEC,aAAa,EAAEC,WAAW,EAAEC,QAAO,QAAS,MAAM;AACxI,SAASC,MAAK,QAAS,YAAY;AAEnC,eAAe;EACXC,IAAI,EAAE,cAAc;EACpBC,IAAIA,CAAA,EAAG;IACH,OAAO;MACHC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE,EAAE;MACrBC,mBAAmB,EAAE,EAAE;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE,EAAE;MACrBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,CAAC;MAChBC,qBAAqB,EAAE,EAAE;MACzBC,iBAAiB,EAAE,EAAE;MACrBC,qBAAqB,EAAE,EAAE;MACzBC,mBAAmB,EAAE,CAAC;MACtBC,iBAAiB,EAAE,EAAE;MACrBC,mBAAmB,EAAE,CAAC;MACtBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE;QACZC,KAAK,EAAE,IAAI,CAAChB,iBAAiB;QAC7BiB,KAAK,EAAE,IAAI,CAAClB,iBAAiB;QAC7BmB,WAAW,EAAE,IAAI,CAACjB,mBAAmB;QACrCkB,IAAI,EAAE,IAAI,CAACjB,iBAAiB;QAC5BkB,IAAI,EAAE,IAAI,CAACjB,iBAAiB;QAC5BkB,KAAK,EAAE,IAAI,CAACjB,aAAa;QACzBkB,QAAQ,EAAE,IAAI,CAACjB,YAAY;QAC3BkB,QAAQ,EAAE,IAAI,CAAChB,qBAAqB;QACpCiB,IAAI,EAAE,IAAI,CAAChB,iBAAiB;QAC5BiB,QAAQ,EAAE,IAAI,CAAChB,qBAAqB;QACpCiB,MAAM,EAAE,IAAI,CAAChB,mBAAmB;QAChCiB,aAAa,EAAE,IAAI,CAAChB,iBAAiB;QACrCiB,WAAW,EAAE,IAAI,CAAChB;MACtB,CAAC;MACD;MACAiB,UAAU,EAAE,EAAE;MACd;MACAC,IAAI,EAAE,IAAI;MACVC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,EAAE;MAChBC,iBAAiB,EAAE,KAAK;MACxBC,eAAe,EAAE,IAAI;MACrBC,gBAAgB,EAAE,KAAK;MACvBC,WAAW,EAAE,QAAQ;MACrBC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,IAAI;MAAE;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,WAAW,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,OAAO,EAAE;MACb,CAAC;MACDC,eAAe,EAAE,KAAK;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,mBAAmB,EAAE;QACjBC,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,EAAE;QAAE;QACZC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;MACd,CAAC;MACDC,iBAAiB,EAAE,KAAK;MACxBC,gBAAgB,EAAE;IACtB,CAAC;EACL,CAAC;EACDC,QAAQ,EAAE;IACNC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAAClB,cAAc,EAAE;QACtB,OAAO,IAAI,CAACN,YAAY;MAC5B;MACA,OAAO,IAAI,CAACA,YAAY,CAACyB,MAAM,CAACN,QAAO,IAAKA,QAAQ,CAACO,KAAI,KAAM,IAAI,CAACpB,cAAc,CAAC;IACvF;EACJ,CAAC;EACDqB,OAAO,EAAE;IACLC,YAAYA,CAACF,KAAK,EAAE;MAChB;MACA,IAAI,CAACpB,cAAa,GAAI,IAAI,CAACA,cAAa,KAAMoB,KAAI,GAAI,IAAG,GAAIA,KAAK;IACtE,CAAC;IACDG,cAAcA,CAAA,EAAG;MACb,IAAI,CAAC9B,YAAW,GAAI,CAAC,IAAI,CAACA,YAAY;IAC1C,CAAC;IACD+B,MAAMA,CAAA,EAAG;MACLC,YAAY,CAACC,UAAU,CAAC,YAAY,CAAC;MACrCD,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;MACpCD,YAAY,CAACC,UAAU,CAAC,eAAe,CAAC;MACxC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;IAC/B,CAAC;IACDC,SAASA,CAAA,EAAG;MACR,IAAI,CAACpC,YAAW,GAAI,KAAK;MACzB,IAAI,CAACkC,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;IAC/B,CAAC;IACDE,mBAAmBA,CAAA,EAAG;MAClB,IAAI,CAACrC,YAAW,GAAI,KAAK;MACzB,IAAI,CAACS,WAAW,CAACC,QAAO,GAAI,IAAI,CAACX,IAAI,EAAEuC,SAAQ,IAAK,EAAE;MACtD,IAAI,CAAC7B,WAAW,CAACK,OAAM,GAAIkB,YAAY,CAACO,OAAO,CAAC,eAAe,KAAK,EAAE;MACtE,IAAI,CAAC9B,WAAW,CAACE,eAAc,GAAI,EAAE;MACrC,IAAI,CAACF,WAAW,CAACG,WAAU,GAAI,EAAE;MACjC,IAAI,CAACH,WAAW,CAACI,eAAc,GAAI,EAAE;MACrC,IAAI,CAACL,mBAAkB,GAAI,IAAI;IACnC,CAAC;IACDgC,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAAChC,mBAAkB,GAAI,KAAK;MAChC,IAAI,CAACC,WAAU,GAAI;QACfC,QAAQ,EAAE,EAAE;QACZC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,OAAO,EAAE;MACb,CAAC;IACL,CAAC;IACD,MAAM2B,qBAAqBA,CAAA,EAAG;MAC1B;MACA,IAAI,CAAC,IAAI,CAAChC,WAAW,CAACC,QAAQ,CAACgC,IAAI,CAAC,CAAC,EAAE;QACnC,MAAM,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC;UAClB3D,KAAK,EAAE,iBAAiB;UACxB4D,IAAI,EAAE,yBAAyB;UAC/BC,IAAI,EAAE;QACV,CAAC,CAAC;QACF;MACJ;MAEA,IAAI,IAAI,CAACrC,WAAW,CAACG,WAAU,IAAK,IAAI,CAACH,WAAW,CAACG,WAAU,KAAM,IAAI,CAACH,WAAW,CAACI,eAAe,EAAE;QACnG,MAAM,IAAI,CAAC8B,KAAK,CAACC,IAAI,CAAC;UAClB3D,KAAK,EAAE,iBAAiB;UACxB4D,IAAI,EAAE,4CAA4C;UAClDC,IAAI,EAAE;QACV,CAAC,CAAC;QACF;MACJ;MAEA,IAAI,IAAI,CAACrC,WAAW,CAACG,WAAU,IAAK,IAAI,CAACH,WAAW,CAACG,WAAW,CAACmC,MAAK,GAAI,CAAC,EAAE;QACzE,MAAM,IAAI,CAACJ,KAAK,CAACC,IAAI,CAAC;UAClB3D,KAAK,EAAE,iBAAiB;UACxB4D,IAAI,EAAE,0CAA0C;UAChDC,IAAI,EAAE;QACV,CAAC,CAAC;QACF;MACJ;MAEA,IAAI,CAAC/B,eAAc,GAAI,IAAI;MAE3B,IAAI;QACA,MAAMiC,UAAS,GAAI;UACfV,SAAS,EAAE,IAAI,CAAC7B,WAAW,CAACC,QAAQ,CAACgC,IAAI,CAAC,CAAC;UAC3CO,QAAQ,EAAE,IAAI,CAACxC,WAAW,CAACK,OAAO,CAAC4B,IAAI,CAAC;QAC5C,CAAC;;QAED;QACA,IAAI,IAAI,CAACjC,WAAW,CAACG,WAAW,EAAE;UAC9BoC,UAAU,CAACE,gBAAe,GAAI,IAAI,CAACzC,WAAW,CAACE,eAAe;UAC9DqC,UAAU,CAACG,YAAW,GAAI,IAAI,CAAC1C,WAAW,CAACG,WAAW;QAC1D;QAEA,MAAMwC,QAAO,GAAI,MAAM,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,sBAAsB,EAAEN,UAAU,CAAC;QAExE,IAAII,QAAQ,CAACG,EAAE,EAAE;UACb,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;;UAErB;UACA,IAAI,CAACzD,IAAI,CAACuC,SAAQ,GAAI,IAAI,CAAC7B,WAAW,CAACC,QAAQ,CAACgC,IAAI,CAAC,CAAC;UACtDV,YAAY,CAACyB,OAAO,CAAC,WAAW,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC5D,IAAI,CAAC,CAAC;UAC5DiC,YAAY,CAACyB,OAAO,CAAC,eAAe,EAAE,IAAI,CAAChD,WAAW,CAACK,OAAO,CAAC4B,IAAI,CAAC,CAAC,CAAC;UAEtE,MAAM,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC;YAClB3D,KAAK,EAAE,kBAAkB;YACzB4D,IAAI,EAAE,+BAA+B;YACrCC,IAAI,EAAE;UACV,CAAC,CAAC;UAEF,IAAI,CAACN,oBAAoB,CAAC,CAAC;QAC/B,OAAO;UACH,MAAMoB,SAAQ,GAAI,MAAMR,QAAQ,CAACI,IAAI,CAAC,CAAC;UACvC,MAAM,IAAI,CAACb,KAAK,CAACC,IAAI,CAAC;YAClB3D,KAAK,EAAE,gBAAgB;YACvB4D,IAAI,EAAEe,SAAS,CAACC,KAAI,IAAK,6BAA6B;YACtDf,IAAI,EAAE;UACV,CAAC,CAAC;QACN;MACJ,EAAE,OAAOe,KAAK,EAAE;QACZ,IAAIA,KAAK,CAACC,OAAM,KAAM,iBAAiB,EAAE;UACrC;UACA;QACJ;QACAC,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAM,IAAI,CAAClB,KAAK,CAACC,IAAI,CAAC;UAClB3D,KAAK,EAAE,gBAAgB;UACvB4D,IAAI,EAAE,oCAAoC;UAC1CC,IAAI,EAAE;QACV,CAAC,CAAC;MACN,UAAU;QACN,IAAI,CAAC/B,eAAc,GAAI,KAAK;MAChC;IACJ,CAAC;IACD,MAAMiD,QAAQA,CAAA,EAAG;MACb,IAAI;QACA;QACA,IAAI,CAAC,IAAI,CAAC1D,eAAe,CAACoC,IAAI,CAAC,CAAC,EAAE;UAC9B,MAAM,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC;YAClB3D,KAAK,EAAE,iBAAiB;YACxB4D,IAAI,EAAE,8BAA8B;YACpCC,IAAI,EAAE;UACV,CAAC,CAAC;UACF;QACJ;;QAEA;QACA,MAAMmB,aAAY,GAAIC,QAAQ,CAACC,gBAAgB,CAAC,gBAAgB,CAAC;QAEjE,IAAIF,aAAa,CAAClB,MAAK,KAAM,CAAC,EAAE;UAC5B,MAAM,IAAI,CAACJ,KAAK,CAACC,IAAI,CAAC;YAClB3D,KAAK,EAAE,iBAAiB;YACxB4D,IAAI,EAAE,gCAAgC;YACtCC,IAAI,EAAE;UACV,CAAC,CAAC;UACF;QACJ;QAEA,MAAMsB,KAAI,GAAIpC,YAAY,CAACO,OAAO,CAAC,YAAY,CAAC;QAChD,MAAMzB,OAAM,GAAIkB,YAAY,CAACO,OAAO,CAAC,eAAe,CAAC;QAErD,IAAI,CAAC6B,KAAK,EAAE;UACR,IAAI,CAACC,MAAM,CAACR,KAAK,CAAC,yBAAyB,CAAC;UAC5C,IAAI,CAAC3B,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;UAC3B;QACJ;QAEA,MAAMrC,UAAS,GAAI,EAAE;QACrB,IAAIwE,SAAQ,GAAI,KAAK;;QAErB;QACA,KAAK,IAAIC,KAAI,GAAI,CAAC,EAAEA,KAAI,GAAIN,aAAa,CAAClB,MAAM,EAAEwB,KAAK,EAAE,EAAE;UACvD,MAAMC,IAAG,GAAIP,aAAa,CAACM,KAAK,CAAC;;UAEjC;UACA,MAAME,UAAS,GAAID,IAAI,CAACE,aAAa,CAAC,uCAAuC,CAAC;UAC9E,MAAMC,UAAS,GAAIH,IAAI,CAACE,aAAa,CAAC,mCAAmC,CAAC;UAC1E,MAAME,cAAa,GAAIJ,IAAI,CAACE,aAAa,CAAC,sCAAsC,CAAC;UACjF,MAAMG,SAAQ,GAAIL,IAAI,CAACE,aAAa,CAAC,oBAAoB,CAAC;UAC1D,MAAMI,WAAU,GAAIN,IAAI,CAACE,aAAa,CAAC,uBAAuB,CAAC;UAE/D,MAAMK,SAAQ,GAAIN,UAAU,EAAEO,KAAK,EAAEtC,IAAI,CAAC,CAAC;UAC3C,MAAMzD,KAAI,GAAI0F,UAAU,EAAEK,KAAK,EAAEtC,IAAI,CAAC,CAAC;UACvC,MAAMuC,gBAAe,GAAIL,cAAc,EAAEI,KAAK,EAAEtC,IAAI,CAAC,CAAC;UACtD,MAAMwC,YAAW,GAAIL,SAAS,EAAEG,KAAK;UACrC,MAAMrD,KAAI,GAAImD,WAAW,EAAEE,KAAK;;UAEhC;UACAjB,OAAO,CAACoB,GAAG,CAAC,YAAYZ,KAAI,GAAI,CAAC,qBAAqB,EAAE5C,KAAK,EAAE,eAAe,EAAEmD,WAAW,CAAC;;UAE5F;UACA,MAAMM,YAAW,GAAIZ,IAAI,CAACE,aAAa,CAAC,UAAU,CAAC;UACnD,MAAMW,YAAW,GAAID,YAAY,EAAEJ,KAAK,EAAEtC,IAAI,CAAC,KAAK,EAAE;;UAEtD;UACA,MAAM4C,UAAS,GAAId,IAAI,CAACL,gBAAgB,CAAC,uCAAuC,CAAC;UACjF,MAAM9F,aAAY,GAAIkH,KAAK,CAACC,IAAI,CAACF,UAAU,CAAC,CAACG,GAAG,CAACC,KAAI,KAAM;YAAE7C,IAAI,EAAE6C,KAAK,CAACV,KAAK,CAACtC,IAAI,CAAC;UAAE,CAAC,CAAC,CAAC,CAAChB,MAAM,CAACiE,IAAG,IAAKA,IAAI,CAAC9C,IAAI,CAAC;;UAEnH;UACA,MAAM+C,iBAAgB,GAAIpB,IAAI,CAACL,gBAAgB,CAAC,4CAA4C,CAAC;UAC7F,MAAM7F,YAAW,GAAIiH,KAAK,CAACC,IAAI,CAACI,iBAAiB,CAAC,CAACH,GAAG,CAACC,KAAI,KAAM;YAAE7C,IAAI,EAAE6C,KAAK,CAACV,KAAK,CAACtC,IAAI,CAAC;UAAE,CAAC,CAAC,CAAC,CAAChB,MAAM,CAACmE,KAAI,IAAKA,KAAK,CAAChD,IAAI,CAAC;;UAE3H;UACA,MAAMiD,aAAY,GAAItB,IAAI,CAACE,aAAa,CAAC,2BAA2B,CAAC;UACrE,MAAMnG,aAAY,GAAIuH,aAAa,EAAEd,KAAI,GAAIe,QAAQ,CAACD,aAAa,CAACd,KAAK,IAAI,IAAI;;UAEjF;UACA,MAAMgB,aAAY,GAAIxB,IAAI,CAACE,aAAa,CAAC,kCAAkC,CAAC;UAC5E,MAAMuB,gBAAe,GAAID,aAAa,EAAEhB,KAAK,EAAEtC,IAAI,CAAC,KAAK,EAAE;UAE3D,MAAMwD,SAAQ,GAAI1B,IAAI,CAACE,aAAa,CAAC,oBAAoB,CAAC;UAC1D,MAAMyB,YAAW,GAAID,SAAS,EAAElB,KAAI,IAAK,IAAI;UAE7C,MAAMoB,aAAY,GAAI5B,IAAI,CAACE,aAAa,CAAC,iCAAiC,CAAC;UAC3E,MAAM2B,gBAAe,GAAID,aAAa,EAAEpB,KAAK,EAAEtC,IAAI,CAAC,KAAK,EAAE;;UAE3D;UACA,MAAM4D,YAAW,GAAI9B,IAAI,CAACL,gBAAgB,CAAC,qBAAqB,CAAC;UACjE,MAAMoC,cAAa,GAAID,YAAY,CAAC,CAAC,CAAC,EAAEtB,KAAI,GAAIwB,UAAU,CAACF,YAAY,CAAC,CAAC,CAAC,CAACtB,KAAK,IAAI,IAAI;UACxF,MAAMnF,WAAU,GAAIyG,YAAY,CAAC,CAAC,CAAC,EAAEtB,KAAI,GAAIwB,UAAU,CAACF,YAAY,CAAC,CAAC,CAAC,CAACtB,KAAK,IAAI,IAAI;;UAErF;UACA,MAAMyB,iBAAgB,GAAIjC,IAAI,CAACL,gBAAgB,CAAC,qBAAqB,CAAC;UACtE,MAAMvE,aAAY,GAAI,EAAE;UAExB6G,iBAAiB,CAACC,OAAO,CAACC,UAAS,IAAK;YACpC;YACA,MAAMC,SAAQ,GAAID,UAAU,CAACxC,gBAAgB,CAAC,OAAO,CAAC;YACtD,IAAIyC,SAAS,CAAC7D,MAAK,IAAK,CAAC,EAAE;cACvB,MAAM8D,MAAK,GAAI;gBACX/I,IAAI,EAAE8I,SAAS,CAAC,CAAC,CAAC,EAAE5B,KAAK,EAAEtC,IAAI,CAAC,KAAK,EAAE;gBACvCoE,IAAI,EAAEF,SAAS,CAAC,CAAC,CAAC,EAAE5B,KAAK,EAAEtC,IAAI,CAAC,KAAK,EAAE;gBACvCqE,MAAM,EAAEH,SAAS,CAAC,CAAC,CAAC,EAAE5B,KAAI,GAAIe,QAAQ,CAACa,SAAS,CAAC,CAAC,CAAC,CAAC5B,KAAK,IAAI,CAAC;gBAC9DgC,KAAK,EAAEJ,SAAS,CAAC,CAAC,CAAC,EAAE5B,KAAI,GAAIwB,UAAU,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC5B,KAAK,IAAI,CAAC;gBAC/DiC,WAAW,EAAEL,SAAS,CAAC,CAAC,CAAC,EAAE5B,KAAI,GAAIwB,UAAU,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC5B,KAAK,IAAI;cACxE,CAAC;cACD,IAAI6B,MAAM,CAAC/I,IAAI,EAAE8B,aAAa,CAACuC,IAAI,CAAC0E,MAAM,CAAC;YAC/C;UACJ,CAAC,CAAC;;UAEF;UACA9C,OAAO,CAACoB,GAAG,CAAC,YAAYZ,KAAI,GAAI,CAAC,8BAA8B,EAAE3E,aAAa,CAAC;;UAE/E;UACA,MAAMsH,WAAU,GAAI1C,IAAI,CAACL,gBAAgB,CAAC,kDAAkD,CAAC;UAC7F,MAAMrF,cAAa,GAAIyG,KAAK,CAACC,IAAI,CAAC0B,WAAW,CAAC,CAACzB,GAAG,CAACC,KAAI,KAAM;YAAEvG,WAAW,EAAEuG,KAAK,CAACV,KAAK,CAACtC,IAAI,CAAC;UAAE,CAAC,CAAC,CAAC,CAAChB,MAAM,CAACyF,KAAI,IAAKA,KAAK,CAAChI,WAAW,CAAC;;UAErI;UACA,IAAI,CAAC4F,SAAQ,IAAK,CAAC9F,KAAI,IAAK,CAACiG,YAAW,IAAK,CAACvD,KAAK,EAAE;YACjD,IAAIyF,aAAY,GAAI,EAAE;YACtB,IAAI,CAACrC,SAAS,EAAEqC,aAAa,CAACjF,IAAI,CAAC,iBAAiB,CAAC;YACrD,IAAI,CAAClD,KAAK,EAAEmI,aAAa,CAACjF,IAAI,CAAC,cAAc,CAAC;YAC9C,IAAI,CAAC+C,YAAY,EAAEkC,aAAa,CAACjF,IAAI,CAAC,cAAc,CAAC;YACrD,IAAI,CAACR,KAAK,EAAEyF,aAAa,CAACjF,IAAI,CAAC,aAAa,CAAC;YAE7C,IAAI,CAACQ,KAAK,CAACC,IAAI,CAAC;cACZ3D,KAAK,EAAE,iBAAiB;cACxB4D,IAAI,EAAE,uCAAuC0B,KAAI,GAAI,CAAC,KAAK6C,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;cACrFvE,IAAI,EAAE;YACV,CAAC,CAAC;YACFwB,SAAQ,GAAI,IAAI;YAChB;UACJ;UAEA,IAAIgD,MAAK,GAAI,IAAI;;UAEjB;UACA,IAAI9C,IAAI,CAAC+C,YAAY,EAAE;YACnB,IAAI;cACA,MAAMC,QAAO,GAAI,IAAIC,QAAQ,CAAC,CAAC;cAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAElD,IAAI,CAAC+C,YAAY,CAAC;cAE1C,MAAMI,cAAa,GAAI,MAAMC,KAAK,CAAC,qCAAqC,EAAE;gBACtEC,MAAM,EAAE,MAAM;gBACdC,OAAO,EAAE;kBACL,eAAe,EAAE,UAAU1D,KAAK;gBACpC,CAAC;gBACD2D,IAAI,EAAEP;cACV,CAAC,CAAC;cAEF,IAAIG,cAAc,CAACpE,EAAE,EAAE;gBACnB,MAAMyE,YAAW,GAAI,MAAML,cAAc,CAACnE,IAAI,CAAC,CAAC;gBAChD,IAAIwE,YAAY,CAACC,IAAG,IAAKD,YAAY,CAACC,IAAI,CAACC,EAAE,EAAE;kBAC3CZ,MAAK,GAAIU,YAAY,CAACC,IAAI,CAACC,EAAE;gBACjC;cACJ,OAAO;gBACH,MAAMtE,SAAQ,GAAI,MAAM+D,cAAc,CAACnE,IAAI,CAAC,CAAC;gBAC7C,IAAI,CAACa,MAAM,CAACR,KAAK,CAAC,6BAA6BU,KAAI,GAAI,CAAC,KAAKX,SAAS,CAACC,KAAI,IAAK,eAAe,EAAE,CAAC;gBAClGS,SAAQ,GAAI,IAAI;gBAChB;cACJ;YACJ,EAAE,OAAO6D,WAAW,EAAE;cAClBpE,OAAO,CAACF,KAAK,CAAC,oBAAoB,EAAEsE,WAAW,CAAC;cAChD,IAAI,CAAC9D,MAAM,CAACR,KAAK,CAAC,6BAA6BU,KAAI,GAAI,CAAC,EAAE,CAAC;cAC3DD,SAAQ,GAAI,IAAI;cAChB;YACJ;UACJ;UAEAxE,UAAU,CAACqC,IAAI,CAAC;YACZiG,UAAU,EAAErD,SAAS;YACrB9F,KAAK,EAAEA,KAAK;YACZoJ,iBAAiB,EAAEpD,gBAAe,IAAK,EAAE;YACzCqD,aAAa,EAAEpD,YAAY;YAC3BvD,KAAK,EAAEA,KAAK;YACZ4G,OAAO,EAAEjB,MAAM;YACfkB,aAAa,EAAEnD,YAAY;YAC3BoD,cAAc,EAAEpK,aAAa;YAC7BqK,aAAa,EAAEpK,YAAY;YAC3BqK,cAAc,EAAEpK,aAAa;YAC7BqK,iBAAiB,EAAE3C,gBAAgB;YACnC4C,aAAa,EAAE1C,YAAY;YAC3B2C,iBAAiB,EAAEzC,gBAAgB;YACnC0C,eAAe,EAAExC,cAAc;YAC/ByC,cAAc,EAAEpJ,aAAa;YAC7BqJ,YAAY,EAAEpJ,WAAW;YACzBqJ,eAAe,EAAEpK;UACrB,CAAC,CAAC;QACN;QAEA,IAAIwF,SAAS,EAAE;;QAEf;QACA,MAAM6E,cAAa,GAAI;UACnBC,gBAAgB,EAAE,IAAI,CAAC9I,eAAe,CAACoC,IAAI,CAAC,CAAC;UAC7C5C,UAAU,EAAEA;QAChB,CAAC;;QAED;QACA,MAAMsD,QAAO,GAAI,MAAMwE,KAAK,CAAC,qCAAqC,EAAE;UAChEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACL,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAU1D,KAAK,EAAE;YAClC,YAAY,EAAEtD;UAClB,CAAC;UACDiH,IAAI,EAAErE,IAAI,CAACC,SAAS,CAACwF,cAAc;QACvC,CAAC,CAAC;QAEF,IAAI/F,QAAQ,CAACG,EAAE,EAAE;UACb,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;UACrB,IAAI,CAACa,MAAM,CAACgF,OAAO,CAAC,0BAA0B,CAAC;;UAE/C;UACA,IAAI,CAAC/I,eAAc,GAAI,EAAE;;UAEzB;UACA2D,aAAa,CAACyC,OAAO,CAAClC,IAAG,IAAKA,IAAI,CAAC8E,MAAM,CAAC,CAAC,CAAC;;UAE5C;UACA,IAAI,IAAI,CAAClJ,gBAAgB,EAAE;YACvB,IAAI,CAACmJ,iBAAiB,CAAC,CAAC;UAC5B;QACJ,OAAO;UACH,IAAI3F,SAAQ,GAAI,CAAC,CAAC;UAClB,IAAI;YACAA,SAAQ,GAAI,MAAMR,QAAQ,CAACI,IAAI,CAAC,CAAC;UACrC,EAAE,OAAOgG,SAAS,EAAE;YAChBzF,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAE2F,SAAS,CAAC;UAC5D;UACA,IAAI,CAACnF,MAAM,CAACR,KAAK,CAAC,0BAA0BD,SAAS,CAACE,OAAM,IAAK,eAAe,EAAE,CAAC;QACvF;MAEJ,EAAE,OAAOD,KAAK,EAAE;QACZE,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAACQ,MAAM,CAACR,KAAK,CAAC,uDAAuD,CAAC;MAC9E;IACJ,CAAC;IACD,MAAM0F,iBAAiBA,CAAA,EAAG;MACtB,IAAI,CAACrJ,iBAAgB,GAAI,IAAI;MAC7B,IAAI,CAACD,YAAW,GAAI,EAAE,EAAE;;MAExB,IAAI;QACA,MAAMmD,QAAO,GAAI,MAAM,IAAI,CAACC,IAAI,CAACoG,GAAG,CAAC,aAAa,CAAC;QAEnD,IAAIrG,QAAQ,CAACG,EAAE,EAAE;UACb,MAAMxF,IAAG,GAAI,MAAMqF,QAAQ,CAACI,IAAI,CAAC,CAAC;;UAElC;UACA,IAAIzF,IAAI,CAAC+B,UAAS,IAAKyF,KAAK,CAACmE,OAAO,CAAC3L,IAAI,CAAC+B,UAAU,CAAC,EAAE;YACnD,IAAI,CAACG,YAAW,GAAIlC,IAAI,CAAC+B,UAAU,CAAC2F,GAAG,CAACrE,QAAO,KAAM;cACjD,GAAGA,QAAQ;cACXuI,eAAe,EAAE;gBACbzB,EAAE,EAAE9G,QAAQ,CAACwI,aAAa;gBAC1BC,WAAW,EAAEzI,QAAQ,CAACyI,WAAW;gBACjCT,gBAAgB,EAAEhI,QAAQ,CAACgI,gBAAgB;gBAC3CU,UAAU,EAAE1I,QAAQ,CAAC0I;cACzB;YACJ,CAAC,CAAC,CAAC;UACP;QACJ,OAAO,IAAI1G,QAAQ,CAAC2G,MAAK,KAAM,GAAG,EAAE;UAChC;UACA,IAAI,CAAC9J,YAAW,GAAI,EAAE;QAC1B,OAAO;UACH,MAAM2D,SAAQ,GAAI,MAAMR,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACwG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;UACzDjG,OAAO,CAACF,KAAK,CAAC,6BAA6B,EAAED,SAAS,CAAC;UACvD,IAAI,CAACS,MAAM,CAACR,KAAK,CAAC,0BAA0BD,SAAS,CAACE,OAAM,IAAK,eAAe,EAAE,CAAC;QACvF;MACJ,EAAE,OAAOD,KAAK,EAAE;QACZ,IAAIA,KAAK,CAACC,OAAM,KAAM,iBAAiB,EAAE;UACrC;UACA;QACJ;QACAC,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAIA,KAAK,CAAC/F,IAAG,KAAM,WAAU,IAAK+F,KAAK,CAACC,OAAO,CAACmG,QAAQ,CAAC,OAAO,CAAC,EAAE;UAC/D,IAAI,CAAC5F,MAAM,CAACR,KAAK,CAAC,2EAA2E,CAAC;QAClG,OAAO;UACH,IAAI,CAACQ,MAAM,CAACR,KAAK,CAAC,uDAAuD,CAAC;QAC9E;MACJ,UAAU;QACN,IAAI,CAAC3D,iBAAgB,GAAI,KAAK;MAClC;IACJ,CAAC;IACDgK,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAAC9J,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;MAC9C,IAAI,IAAI,CAACA,gBAAe,IAAK,IAAI,CAACH,YAAY,CAAC8C,MAAK,KAAM,CAAC,EAAE;QACzD,IAAI,CAACwG,iBAAiB,CAAC,CAAC;MAC5B;IACJ,CAAC;IACDY,YAAYA,CAAC/I,QAAQ,EAAE;MACnB,IAAI,CAACgJ,wBAAwB,CAAC,MAAM,EAAEhJ,QAAQ,EAAE,MAAM;QAClD,IAAI,CAACiJ,aAAa,CAACjJ,QAAQ,CAAC;MAChC,CAAC,CAAC;IACN,CAAC;IACDiJ,aAAaA,CAACjJ,QAAQ,EAAE;MACpB,IAAI,CAACjB,eAAc,GAAI;QAAE,GAAGiB;MAAS,CAAC;MACtC;MACA,IAAI,IAAI,CAACjB,eAAe,CAACmI,aAAa,EAAE;QACpC,MAAMlJ,IAAG,GAAI,IAAIkL,IAAI,CAAC,IAAI,CAACnK,eAAe,CAACmI,aAAa,CAAC;QACzD;QACA,MAAMiC,IAAG,GAAInL,IAAI,CAACoL,WAAW,CAAC,CAAC;QAC/B,MAAMC,KAAI,GAAIC,MAAM,CAACtL,IAAI,CAACuL,QAAQ,CAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC1D,MAAMC,GAAE,GAAIH,MAAM,CAACtL,IAAI,CAAC0L,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACnD,IAAI,CAACzK,eAAe,CAACmI,aAAY,GAAI,GAAGiC,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;MAClE;IACJ,CAAC;IACD,MAAME,YAAYA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAAC5K,eAAe,EAAE;MAE3B,IAAI;QACA,MAAM6C,UAAS,GAAI;UACfoF,UAAU,EAAE,IAAI,CAACjI,eAAe,CAACiI,UAAU;UAC3CnJ,KAAK,EAAE,IAAI,CAACkB,eAAe,CAAClB,KAAK;UACjCoJ,iBAAiB,EAAE,IAAI,CAAClI,eAAe,CAACkI,iBAAiB;UACzDC,aAAa,EAAE,IAAI,CAACnI,eAAe,CAACmI,aAAa;UACjD3G,KAAK,EAAE,IAAI,CAACxB,eAAe,CAACwB;QAChC,CAAC;QAED,MAAMyB,QAAO,GAAI,MAAM,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,eAAe,IAAI,CAACnD,eAAe,CAAC+H,EAAE,EAAE,EAAElF,UAAU,CAAC;QAE1F,IAAII,QAAQ,CAACG,EAAE,EAAE;UACb,MAAMyH,eAAc,GAAI,MAAM5H,QAAQ,CAACI,IAAI,CAAC,CAAC;UAC7C;UACA,MAAMe,KAAI,GAAI,IAAI,CAACtE,YAAY,CAACgL,SAAS,CAACC,CAAA,IAAKA,CAAC,CAAChD,EAAC,KAAM8C,eAAe,CAAC9C,EAAE,CAAC;UAC3E,IAAI3D,KAAI,KAAM,CAAC,CAAC,EAAE;YACd,IAAI,CAACtE,YAAY,CAACsE,KAAK,IAAI;cAAE,GAAGyG,eAAe;cAAErB,eAAe,EAAE,IAAI,CAAC1J,YAAY,CAACsE,KAAK,CAAC,CAACoF;YAAgB,CAAC;UAChH;UACA,IAAI,CAACxJ,eAAc,GAAI,IAAI;UAC3B,IAAI,CAACkE,MAAM,CAACgF,OAAO,CAAC,wBAAwB,CAAC;QACjD,OAAO;UACH,IAAI,CAAChF,MAAM,CAACR,KAAK,CAAC,qBAAqB,CAAC;QAC5C;MACJ,EAAE,OAAOA,KAAK,EAAE;QACZ,IAAIA,KAAK,CAACC,OAAM,KAAM,iBAAiB,EAAE;UACrC;UACA;QACJ;QACAC,OAAO,CAACF,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACQ,MAAM,CAACR,KAAK,CAAC,4BAA4B,CAAC;MACnD;IACJ,CAAC;IACDsH,UAAUA,CAAA,EAAG;MACT,IAAI,CAAChL,eAAc,GAAI,IAAI;IAC/B,CAAC;IACDiL,cAAcA,CAACC,UAAU,EAAE;MACvB,MAAMjK,QAAO,GAAI,IAAI,CAACnB,YAAY,CAACqL,IAAI,CAACJ,CAAA,IAAKA,CAAC,CAAChD,EAAC,KAAMmD,UAAU,CAAC;MACjE,IAAI,CAACjB,wBAAwB,CAAC,QAAQ,EAAEhJ,QAAQ,EAAE,YAAY;QAC1D,MAAM,IAAI,CAACmK,qBAAqB,CAACF,UAAU,CAAC;MAChD,CAAC,CAAC;IACN,CAAC;IACD,MAAME,qBAAqBA,CAACF,UAAU,EAAE;MACpC,IAAI;QACA,MAAMjH,KAAI,GAAIpC,YAAY,CAACO,OAAO,CAAC,YAAY,CAAC;QAChD,MAAMzB,OAAM,GAAIkB,YAAY,CAACO,OAAO,CAAC,eAAe,CAAC;QAErD,MAAMa,QAAO,GAAI,MAAMwE,KAAK,CAAC,sCAAsCyD,UAAU,EAAE,EAAE;UAC7ExD,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YACL,eAAe,EAAE,UAAU1D,KAAK,EAAE;YAClC,YAAY,EAAEtD;UAClB;QACJ,CAAC,CAAC;QAEF,IAAIsC,QAAQ,CAACG,EAAE,EAAE;UACb,IAAI,CAACtD,YAAW,GAAI,IAAI,CAACA,YAAY,CAACyB,MAAM,CAACwJ,CAAA,IAAKA,CAAC,CAAChD,EAAC,KAAMmD,UAAU,CAAC;UACtE,IAAI,CAAChH,MAAM,CAACgF,OAAO,CAAC,sBAAsB,CAAC;QAC/C,OAAO;UACH,IAAI,CAAChF,MAAM,CAACR,KAAK,CAAC,mBAAmB,CAAC;QAC1C;MACJ,EAAE,OAAOA,KAAK,EAAE;QACZE,OAAO,CAACF,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACQ,MAAM,CAACR,KAAK,CAAC,0BAA0B,CAAC;MACjD;IACJ,CAAC;IACD2H,cAAcA,CAACzB,MAAM,EAAE;MACnB,MAAM0B,SAAQ,GAAI;QACd,WAAW,EAAE,sBAAsB;QACnC,eAAe,EAAE,wBAAwB;QACzC,OAAO,EAAE,iBAAiB;QAC1B,OAAO,EAAE,iBAAiB;QAC1B,aAAa,EAAE,mBAAmB;QAClC,eAAe,EAAE,0BAA0B;QAC3C,eAAe,EAAE,wBAAwB;QACzC,MAAM,EAAE;MACZ,CAAC;MACD,OAAOA,SAAS,CAAC1B,MAAM,KAAK,gBAAgB;IAChD,CAAC;IACD2B,eAAeA,CAAA,EAAG;MACd,MAAMC,WAAU,GAAIzH,QAAQ,CAAC0H,aAAa,CAAC,KAAK,CAAC;MACjDD,WAAW,CAACE,SAAQ,GAAI,eAAe;MACvC,MAAMC,aAAY,GAAI5H,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MACrDE,aAAa,CAAChF,IAAG,GAAI,MAAM;MAC3BgF,aAAa,CAACC,WAAU,GAAI,kBAAkB;MAC9CD,aAAa,CAACD,SAAQ,GAAI,gBAAgB;MAC1CC,aAAa,CAACE,QAAO,GAAI,IAAI;MAC7BF,aAAa,CAACG,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;QAC/C,IAAI,CAAClO,iBAAgB,GAAIkO,KAAK,CAACC,MAAM,CAACnH,KAAK;MAC/C,CAAC,CAAC;MACF2G,WAAW,CAACS,WAAW,CAACN,aAAa,CAAC;MACtC,MAAMO,aAAY,GAAInI,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MACrDS,aAAa,CAACvF,IAAG,GAAI,MAAM;MAC3BuF,aAAa,CAACN,WAAU,GAAI,cAAc;MAC1CM,aAAa,CAACR,SAAQ,GAAI,gBAAgB;MAC1CQ,aAAa,CAACL,QAAO,GAAI,IAAI;MAC7BK,aAAa,CAACJ,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;QAC/C,IAAI,CAACG,aAAY,GAAIH,KAAK,CAACC,MAAM,CAACnH,KAAK;MAC3C,CAAC,CAAC;MACF2G,WAAW,CAACS,WAAW,CAACC,aAAa,CAAC;MACtC,MAAMC,wBAAuB,GAAIpI,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MAChEU,wBAAwB,CAACxF,IAAG,GAAI,MAAM;MACtCwF,wBAAwB,CAACP,WAAU,GAAI,iBAAiB;MACxDO,wBAAwB,CAACT,SAAQ,GAAI,gBAAgB;MACrDS,wBAAwB,CAACN,QAAO,GAAI,IAAI;MACxCM,wBAAwB,CAACL,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;QAC1D,IAAI,CAAChO,mBAAkB,GAAIgO,KAAK,CAACC,MAAM,CAACnH,KAAK;MACjD,CAAC,CAAC;MACF2G,WAAW,CAACS,WAAW,CAACE,wBAAwB,CAAC;MACjD,MAAMC,iBAAgB,GAAIrI,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MACzDW,iBAAiB,CAACC,WAAU,GAAI,cAAc;MAC9Cb,WAAW,CAACS,WAAW,CAACG,iBAAiB,CAAC;MAC1C,MAAMrH,YAAW,GAAIhB,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MACpD1G,YAAY,CAAC4B,IAAG,GAAI,MAAM;MAC1B5B,YAAY,CAAC2G,SAAQ,GAAI,gBAAgB;MACzC3G,YAAY,CAAC8G,QAAO,GAAI,IAAI;MAC5B9G,YAAY,CAAC+G,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;QAC9C,IAAI,CAAC/N,iBAAgB,GAAI+N,KAAK,CAACC,MAAM,CAACnH,KAAK;MAC/C,CAAC,CAAC;MACF2G,WAAW,CAACS,WAAW,CAAClH,YAAY,CAAC;MACrC,MAAMuH,iBAAgB,GAAIvI,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MACzDa,iBAAiB,CAACD,WAAU,GAAI,aAAa;MAC7Cb,WAAW,CAACS,WAAW,CAACK,iBAAiB,CAAC;MAC1C,MAAMpH,YAAW,GAAInB,QAAQ,CAAC0H,aAAa,CAAC,UAAU,CAAC;MACvDvG,YAAY,CAAC0G,WAAU,GAAI,aAAa;MACxC1G,YAAY,CAACwG,SAAQ,GAAI,gBAAgB;MACzCxG,YAAY,CAAC2G,QAAO,GAAI,IAAI;MAC5B3G,YAAY,CAAC4G,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;QAC9C,IAAI,CAAC9N,iBAAgB,GAAI8N,KAAK,CAACC,MAAM,CAACnH,KAAK;MAC/C,CAAC,CAAC;MACF2G,WAAW,CAACS,WAAW,CAAC/G,YAAY,CAAC;MACrC,MAAMqH,kBAAiB,GAAIxI,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MAC1Dc,kBAAkB,CAACF,WAAU,GAAI,cAAc;MAC/Cb,WAAW,CAACS,WAAW,CAACM,kBAAkB,CAAC;MAC3C,MAAMC,iBAAgB,GAAIzI,QAAQ,CAAC0H,aAAa,CAAC,IAAI,CAAC;MACtDD,WAAW,CAACS,WAAW,CAACO,iBAAiB,CAAC;MAC1C,MAAMC,eAAc,GAAIA,CAAA,KAAM;QAC1B,MAAMjH,IAAG,GAAI;UAAE9C,IAAI,EAAE;QAAG;QACxB,MAAMgK,QAAO,GAAI3I,QAAQ,CAAC0H,aAAa,CAAC,IAAI,CAAC;QAC7C,MAAMkB,SAAQ,GAAI5I,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;QACjDkB,SAAS,CAAChG,IAAG,GAAI,MAAM;QACvBgG,SAAS,CAACf,WAAU,GAAI,YAAY;QACpCe,SAAS,CAACjB,SAAQ,GAAI,gBAAgB;QACtCiB,SAAS,CAACd,QAAO,GAAI,IAAI;QACzBc,SAAS,CAACb,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;UAC3CvG,IAAI,CAAC9C,IAAG,GAAIqJ,KAAK,CAACC,MAAM,CAACnH,KAAK;QAClC,CAAC,CAAC;QACF,IAAI,CAAC3G,aAAa,CAAC8D,IAAI,CAACwD,IAAI,CAAC;QAC7BkH,QAAQ,CAACT,WAAW,CAACU,SAAS,CAAC;QAC/BH,iBAAiB,CAACP,WAAW,CAACS,QAAQ,CAAC;MAC3C,CAAC;MACD,MAAME,sBAAqB,GAAI7I,QAAQ,CAAC0H,aAAa,CAAC,QAAQ,CAAC;MAC/DmB,sBAAsB,CAACP,WAAU,GAAI,WAAW;MAChDO,sBAAsB,CAACC,OAAM,GAAIJ,eAAe;MAChDjB,WAAW,CAACS,WAAW,CAACW,sBAAsB,CAAC;MAC/C,MAAME,wBAAuB,GAAI/I,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MAChEqB,wBAAwB,CAACT,WAAU,GAAI,iBAAiB;MACxDb,WAAW,CAACS,WAAW,CAACa,wBAAwB,CAAC;MACjD,MAAMC,uBAAsB,GAAIhJ,QAAQ,CAAC0H,aAAa,CAAC,IAAI,CAAC;MAC5DD,WAAW,CAACS,WAAW,CAACc,uBAAuB,CAAC;MAChD,MAAMC,sBAAqB,GAAIA,CAAA,KAAM;QACjC,MAAMC,WAAU,GAAI;UAAEvK,IAAI,EAAE;QAAG,CAAC;QAChC,MAAMwK,eAAc,GAAInJ,QAAQ,CAAC0H,aAAa,CAAC,IAAI,CAAC;QACpD,MAAM0B,gBAAe,GAAIpJ,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;QACxD0B,gBAAgB,CAACxG,IAAG,GAAI,MAAM;QAC9BwG,gBAAgB,CAACvB,WAAU,GAAI,iBAAiB;QAChDuB,gBAAgB,CAACzB,SAAQ,GAAI,gBAAgB;QAC7CyB,gBAAgB,CAACtB,QAAO,GAAI,IAAI;QAChCsB,gBAAgB,CAACrB,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;UAClDkB,WAAW,CAACvK,IAAG,GAAIqJ,KAAK,CAACC,MAAM,CAACnH,KAAK;QACzC,CAAC,CAAC;QACF,IAAI,CAAC1G,YAAY,CAAC6D,IAAI,CAACiL,WAAW,CAAC;QACnCC,eAAe,CAACjB,WAAW,CAACkB,gBAAgB,CAAC;QAC7CJ,uBAAuB,CAACd,WAAW,CAACiB,eAAe,CAAC;MACxD,CAAC;MACD,MAAME,4BAA2B,GAAIrJ,QAAQ,CAAC0H,aAAa,CAAC,QAAQ,CAAC;MACrE2B,4BAA4B,CAACf,WAAU,GAAI,mBAAmB;MAC9De,4BAA4B,CAACP,OAAM,GAAIG,sBAAsB;MAC7DxB,WAAW,CAACS,WAAW,CAACmB,4BAA4B,CAAC;MACrD,MAAMC,0BAAyB,GAAItJ,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MAClE4B,0BAA0B,CAAChB,WAAU,GAAI,sBAAsB;MAC/Db,WAAW,CAACS,WAAW,CAACoB,0BAA0B,CAAC;MACnD,MAAMC,wBAAuB,GAAIvJ,QAAQ,CAAC0H,aAAa,CAAC,KAAK,CAAC;MAC9D6B,wBAAwB,CAACC,KAAI,GAAI,qEAAqE;MACtG,MAAMC,qBAAoB,GAAIzJ,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MAC7D+B,qBAAqB,CAAC7G,IAAG,GAAI,aAAa;MAC1C6G,qBAAqB,CAAC9B,SAAQ,GAAI,gBAAgB;MAClD8B,qBAAqB,CAAC3B,QAAO,GAAI,IAAI;MACrC2B,qBAAqB,CAAC1B,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;QACvD,IAAI,CAAC3N,aAAY,GAAI2N,KAAK,CAACC,MAAM,CAACnH,KAAK;MAC3C,CAAC,CAAC;MACFyI,wBAAwB,CAACrB,WAAW,CAACuB,qBAAqB,CAAC;MAC3D,MAAMC,6BAA4B,GAAI1J,QAAQ,CAAC0H,aAAa,CAAC,MAAM,CAAC;MACpEgC,6BAA6B,CAACpB,WAAU,GAAI,KAAK;MACjDiB,wBAAwB,CAACrB,WAAW,CAACwB,6BAA6B,CAAC;MACnEjC,WAAW,CAACS,WAAW,CAACqB,wBAAwB,CAAC;MACjD,MAAMI,qBAAoB,GAAI3J,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MAC7DiC,qBAAqB,CAACrB,WAAU,GAAI,aAAa;MACjDb,WAAW,CAACS,WAAW,CAACyB,qBAAqB,CAAC;MAC9C,MAAM5H,gBAAe,GAAI/B,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MACxD3F,gBAAgB,CAACa,IAAG,GAAI,MAAM;MAC9Bb,gBAAgB,CAAC8F,WAAU,GAAI,aAAa;MAC5C9F,gBAAgB,CAAC4F,SAAQ,GAAI,gBAAgB;MAC7C5F,gBAAgB,CAAC+F,QAAO,GAAI,IAAI;MAChC/F,gBAAgB,CAACgG,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;QAClD,IAAI,CAAC1N,qBAAoB,GAAI0N,KAAK,CAACC,MAAM,CAACnH,KAAK;MACnD,CAAC,CAAC;MACF2G,WAAW,CAACS,WAAW,CAACnG,gBAAgB,CAAC;MACzC,MAAM6H,iBAAgB,GAAI5J,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MACzDkC,iBAAiB,CAACtB,WAAU,GAAI,YAAY;MAC5Cb,WAAW,CAACS,WAAW,CAAC0B,iBAAiB,CAAC;MAC1C,MAAM3H,YAAW,GAAIjC,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MACpDzF,YAAY,CAACW,IAAG,GAAI,MAAM;MAC1BX,YAAY,CAAC0F,SAAQ,GAAI,gBAAgB;MACzC1F,YAAY,CAAC6F,QAAO,GAAI,IAAI;MAC5B7F,YAAY,CAAC8F,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;QAC9C,IAAI,CAACzN,iBAAgB,GAAIyN,KAAK,CAACC,MAAM,CAACnH,KAAK;MAC/C,CAAC,CAAC;MACF2G,WAAW,CAACS,WAAW,CAACjG,YAAY,CAAC;MACrC,MAAM4H,qBAAoB,GAAI7J,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MAC7DmC,qBAAqB,CAACvB,WAAU,GAAI,YAAY;MAChDb,WAAW,CAACS,WAAW,CAAC2B,qBAAqB,CAAC;MAC9C,MAAM1H,gBAAe,GAAInC,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MACxDvF,gBAAgB,CAACS,IAAG,GAAI,MAAM;MAC9BT,gBAAgB,CAAC0F,WAAU,GAAI,YAAY;MAC3C1F,gBAAgB,CAACwF,SAAQ,GAAI,gBAAgB;MAC7CxF,gBAAgB,CAAC2F,QAAO,GAAI,IAAI;MAChC3F,gBAAgB,CAAC4F,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;QAClD,IAAI,CAACxN,qBAAoB,GAAIwN,KAAK,CAACC,MAAM,CAACnH,KAAK;MACnD,CAAC,CAAC;MACF2G,WAAW,CAACS,WAAW,CAAC/F,gBAAgB,CAAC;MACzC,MAAM2H,mBAAkB,GAAI9J,QAAQ,CAAC0H,aAAa,CAAC,OAAO;MAC1DoC,mBAAmB,CAACxB,WAAU,GAAI,gBAAe;MACjDb,WAAW,CAACS,WAAW,CAAC4B,mBAAmB;MAC3C,MAAMC,SAAQ,GAAI/J,QAAQ,CAAC0H,aAAa,CAAC,KAAK,CAAC;MAC/CqC,SAAS,CAACpC,SAAQ,GAAI,cAAc;MACpCoC,SAAS,CAACP,KAAK,CAACQ,OAAM,GAAI,MAAM;MAChCD,SAAS,CAACP,KAAK,CAACS,aAAY,GAAI,KAAK;MACrCF,SAAS,CAACP,KAAK,CAACU,GAAE,GAAI,MAAM;MAC5BH,SAAS,CAACP,KAAK,CAACW,UAAS,GAAI,QAAQ;MACrCJ,SAAS,CAACP,KAAK,CAACY,SAAQ,GAAI,KAAK;MACjC,MAAMC,WAAU,GAAIrK,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MACnD2C,WAAW,CAACzH,IAAG,GAAI,aAAa;MAChCyH,WAAW,CAACxC,WAAU,GAAI,gBAAe;MACzCwC,WAAW,CAACvC,QAAO,GAAI,IAAI;MAC3BuC,WAAW,CAACtC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;QAC7C,IAAI,CAACvN,mBAAkB,GAAIuN,KAAK,CAACC,MAAM,CAACnH,KAAK;MACjD,CAAC,CAAC;MACFiJ,SAAS,CAAC7B,WAAW,CAACmC,WAAW,CAAC;MAClC,MAAMC,iBAAgB,GAAItK,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MACzD4C,iBAAiB,CAAChC,WAAU,GAAI,MAAM;MACtCyB,SAAS,CAACQ,SAAQ,IAAKD,iBAAiB,CAACE,SAAS;MAClD/C,WAAW,CAACS,WAAW,CAAC6B,SAAS,CAAC;MAClC,MAAMU,0BAAyB,GAAIzK,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MAClE+C,0BAA0B,CAACnC,WAAU,GAAI,cAAc;MACvDb,WAAW,CAACS,WAAW,CAACuC,0BAA0B,CAAC;MACnD,MAAMC,yBAAwB,GAAI1K,QAAQ,CAAC0H,aAAa,CAAC,IAAI,CAAC;MAC9DD,WAAW,CAACS,WAAW,CAACwC,yBAAyB,CAAC;MAClD,MAAMC,8BAA6B,GAAIA,CAAA,KAAM;QAC1C,MAAMC,QAAO,GAAI;UAAEhR,IAAI,EAAE,EAAE;UAAEgJ,IAAI,EAAE,EAAE;UAAEiI,IAAI,EAAE,EAAE;UAAEhI,MAAM,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;QACtF,MAAM+H,gBAAe,GAAI9K,QAAQ,CAAC0H,aAAa,CAAC,KAAK,CAAC;QACtDoD,gBAAgB,CAACnD,SAAQ,GAAI,oBAAoB;QACjDmD,gBAAgB,CAACtB,KAAI,GACrB,mDAAkD,GAClD,iEAAgE,GAChE,uEAAsE,GACtE,yBAAyB;QACzB,MAAMuB,eAAc,GAAI/K,QAAQ,CAAC0H,aAAa,CAAC,KAAK,CAAC;QACrDqD,eAAe,CAACvB,KAAK,CAACQ,OAAM,GAAI,MAAK;QACrCe,eAAe,CAACd,aAAY,GAAI,KAAI;QACpCc,eAAe,CAACvB,KAAK,CAACU,GAAE,GAAI,MAAK;QACjC,MAAMc,iBAAgB,GAAIhL,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;QACzDsD,iBAAiB,CAAC1C,WAAU,GAAI,YAAY;QAC5CyC,eAAe,CAAC7C,WAAW,CAAC8C,iBAAiB,CAAC;QAC9C,MAAMC,iBAAgB,GAAIjL,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;QACzDuD,iBAAiB,CAACrI,IAAG,GAAI,MAAM;QAC/BqI,iBAAiB,CAACpD,WAAU,GAAI,iBAAiB;QACjDoD,iBAAiB,CAACnD,QAAO,GAAI,IAAI;QACjCmD,iBAAiB,CAAClD,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;UACnD4C,QAAQ,CAAChR,IAAG,GAAIoO,KAAK,CAACC,MAAM,CAACnH,KAAK;QACtC,CAAC,CAAC;QACFiK,eAAe,CAAC7C,WAAW,CAAC+C,iBAAiB,CAAC;QAC9CH,gBAAgB,CAAC5C,WAAW,CAAC6C,eAAe,CAAC;QAC7C,MAAMG,eAAc,GAAIlL,QAAQ,CAAC0H,aAAa,CAAC,KAAK;QACpDwD,eAAe,CAAC1B,KAAK,CAACQ,OAAM,GAAI,MAAK;QACrCkB,eAAe,CAAC1B,KAAK,CAACS,aAAY,GAAI,KAAI;QAC1CiB,eAAe,CAAC1B,KAAK,CAACU,GAAE,GAAI,MAAK;QACjC,MAAMiB,iBAAgB,GAAInL,QAAQ,CAAC0H,aAAa,CAAC,OAAO;QACxDyD,iBAAiB,CAAC7C,WAAU,GAAI,YAAW;QAC3C,MAAM8C,iBAAgB,GAAIpL,QAAQ,CAAC0H,aAAa,CAAC,OAAO;QACxD0D,iBAAiB,CAACxI,IAAG,GAAI,MAAK;QAC9BwI,iBAAiB,CAACvD,WAAU,GAAI,iBAAgB;QAChDuD,iBAAiB,CAACtD,QAAO,GAAI,IAAI;QACjCsD,iBAAiB,CAACrD,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;UACnD4C,QAAQ,CAAChI,IAAG,GAAIoF,KAAK,CAACC,MAAM,CAACnH,KAAK;QACtC,CAAC,CAAC;QACFoK,eAAe,CAAChD,WAAW,CAACiD,iBAAiB;QAC7CD,eAAe,CAAChD,WAAW,CAACkD,iBAAiB;QAC7CN,gBAAgB,CAAC5C,WAAW,CAACgD,eAAe;QAC5C,MAAMG,iBAAgB,GAAIrL,QAAQ,CAAC0H,aAAa,CAAC,KAAK;QACtD2D,iBAAiB,CAAC7B,KAAK,CAACQ,OAAM,GAAI,MAAK;QACvCqB,iBAAiB,CAAC7B,KAAK,CAACS,aAAY,GAAI,KAAI;QAC5CoB,iBAAiB,CAAC7B,KAAK,CAACU,GAAE,GAAI,MAAK;QACnC,MAAMoB,mBAAkB,GAAItL,QAAQ,CAAC0H,aAAa,CAAC,OAAO;QAC1D4D,mBAAmB,CAAChD,WAAU,GAAI,YAAW;QAC7C+C,iBAAiB,CAACnD,WAAW,CAACoD,mBAAmB;QACjD,MAAMC,mBAAkB,GAAIvL,QAAQ,CAAC0H,aAAa,CAAC,OAAO;QAC1D6D,mBAAmB,CAAC3I,IAAG,GAAI,MAAK;QAChC2I,mBAAmB,CAAC1D,WAAU,GAAI,iBAAgB;QAClD0D,mBAAmB,CAACzD,QAAO,GAAI,IAAI;QACnCyD,mBAAmB,CAACxD,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;UACrD4C,QAAQ,CAAC/H,MAAK,GAAImF,KAAK,CAACC,MAAM,CAACnH,KAAK;QACxC,CAAC,CAAC;QACFuK,iBAAiB,CAACnD,WAAW,CAACqD,mBAAmB;QACjDT,gBAAgB,CAAC5C,WAAW,CAACmD,iBAAiB;QAC9C,MAAMG,gBAAe,GAAIxL,QAAQ,CAAC0H,aAAa,CAAC,KAAK;QACrD8D,gBAAgB,CAAChC,KAAK,CAACQ,OAAM,GAAI,MAAK;QACtCwB,gBAAgB,CAAChC,KAAK,CAACS,aAAY,GAAI,KAAI;QAC3CuB,gBAAgB,CAAChC,KAAK,CAACU,GAAE,GAAI,MAAK;QAClC,MAAMuB,kBAAiB,GAAIzL,QAAQ,CAAC0H,aAAa,CAAC,OAAO;QACzD+D,kBAAkB,CAACnD,WAAU,GAAI,YAAW;QAC5CkD,gBAAgB,CAACtD,WAAW,CAACuD,kBAAkB;QAC/C,MAAMC,kBAAiB,GAAI1L,QAAQ,CAAC0H,aAAa,CAAC,OAAO;QACzDgE,kBAAkB,CAAC9I,IAAG,GAAI,MAAK;QAC/B8I,kBAAkB,CAAC7D,WAAW,GAAE,iBAAgB;QAChD6D,kBAAkB,CAAC5D,QAAO,GAAI,IAAI;QAClC4D,kBAAkB,CAAC3D,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;UACpD4C,QAAQ,CAAC9H,KAAI,GAAIkF,KAAK,CAACC,MAAM,CAACnH,KAAK;QACvC,CAAC,CAAC;QACF0K,gBAAgB,CAACtD,WAAW,CAACwD,kBAAkB;QAC/CZ,gBAAgB,CAAC5C,WAAW,CAACsD,gBAAgB;QAC7C,MAAMG,sBAAqB,GAAI3L,QAAQ,CAAC0H,aAAa,CAAC,KAAK;QAC3DiE,sBAAsB,CAACnC,KAAK,CAACQ,OAAM,GAAI,MAAK;QAC5C2B,sBAAsB,CAACnC,KAAK,CAACS,aAAY,GAAI,KAAI;QACjD0B,sBAAsB,CAACnC,KAAK,CAACU,GAAE,GAAI,MAAK;QACxC,MAAM0B,wBAAuB,GAAI5L,QAAQ,CAAC0H,aAAa,CAAC,OAAO;QAC/DkE,wBAAwB,CAACtD,WAAU,GAAI,WAAU;QACjDqD,sBAAsB,CAACzD,WAAW,CAAC0D,wBAAwB;QAC3D,MAAMC,wBAAuB,GAAI7L,QAAQ,CAAC0H,aAAa,CAAC,OAAO;QAC/DmE,wBAAwB,CAACjJ,IAAG,GAAI,MAAK;QACrCiJ,wBAAwB,CAAChE,WAAW,GAAE,gBAAe;QACrDgE,wBAAwB,CAAC/D,QAAO,GAAI,IAAI;QACxC+D,wBAAwB,CAAC9D,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;UAC1D4C,QAAQ,CAAC7H,WAAU,GAAIiF,KAAK,CAACC,MAAM,CAACnH,KAAK;QAC7C,CAAC,CAAC;QACF,IAAI,CAACpG,iBAAiB,CAACuD,IAAI,CAAC2M,QAAQ,CAAC;QACrCe,sBAAsB,CAACzD,WAAW,CAAC2D,wBAAwB;QAC3Df,gBAAgB,CAAC5C,WAAW,CAACyD,sBAAsB;QACnDjB,yBAAyB,CAACxC,WAAW,CAAC4C,gBAAgB,CAAC;MAC1D,CAAC;MACD,MAAMgB,oCAAmC,GAAI9L,QAAQ,CAAC0H,aAAa,CAAC,QAAQ,CAAC;MAC7EoE,oCAAoC,CAACxD,WAAU,GAAI,WAAU;MAC7DwD,oCAAoC,CAAChD,OAAM,GAAI6B,8BAA8B;MAC7ElD,WAAW,CAACS,WAAW,CAAC4D,oCAAoC,CAAC;MAC7D,MAAMC,wBAAuB,GAAI/L,QAAQ,CAAC0H,aAAa,CAAC,OAAO;MAC/DqE,wBAAwB,CAACzD,WAAU,GAAI,mBAAkB;MACzDb,WAAW,CAACS,WAAW,CAAC6D,wBAAwB;MAChD,MAAMC,cAAa,GAAIhM,QAAQ,CAAC0H,aAAa,CAAC,KAAK;MACnDsE,cAAc,CAACrE,SAAQ,GAAI,cAAa;MACxCqE,cAAc,CAACxC,KAAI,GACf,qEAAoE;MACxE,MAAMyC,gBAAe,GAAIjM,QAAQ,CAAC0H,aAAa,CAAC,OAAO;MACvDuE,gBAAgB,CAACrJ,IAAG,GAAI,aAAY;MACpCqJ,gBAAgB,CAACpE,WAAU,GAAI,oBAAmB;MAClDoE,gBAAgB,CAACnE,QAAO,GAAI,IAAI;MAChCmE,gBAAgB,CAAClE,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;QAClD,IAAI,CAACrN,mBAAkB,GAAIqN,KAAK,CAACC,MAAM,CAACnH,KAAK;MACjD,CAAC,CAAC;MACFmL,gBAAgB,CAACtE,SAAQ,GAAI,cAAa;MAC1CqE,cAAc,CAAC9D,WAAW,CAAC+D,gBAAgB;MAC3CD,cAAc,CAAC9D,WAAW,CAACoC,iBAAiB;MAC5C7C,WAAW,CAACS,WAAW,CAAC8D,cAAc;MACtC,MAAME,0BAAyB,GAAIlM,QAAQ,CAAC0H,aAAa,CAAC,OAAO;MACjEwE,0BAA0B,CAAC5D,WAAU,GAAI,oBAAmB;MAC5Db,WAAW,CAACS,WAAW,CAACgE,0BAA0B;MAClD,MAAMC,yBAAwB,GAAInM,QAAQ,CAAC0H,aAAa,CAAC,IAAI,CAAC;MAC9DD,WAAW,CAACS,WAAW,CAACiE,yBAAyB,CAAC;MAClD,MAAMC,uBAAsB,GAAIA,CAAA,KAAM;QAClC,MAAMC,YAAW,GAAI;UAAEpR,WAAW,EAAE;QAAG,CAAC;QACxC,MAAMqR,gBAAe,GAAItM,QAAQ,CAAC0H,aAAa,CAAC,IAAI,CAAC;QACrD,MAAM6E,iBAAgB,GAAIvM,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;QACzD6E,iBAAiB,CAAC3J,IAAG,GAAI,MAAM;QAC/B2J,iBAAiB,CAAC1E,WAAU,GAAI,uBAAuB;QACvD0E,iBAAiB,CAAC5E,SAAQ,GAAI,gBAAgB;QAC9C4E,iBAAiB,CAACzE,QAAO,GAAI,IAAI;QACjCyE,iBAAiB,CAACxE,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;UACnDqE,YAAY,CAACpR,WAAU,GAAI+M,KAAK,CAACC,MAAM,CAACnH,KAAK;QACjD,CAAC,CAAC;QACF,IAAI,CAAClG,cAAc,CAACqD,IAAI,CAACoO,YAAY,CAAC;QACtCC,gBAAgB,CAACpE,WAAW,CAACqE,iBAAiB,CAAC;QAC/CJ,yBAAyB,CAACjE,WAAW,CAACoE,gBAAgB,CAAC;MAC3D,CAAC;MACD,MAAME,6BAA4B,GAAIxM,QAAQ,CAAC0H,aAAa,CAAC,QAAQ,CAAC;MACtE8E,6BAA6B,CAAClE,WAAU,GAAI,mBAAmB;MAC/DkE,6BAA6B,CAAC1D,OAAM,GAAIsD,uBAAuB;MAC/D3E,WAAW,CAACS,WAAW,CAACsE,6BAA6B;MACrD,MAAMC,kBAAiB,GAAIzM,QAAQ,CAAC0H,aAAa,CAAC,OAAO,CAAC;MAC1D+E,kBAAkB,CAACnE,WAAU,GAAI,aAAa;MAC9Cb,WAAW,CAACS,WAAW,CAACuE,kBAAkB,CAAC;MAC3C,MAAMC,kBAAiB,GAAI1M,QAAQ,CAAC0H,aAAa,CAAC,QAAQ,CAAC;MAC3DgF,kBAAkB,CAAC/E,SAAQ,GAAI,gBAAgB;MAC/C+E,kBAAkB,CAAC5E,QAAO,GAAI,IAAI;;MAElC;MACA,MAAM6E,aAAY,GAAI3M,QAAQ,CAAC0H,aAAa,CAAC,QAAQ,CAAC;MACtDiF,aAAa,CAAC7L,KAAI,GAAI,EAAE;MACxB6L,aAAa,CAACrE,WAAU,GAAI,kBAAkB;MAC9CqE,aAAa,CAACC,QAAO,GAAI,IAAI;MAC7BD,aAAa,CAACE,QAAO,GAAI,IAAI;MAC7BH,kBAAkB,CAACxE,WAAW,CAACyE,aAAa,CAAC;MAE7C,MAAMG,MAAK,GAAI,CACX,WAAW,EAAG,eAAe,EAAE,OAAO,EAAE,OAAO,EAC/C,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,MAAK,CACzD;MACDA,MAAM,CAACtK,OAAO,CAAC/E,KAAI,IAAK;QACpB,MAAMsP,MAAK,GAAI/M,QAAQ,CAAC0H,aAAa,CAAC,QAAQ,CAAC;QAC/CqF,MAAM,CAACjM,KAAI,GAAIrD,KAAK;QACpBsP,MAAM,CAACzE,WAAU,GAAI7K,KAAK;QAC1BiP,kBAAkB,CAACxE,WAAW,CAAC6E,MAAM,CAAC;MAC1C,CAAC,CAAC;MACFL,kBAAkB,CAAC3E,gBAAgB,CAAC,QAAQ,EAAGC,KAAK,IAAK;QACrD,IAAI,CAACnN,cAAa,GAAImN,KAAK,CAACC,MAAM,CAACnH,KAAK;QACxC;QACA,IAAI,CAAChG,cAAa,GAAI;UAClBC,KAAK,EAAE,IAAI,CAAChB,iBAAiB;UAC7BiB,KAAK,EAAE,IAAI,CAAClB,iBAAiB;UAC7BmB,WAAW,EAAE,IAAI,CAACjB,mBAAmB;UACrCkB,IAAI,EAAE,IAAI,CAACjB,iBAAiB;UAC5BkB,IAAI,EAAE,IAAI,CAACjB,iBAAiB;UAC5BkB,KAAK,EAAE,IAAI,CAACjB,aAAa;UACzBkB,QAAQ,EAAE,IAAI,CAACjB,YAAY;UAC3ByL,MAAM,EAAE,IAAI,CAAChL,cAAc;UAC3B8K,WAAW,EAAE,IAAI,CAACqH,mBAAmB;UACrCC,WAAW,EAAE,IAAI,CAAC7Q;QACtB,CAAC;MACL,CAAC,CAAC;MACF,MAAM8Q,oBAAmB,GAAIlN,QAAQ,CAAC0H,aAAa,CAAC,QAAQ,CAAC;MAC7DwF,oBAAoB,CAACvF,SAAQ,GAAI,wBAAwB;MACzDuF,oBAAoB,CAAC5E,WAAU,GAAI,YAAY;MAC/C4E,oBAAoB,CAACpE,OAAM,GAAI,MAAM;QACjCrB,WAAW,CAACrC,MAAM,CAAC,CAAC;MACxB,CAAC;MACDqC,WAAW,CAACS,WAAW,CAACwE,kBAAkB,CAAC;MAC3CjF,WAAW,CAACS,WAAW,CAACgF,oBAAoB,CAAC;MAC7C,IAAI,CAACtR,UAAU,CAACqC,IAAI,CAACwJ,WAAW,CAAC;MACjCzH,QAAQ,CAACQ,aAAa,CAAC,kBAAkB,CAAC,CAAC0H,WAAW,CAACT,WAAW,CAAC;IACvE,CAAC;IACD0F,kBAAkBA,CAACnF,KAAK,EAAE;MACtB,MAAMoF,WAAU,GAAIpF,KAAK,CAACC,MAAM,CAACoF,OAAO,CAAC,eAAe,CAAC;MACzD,IAAI,CAACD,WAAW,EAAE;QACd,IAAI,CAACtR,YAAW,GAAI,KAAK;MAC7B;IACJ,CAAC;IACDwR,cAAcA,CAACC,IAAI,EAAE;MACjB,IAAI,CAACpR,WAAU,GAAIoR,IAAI;MACvB;MACA,IAAIA,IAAG,KAAM,MAAM,EAAE;QACjB,IAAI,CAAClI,iBAAiB,CAAC,CAAC;MAC5B;IACJ,CAAC;IACDmI,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACnI,iBAAiB,CAAC,CAAC;IAC5B,CAAC;IACD,MAAMoI,mBAAmBA,CAAA,EAAG;MACxB,IAAI;QACA,MAAMvN,KAAI,GAAIpC,YAAY,CAACO,OAAO,CAAC,YAAY,CAAC;QAChD,IAAI,CAAC6B,KAAK,EAAE;QAEZ,MAAMhB,QAAO,GAAI,MAAMwE,KAAK,CAAC,qCAAqC,EAAE;UAChEE,OAAO,EAAE;YACL,eAAe,EAAE,UAAU1D,KAAK,EAAE;YAClC,cAAc,EAAE;UACpB;QACJ,CAAC,CAAC;QAEF,IAAIhB,QAAQ,CAACG,EAAE,EAAE;UACb,MAAMxF,IAAG,GAAI,MAAMqF,QAAQ,CAACI,IAAI,CAAC,CAAC;UAClC,IAAI,CAAClD,eAAc,GAAIvC,IAAI,CAACqL,gBAAe,IAAK,EAAE;QACtD,OAAO,IAAIhG,QAAQ,CAAC2G,MAAK,KAAM,GAAG,EAAE;UAChC;UACA,IAAI,CAACzJ,eAAc,GAAI,EAAE;QAC7B;MACJ,EAAE,OAAOuD,KAAK,EAAE;QACZE,OAAO,CAACF,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD;MACJ;IACJ,CAAC;IACD+N,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACnQ,kBAAkB,CAACsB,MAAK,KAAM,CAAC,EAAE;QACtC,IAAI,CAACsB,MAAM,CAACR,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACJ;;MAEA;MACA,MAAMiE,OAAM,GAAI,CACZ,cAAc,EACd,aAAa,EACb,YAAY,EACZ,cAAc,EACd,aAAa,EACb,UAAU,EACV,eAAe,EACf,eAAc,CACjB;;MAED;MACA,MAAM+J,OAAM,GAAI,IAAI,CAACpQ,kBAAkB,CAACgE,GAAG,CAACrE,QAAO,IAAK;QACpD,OAAO,CACH,IAAIA,QAAQ,CAACnC,KAAI,IAAK,EAAE,GAAG,EAC3B,IAAImC,QAAQ,CAACgH,UAAS,IAAK,EAAE,GAAG,EAChC,IAAIhH,QAAQ,CAACiH,iBAAgB,IAAK,EAAE,GAAG,EACvCjH,QAAQ,CAACkH,aAAY,GAAI,IAAIgC,IAAI,CAAClJ,QAAQ,CAACkH,aAAa,CAAC,CAACwJ,kBAAkB,CAAC,OAAO,IAAI,EAAE,EAC1F,IAAI1Q,QAAQ,CAACO,KAAI,IAAK,EAAE,GAAG,EAC3B,IAAIP,QAAQ,CAACuI,eAAe,EAAEE,WAAU,IAAK,EAAE,GAAG,EAClD,IAAIzI,QAAQ,CAACuI,eAAe,EAAEP,gBAAe,IAAK,EAAE,GAAG,EACvDhI,QAAQ,CAACuI,eAAe,EAAEG,UAAS,GACnC,IAAIQ,IAAI,CAAClJ,QAAQ,CAACuI,eAAe,CAACG,UAAU,CAAC,CAACgI,kBAAkB,CAAC,OAAO,IAAI,EAAC,CAChF,CAACzK,IAAI,CAAC,GAAG,CAAC;MACf,CAAC,CAAC;;MAEF;MACA,MAAM0K,UAAS,GAAI,CAACjK,OAAO,CAACT,IAAI,CAAC,GAAG,CAAC,EAAE,GAAGwK,OAAO,CAAC,CAACxK,IAAI,CAAC,IAAI,CAAC;;MAE7D;MACA,MAAM2K,GAAE,GAAI,QAAQ;MACpB,MAAMC,UAAS,GAAID,GAAE,GAAID,UAAU;;MAEnC;MACA,MAAMG,IAAG,GAAI,IAAIC,IAAI,CAAC,CAACF,UAAU,CAAC,EAAE;QAAEnL,IAAI,EAAE;MAA0B,CAAC,CAAC;MACxE,MAAMsL,IAAG,GAAIlO,QAAQ,CAAC0H,aAAa,CAAC,GAAG,CAAC;MAExC,IAAIwG,IAAI,CAACC,QAAO,KAAMC,SAAS,EAAE;QAC7B,MAAMC,GAAE,GAAIC,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;QACrCE,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;;QAE9B;QACA,MAAMI,WAAU,GAAI,IAAIrI,IAAI,CAAC,CAAC,CAACsI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1D,MAAMC,UAAS,GAAI,IAAI,CAACvS,cAAa,GAAI,IAAI,IAAI,CAACA,cAAc,EAAC,GAAI,gBAAgB;QACrF,MAAMwS,QAAO,GAAI,uBAAuBD,UAAU,IAAIH,WAAW,MAAM;QAEvEP,IAAI,CAACM,YAAY,CAAC,UAAU,EAAEK,QAAQ,CAAC;QACvCX,IAAI,CAAC1E,KAAK,CAACsF,UAAS,GAAI,QAAQ;QAChC9O,QAAQ,CAAC6D,IAAI,CAACqE,WAAW,CAACgG,IAAI,CAAC;QAC/BA,IAAI,CAACa,KAAK,CAAC,CAAC;QACZ/O,QAAQ,CAAC6D,IAAI,CAACmL,WAAW,CAACd,IAAI,CAAC;QAE/B,IAAI,CAAC/N,MAAM,CAACgF,OAAO,CAAC,YAAY,IAAI,CAAC5H,kBAAkB,CAACsB,MAAM,cAAc,CAAC;MACjF,OAAO;QACH,IAAI,CAACsB,MAAM,CAACR,KAAK,CAAC,+BAA+B,CAAC;MACtD;IACJ,CAAC;IACD;IACAuG,wBAAwBA,CAACjJ,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;MACjD,IAAI,CAACJ,mBAAkB,GAAI;QACvBC,GAAG,EAAE,EAAE;QACPC,MAAM,EAAEA,MAAM;QACdC,QAAQ,EAAEA,QAAQ;QAClBC,QAAQ,EAAEA;MACd,CAAC;MACD,IAAI,CAACL,mBAAkB,GAAI,IAAI;MAC/B;MACA,IAAI,CAACmS,SAAS,CAAC,MAAM;QACjB,MAAMC,QAAO,GAAIlP,QAAQ,CAACmP,cAAc,CAAC,YAAY,CAAC;QACtD,IAAID,QAAQ,EAAE;UACVA,QAAQ,CAACE,KAAK,CAAC,CAAC;QACpB;MACJ,CAAC,CAAC;IACN,CAAC;IACDC,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAACvS,mBAAkB,GAAI,KAAK;MAChC,IAAI,CAACC,mBAAkB,GAAI;QACvBC,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;MACd,CAAC;IACL,CAAC;IACD,MAAMmS,gBAAgBA,CAAA,EAAG;MACrB,MAAMC,UAAS,GAAI,IAAI,CAACxS,mBAAmB,CAACC,GAAG;MAC/C,MAAMwS,SAAQ,GAAI1R,YAAY,CAACO,OAAO,CAAC,eAAe,CAAC;MAEvD,IAAIkR,UAAS,KAAMC,SAAS,EAAE;QAC1B,IAAI,CAACrP,MAAM,CAACR,KAAK,CAAC,4BAA4B,CAAC;QAC/C;MACJ;;MAEA;MACA,MAAMxC,QAAO,GAAI,IAAI,CAACJ,mBAAmB,CAACI,QAAQ;;MAElD;MACA,IAAI,CAACkS,oBAAoB,CAAC,CAAC;;MAE3B;MACA,IAAIlS,QAAQ,EAAE;QACV,IAAI,CAAC8R,SAAS,CAAC,YAAY;UACvB,MAAM9R,QAAQ,CAAC,CAAC;QACpB,CAAC,CAAC;MACN;IACJ,CAAC;IACDsS,YAAYA,CAAC1L,IAAI,EAAE;MACf,IAAI;QACA;QACA,MAAM2L,OAAM,GAAI,0BAA0B3L,IAAI,CAAC4L,QAAQ,EAAE;;QAEzD;QACA,MAAMzB,IAAG,GAAIlO,QAAQ,CAAC0H,aAAa,CAAC,GAAG,CAAC;QACxCwG,IAAI,CAAC0B,IAAG,GAAIF,OAAO;QACnBxB,IAAI,CAACC,QAAO,GAAIpK,IAAI,CAAC8L,SAAS;QAC9B3B,IAAI,CAACjG,MAAK,GAAI,QAAQ,EAAE;QACxBiG,IAAI,CAAC1E,KAAK,CAACQ,OAAM,GAAI,MAAM;;QAE3B;QACAhK,QAAQ,CAAC6D,IAAI,CAACqE,WAAW,CAACgG,IAAI,CAAC;QAC/BA,IAAI,CAACa,KAAK,CAAC,CAAC;QACZ/O,QAAQ,CAAC6D,IAAI,CAACmL,WAAW,CAACd,IAAI,CAAC;QAE/B,IAAI,CAAC/N,MAAM,CAACgF,OAAO,CAAC,mBAAmBpB,IAAI,CAAC8L,SAAS,EAAE,CAAC;MAC5D,EAAE,OAAOlQ,KAAK,EAAE;QACZE,OAAO,CAACF,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QACvC,IAAI,CAACQ,MAAM,CAACR,KAAK,CAAC,uBAAuBoE,IAAI,CAAC8L,SAAS,EAAE,CAAC;MAC9D;IACJ,CAAC;IACDC,gBAAgBA,CAACC,QAAQ,EAAE;MACvB;MACA,IAAIA,QAAQ,CAAClR,MAAK,GAAI,EAAE,EAAE;QACtB,MAAMmR,SAAQ,GAAID,QAAQ,CAACpB,KAAK,CAAC,GAAG,CAAC,CAACsB,GAAG,CAAC,CAAC;QAC3C,MAAMC,cAAa,GAAIH,QAAQ,CAACI,SAAS,CAAC,CAAC,EAAEJ,QAAQ,CAACK,WAAW,CAAC,GAAG,CAAC,CAAC;QACvE,OAAOF,cAAc,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,KAAI,GAAI,GAAE,GAAIH,SAAS;MACpE;MACA,OAAOD,QAAQ;IACnB,CAAC;IACDM,iBAAiBA,CAACnT,QAAQ,EAAE;MACxB,IAAI,CAACG,gBAAe,GAAIH,QAAQ;MAChC,IAAI,CAACE,iBAAgB,GAAI,IAAI;MAC7B;MACA4C,QAAQ,CAAC6D,IAAI,CAAC2F,KAAK,CAAC8G,QAAO,GAAI,QAAQ;IAC3C,CAAC;IACDC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACnT,iBAAgB,GAAI,KAAK;MAC9B,IAAI,CAACC,gBAAe,GAAI,IAAI;MAC5B;MACA2C,QAAQ,CAAC6D,IAAI,CAAC2F,KAAK,CAAC8G,QAAO,GAAI,MAAM;IACzC,CAAC;IACDE,UAAUA,CAACC,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,EAAE,OAAO,UAAU;MAClC,MAAMvV,IAAG,GAAI,IAAIkL,IAAI,CAACqK,UAAU,CAAC;MACjC,OAAOvV,IAAI,CAAC0S,kBAAkB,CAAC,OAAO,EAAE;QACpCvH,IAAI,EAAE,SAAS;QACfE,KAAK,EAAE,MAAM;QACbI,GAAG,EAAE;MACT,CAAC,CAAC;IACN,CAAC;IACD+J,UAAUA,CAACC,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,EAAE,OAAO,UAAU;MAClC,OAAOA,UAAU;IACrB,CAAC;IACDC,cAAcA,CAAC/N,MAAM,EAAE;MACnB,IAAI,CAACA,MAAM,EAAE,OAAO,UAAU;MAC9B,OAAO,IAAIgO,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAAClO,MAAM,IAAI,MAAM;IACjE,CAAC;IACD,MAAMmO,sBAAsBA,CAACC,SAAS,EAAE;MACpC,IAAI;QACA;QACA,IAAIC,QAAQ;QACZ,IAAID,SAAS,CAAClL,QAAQ,CAAC,UAAU,CAAC,EAAE;UAChCmL,QAAO,GAAI,CAAC,MAAM,MAAM,CAAC,uBAAuB,CAAC,EAAEC,OAAO;QAC9D,OAAO,IAAIF,SAAS,CAAClL,QAAQ,CAAC,WAAW,CAAC,EAAE;UACxCmL,QAAO,GAAI,CAAC,MAAM,MAAM,CAAC,wBAAwB,CAAC,EAAEC,OAAO;QAC/D;QAEA,IAAI,CAACD,QAAQ,EAAE,MAAM,IAAIE,KAAK,CAAC,4BAA4BH,SAAS,EAAE,CAAC;QAEvE,MAAM/R,QAAO,GAAI,MAAMwE,KAAK,CAACwN,QAAQ,CAAC;QACtC,IAAI,CAAChS,QAAQ,CAACG,EAAE,EAAE,MAAM,IAAI+R,KAAK,CAAC,yBAAyBF,QAAQ,EAAE,CAAC;QACtE,OAAO,MAAMhS,QAAQ,CAACmS,WAAW,CAAC,CAAC;MACvC,EAAE,OAAO1R,KAAK,EAAE;QACZE,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,OAAO,IAAI;MACf;IACJ,CAAC;IACD,MAAM2R,YAAYA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAACjU,gBAAgB,EAAE;MAE5B,MAAMH,QAAO,GAAI,IAAI,CAACG,gBAAgB;;MAEtC;MACA,MAAMkU,aAAY,GAAI,MAAM,IAAI,CAACP,sBAAsB,CAAC,UAAU,CAAC;MACnE,MAAMQ,cAAa,GAAI,MAAM,IAAI,CAACR,sBAAsB,CAAC,WAAW,CAAC;;MAErE;MACA,MAAMS,QAAO,GAAI,EAAE;;MAEnB;MACA,MAAMC,gBAAe,GAAI,EAAE;;MAE3B;MACA,IAAIH,aAAY,IAAKC,cAAc,EAAE;QACjC;QACA,MAAMG,WAAU,GAAI,IAAIvY,KAAK,CAAC;UAC1BwY,KAAK,EAAE;YACHC,IAAI,EAAE,GAAG;YACTjP,IAAI,EAAErJ,SAAS,CAACuY;UACpB,CAAC;UACDC,OAAO,EAAE;YACLC,GAAG,EAAE;cAAExI,KAAK,EAAE/P,WAAW,CAACwY;YAAK,CAAC;YAChCC,MAAM,EAAE;cAAE1I,KAAK,EAAE/P,WAAW,CAACwY;YAAK,CAAC;YACnCE,IAAI,EAAE;cAAE3I,KAAK,EAAE/P,WAAW,CAACwY;YAAK,CAAC;YACjCG,KAAK,EAAE;cAAE5I,KAAK,EAAE/P,WAAW,CAACwY;YAAK,CAAC;YAClCI,gBAAgB,EAAE;cAAE7I,KAAK,EAAE/P,WAAW,CAACwY;YAAK,CAAC;YAC7CK,cAAc,EAAE;cAAE9I,KAAK,EAAE/P,WAAW,CAACwY;YAAK;UAC9C,CAAC;UACDM,IAAI,EAAE,CACF,IAAIlZ,QAAQ,CAAC;YACTmZ,QAAQ,EAAE;YACN;YACA,IAAIlZ,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CACN,IAAI9Y,QAAQ,CAAC;kBACTG,IAAI,EAAE0X,aAAa;kBACnBkB,cAAc,EAAE;oBACZb,KAAK,EAAE,EAAE;oBACTc,MAAM,EAAE;kBACZ;gBACJ,CAAC,CAAC,CACL;gBACDC,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHhB,KAAK,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEjP,IAAI,EAAErJ,SAAS,CAACuY;cAAW;YAClD,CAAC,CAAC;YACF;YACA,IAAIxY,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CACN,IAAItZ,SAAS,CAAC;gBACVsZ,QAAQ,EAAE,CACN,IAAIrZ,OAAO,CAAC;kBACRwF,IAAI,EAAE,sBAAsB;kBAC5BkU,IAAI,EAAE,IAAI;kBACVhB,IAAI,EAAE,EAAE;kBACRiB,IAAI,EAAE;gBACV,CAAC,CAAC,CACL;gBACDH,SAAS,EAAEnZ,aAAa,CAACoZ,MAAM;gBAC/BG,OAAO,EAAE;kBAAEC,KAAK,EAAE;gBAAI,CAAC;gBACvBC,aAAa,EAAE;cACnB,CAAC,CAAC,EACF,IAAI/Z,SAAS,CAAC;gBACVsZ,QAAQ,EAAE,CACN,IAAIrZ,OAAO,CAAC;kBACRwF,IAAI,EAAE,6BAA6B;kBACnCkU,IAAI,EAAE,IAAI;kBACVhB,IAAI,EAAE,EAAE;kBACRiB,IAAI,EAAE;gBACV,CAAC,CAAC,CACL;gBACDH,SAAS,EAAEnZ,aAAa,CAACoZ,MAAM;gBAC/BG,OAAO,EAAE;kBAAEC,KAAK,EAAE;gBAAI,CAAC;gBACvBC,aAAa,EAAE;cACnB,CAAC,CAAC,CACL;cACDrB,KAAK,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEjP,IAAI,EAAErJ,SAAS,CAACuY;cAAW;YAClD,CAAC,CAAC;YACF;YACA,IAAIxY,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CACN,IAAI9Y,QAAQ,CAAC;kBACTG,IAAI,EAAE2X,cAAc;kBACpBiB,cAAc,EAAE;oBACZb,KAAK,EAAE,EAAE;oBACTc,MAAM,EAAE;kBACZ;gBACJ,CAAC,CAAC,CACL;gBACDC,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHhB,KAAK,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEjP,IAAI,EAAErJ,SAAS,CAACuY;cAAW;YAClD,CAAC,CAAC;UAEV,CAAC,CAAC;QAEV,CAAC,CAAC;QAEFJ,gBAAgB,CAACzT,IAAI,CAAC0T,WAAW,CAAC;MACtC,OAAO;QACH;QACAD,gBAAgB,CAACzT,IAAI,CACjB,IAAI/E,SAAS,CAAC;UACVsZ,QAAQ,EAAE,CACN,IAAIrZ,OAAO,CAAC;YACRwF,IAAI,EAAE,sBAAsB;YAC5BkU,IAAI,EAAE,IAAI;YACVhB,IAAI,EAAE,EAAE;YACRiB,IAAI,EAAE;UACV,CAAC,CAAC,CACL;UACDH,SAAS,EAAEnZ,aAAa,CAACoZ,MAAM;UAC/BG,OAAO,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAC;UACvBC,aAAa,EAAE;QACnB,CAAC,CAAC,EACF,IAAI/Z,SAAS,CAAC;UACVsZ,QAAQ,EAAE,CACN,IAAIrZ,OAAO,CAAC;YACRwF,IAAI,EAAE,6BAA6B;YACnCkU,IAAI,EAAE,IAAI;YACVhB,IAAI,EAAE,EAAE;YACRiB,IAAI,EAAE;UACV,CAAC,CAAC,CACL;UACDH,SAAS,EAAEnZ,aAAa,CAACoZ,MAAM;UAC/BG,OAAO,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAC;UACvBC,aAAa,EAAE;QACnB,CAAC,CACL,CAAC;MACL;;MAEA;MACAvB,gBAAgB,CAACzT,IAAI,CACjB,IAAI/E,SAAS,CAAC;QACVsZ,QAAQ,EAAE,CACN,IAAIrZ,OAAO,CAAC;UACRwF,IAAI,EAAEzB,QAAQ,CAACnC,KAAK;UACpB8X,IAAI,EAAE,IAAI;UACVhB,IAAI,EAAE,EAAE;UACRqB,SAAS,EAAE,CAAC,CAAC;UACbJ,IAAI,EAAE;QACV,CAAC,CAAC,CACL;QACDH,SAAS,EAAEnZ,aAAa,CAACoZ,MAAM;QAC/BG,OAAO,EAAE;UAAEI,MAAM,EAAE,GAAG;UAAEH,KAAK,EAAE;QAAI,CAAC;QACpCC,aAAa,EAAE;MACnB,CAAC,CACL,CAAC;;MAED;MACA,MAAMG,YAAW,GAAI,IAAIha,KAAK,CAAC;QAC3BwY,KAAK,EAAE;UACHC,IAAI,EAAE,GAAG;UACTjP,IAAI,EAAErJ,SAAS,CAACuY;QACpB,CAAC;QACDC,OAAO,EAAE;UACLC,GAAG,EAAE;YAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;YAAExB,IAAI,EAAE,CAAC;YAAEyB,KAAK,EAAE;UAAS,CAAC;UAC5DpB,MAAM,EAAE;YAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;YAAExB,IAAI,EAAE,CAAC;YAAEyB,KAAK,EAAE;UAAS,CAAC;UAC/DnB,IAAI,EAAE;YAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;YAAExB,IAAI,EAAE,CAAC;YAAEyB,KAAK,EAAE;UAAS,CAAC;UAC7DlB,KAAK,EAAE;YAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;YAAExB,IAAI,EAAE,CAAC;YAAEyB,KAAK,EAAE;UAAS,CAAC;UAC9DjB,gBAAgB,EAAE;YAAE7I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;YAAExB,IAAI,EAAE,CAAC;YAAEyB,KAAK,EAAE;UAAS,CAAC;UACzEhB,cAAc,EAAE;YAAE9I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;YAAExB,IAAI,EAAE,CAAC;YAAEyB,KAAK,EAAE;UAAS;QAC1E,CAAC;QACDf,IAAI,EAAE;QACF;QACA,IAAI,CAACgB,cAAc,CAAC,OAAO,EAAE,iCAAiCrW,QAAQ,CAACyI,WAAW,MAAMzI,QAAQ,CAACgH,UAAU,EAAE,CAAC,EAC9G,IAAI,CAACqP,cAAc,CAAC,YAAY,EAAErW,QAAQ,CAACnC,KAAK,CAAC,EACjD,IAAImC,QAAQ,CAACoH,aAAY,GAAI,CAAC,IAAI,CAACiP,cAAc,CAAC,aAAa,EAAErW,QAAQ,CAACoH,aAAa,CAAC,IAAI,EAAE,CAAC,EAC/F,IAAIpH,QAAQ,CAACqH,cAAa,IAAKrH,QAAQ,CAACqH,cAAc,CAAC1F,MAAK,GAAI,IAC5D,CAAC,IAAI,CAAC0U,cAAc,CAAC,cAAc,EAAErW,QAAQ,CAACqH,cAAc,CAAChD,GAAG,CAACiS,CAAA,IAAK,KAAKA,CAAC,CAAC7U,IAAI,EAAE,CAAC,CAACwE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,EAC3G,IAAIjG,QAAQ,CAACsH,aAAY,IAAKtH,QAAQ,CAACsH,aAAa,CAAC3F,MAAK,GAAI,IAC1D,CAAC,IAAI,CAAC0U,cAAc,CAAC,iBAAiB,EAAErW,QAAQ,CAACsH,aAAa,CAACjD,GAAG,CAACiS,CAAA,IAAKA,CAAC,CAAC7U,IAAI,CAAC,CAACwE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,EACtG,IAAIjG,QAAQ,CAACuH,cAAa,GAAI,CAAC,IAAI,CAAC8O,cAAc,CAAC,gBAAgB,EAAE,GAAGrW,QAAQ,CAACuH,cAAc,MAAM,CAAC,IAAI,EAAE,CAAC,EAC7G,IAAI,CAAC8O,cAAc,CAAC,cAAc,EAAE,IAAI,CAAC/C,UAAU,CAACtT,QAAQ,CAACkH,aAAa,CAAC,CAAC,EAC5E,IAAIlH,QAAQ,CAACyH,aAAY,GAAI,CAAC,IAAI,CAAC4O,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC7C,UAAU,CAACxT,QAAQ,CAACyH,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC,EAC/G,IAAIzH,QAAQ,CAACwH,iBAAgB,GAAI,CAAC,IAAI,CAAC6O,cAAc,CAAC,aAAa,EAAErW,QAAQ,CAACwH,iBAAiB,CAAC,IAAI,EAAE,CAAC,EACvG,IAAIxH,QAAQ,CAAC0H,iBAAgB,GAAI,CAAC,IAAI,CAAC2O,cAAc,CAAC,YAAY,EAAErW,QAAQ,CAAC0H,iBAAiB,CAAC,IAAI,EAAE,CAAC,EACtG,IAAI1H,QAAQ,CAAC2H,eAAc,GAAI,CAAC,IAAI,CAAC0O,cAAc,CAAC,gBAAgB,EAAE,IAAI,CAAC3C,cAAc,CAAC1T,QAAQ,CAAC2H,eAAe,CAAC,CAAC,IAAI,EAAE,CAAC,EAC3H,IAAI3H,QAAQ,CAAC8H,eAAc,IAAK9H,QAAQ,CAAC8H,eAAe,CAACnG,MAAK,GAAI,IAC9D,CAAC,IAAI,CAAC0U,cAAc,CAAC,oBAAoB,EAAErW,QAAQ,CAAC8H,eAAe,CAACzD,GAAG,CAACkS,CAAA,IAAKA,CAAC,CAACxY,WAAW,CAAC,CAACkI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,EAClH,IAAI,CAACoQ,cAAc,CAAC,aAAa,EAAErW,QAAQ,CAACO,KAAK,CAAC,EAClD,IAAI,CAAC8V,cAAc,CAAC,UAAU,EAAErW,QAAQ,CAACyI,WAAW,CAAC,EACrD,IAAI,CAAC4N,cAAc,CAAC,eAAe,EAAErW,QAAQ,CAACgI,gBAAgB,CAAC,EAC/D,IAAI,CAACqO,cAAc,CAAC,eAAe,EAAE,IAAI,CAAC/C,UAAU,CAACtT,QAAQ,CAAC0I,UAAU,CAAC,CAAC;MAElF,CAAC,CAAC;MAEF6L,QAAQ,CAACxT,IAAI,CAAC,GAAGyT,gBAAgB,EAAE0B,YAAY,CAAC;;MAEhD;MACA,IAAIlW,QAAQ,CAAC4H,cAAa,IAAK5H,QAAQ,CAAC4H,cAAc,CAACjG,MAAK,GAAI,CAAC,EAAE;QAC/D4S,QAAQ,CAACxT,IAAI,CACT,IAAI/E,SAAS,CAAC;UACVsZ,QAAQ,EAAE,CACN,IAAIrZ,OAAO,CAAC;YACRwF,IAAI,EAAE,cAAc;YACpBkU,IAAI,EAAE,IAAI;YACVhB,IAAI,EAAE,EAAE;YACRiB,IAAI,EAAE;UACV,CAAC,CAAC,CACL;UACDC,OAAO,EAAE;YAAEI,MAAM,EAAE,GAAG;YAAEH,KAAK,EAAE;UAAI,CAAC;UACpCC,aAAa,EAAE,IAAI;UACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;QAC7B,CAAC,CACL,CAAC;QAED,MAAMc,WAAU,GAAI,IAAIta,KAAK,CAAC;UAC1BwY,KAAK,EAAE;YACHC,IAAI,EAAE,GAAG;YACTjP,IAAI,EAAErJ,SAAS,CAACuY;UACpB,CAAC;UACDC,OAAO,EAAE;YACLC,GAAG,EAAE;cAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS,CAAC;YAC5DpB,MAAM,EAAE;cAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS,CAAC;YAC/DnB,IAAI,EAAE;cAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS,CAAC;YAC7DlB,KAAK,EAAE;cAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS,CAAC;YAC9DjB,gBAAgB,EAAE;cAAE7I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS,CAAC;YACzEhB,cAAc,EAAE;cAAE9I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS;UAC1E,CAAC;UACDf,IAAI,EAAE;UACF;UACA,IAAIlZ,QAAQ,CAAC;YACTmZ,QAAQ,EAAE,CACN,IAAIlZ,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAE,QAAQ;kBACdkU,IAAI,EAAE,IAAI;kBACVC,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHhB,KAAK,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEjP,IAAI,EAAErJ,SAAS,CAACuY;cAAW,CAAC;cAC/C6B,OAAO,EAAE;gBACLC,IAAI,EAAE,QAAQ,CAAE;cACpB,CAAC;cACD7B,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBACL7B,GAAG,EAAE,GAAG;gBACRE,MAAM,EAAE,GAAG;gBACXC,IAAI,EAAE,GAAG;gBACTC,KAAK,EAAE;cACX;YACJ,CAAC,CAAC,EACF,IAAI9Y,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAE,OAAO;kBACbkU,IAAI,EAAE,IAAI;kBACVC,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHhB,KAAK,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEjP,IAAI,EAAErJ,SAAS,CAACuY;cAAW,CAAC;cAC/C6B,OAAO,EAAE;gBAAEC,IAAI,EAAE;cAAS,CAAC;cAC3B7B,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBAAE7B,GAAG,EAAE,GAAG;gBAAEE,MAAM,EAAE,GAAG;gBAAEC,IAAI,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI;YAC5D,CAAC,CAAC,EACF,IAAI9Y,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAE,OAAO;kBACbkU,IAAI,EAAE,IAAI;kBACVC,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHhB,KAAK,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEjP,IAAI,EAAErJ,SAAS,CAACuY;cAAW,CAAC;cAC/C6B,OAAO,EAAE;gBAAEC,IAAI,EAAE;cAAS,CAAC;cAC3B7B,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBAAE7B,GAAG,EAAE,GAAG;gBAAEE,MAAM,EAAE,GAAG;gBAAEC,IAAI,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI;YAC5D,CAAC,CAAC,EACF,IAAI9Y,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAE,OAAO;kBACbkU,IAAI,EAAE,IAAI;kBACVC,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHhB,KAAK,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEjP,IAAI,EAAErJ,SAAS,CAACuY;cAAW,CAAC;cAC/C6B,OAAO,EAAE;gBAAEC,IAAI,EAAE;cAAS,CAAC;cAC3B7B,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBAAE7B,GAAG,EAAE,GAAG;gBAAEE,MAAM,EAAE,GAAG;gBAAEC,IAAI,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI;YAC5D,CAAC,CAAC,EACF,IAAI9Y,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAE,SAAS;kBACfkU,IAAI,EAAE,IAAI;kBACVC,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHhB,KAAK,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEjP,IAAI,EAAErJ,SAAS,CAACuY;cAAW,CAAC;cAC/C6B,OAAO,EAAE;gBAAEC,IAAI,EAAE;cAAS,CAAC;cAC3B7B,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBAAE7B,GAAG,EAAE,GAAG;gBAAEE,MAAM,EAAE,GAAG;gBAAEC,IAAI,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI;YAC5D,CAAC,CAAC;UAEV,CAAC,CAAC;UACF;UACA,GAAGlV,QAAQ,CAAC4H,cAAc,CAACvD,GAAG,CAAC,CAACoB,MAAM,EAAEtC,KAAK,KAAK,IAAIhH,QAAQ,CAAC;YAC3DmZ,QAAQ,EAAE,CACN,IAAIlZ,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAEgE,MAAM,CAAC/I,IAAG,IAAK,EAAE;kBACvBkZ,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHe,OAAO,EAAE;gBACLC,IAAI,EAAEvT,KAAI,GAAI,MAAM,IAAI,QAAO,GAAI,QAAQ,CAAE;cACjD,CAAC;cACD0R,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBAAE7B,GAAG,EAAE,EAAE;gBAAEE,MAAM,EAAE,EAAE;gBAAEC,IAAI,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI;YAC1D,CAAC,CAAC,EACF,IAAI9Y,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAEgE,MAAM,CAACC,IAAG,IAAK,EAAE;kBACvBkQ,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHe,OAAO,EAAE;gBACLC,IAAI,EAAEvT,KAAI,GAAI,MAAM,IAAI,QAAO,GAAI;cACvC,CAAC;cACD0R,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBAAE7B,GAAG,EAAE,EAAE;gBAAEE,MAAM,EAAE,EAAE;gBAAEC,IAAI,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI;YAC1D,CAAC,CAAC,EACF,IAAI9Y,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAEgE,MAAM,CAACE,MAAM,EAAEiR,QAAQ,CAAC,KAAK,GAAG;kBACtChB,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHe,OAAO,EAAE;gBACLC,IAAI,EAAEvT,KAAI,GAAI,MAAM,IAAI,QAAO,GAAI;cACvC,CAAC;cACD0R,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBAAE7B,GAAG,EAAE,EAAE;gBAAEE,MAAM,EAAE,EAAE;gBAAEC,IAAI,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI;YAC1D,CAAC,CAAC,EACF,IAAI9Y,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAE,IAAI,CAACiS,cAAc,CAACjO,MAAM,CAACG,KAAK,CAAC;kBACvCgQ,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHe,OAAO,EAAE;gBACLC,IAAI,EAAEvT,KAAI,GAAI,MAAM,IAAI,QAAO,GAAI;cACvC,CAAC;cACD0R,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBAAE7B,GAAG,EAAE,EAAE;gBAAEE,MAAM,EAAE,EAAE;gBAAEC,IAAI,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI;YAC1D,CAAC,CAAC,EACF,IAAI9Y,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAE,IAAI,CAACiS,cAAc,CAACjO,MAAM,CAACI,WAAW,CAAC;kBAC7C+P,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHe,OAAO,EAAE;gBACLC,IAAI,EAAEvT,KAAI,GAAI,MAAM,IAAI,QAAO,GAAI;cACvC,CAAC;cACD0R,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBAAE7B,GAAG,EAAE,EAAE;gBAAEE,MAAM,EAAE,EAAE;gBAAEC,IAAI,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI;YAC1D,CAAC,CAAC;UAEV,CAAC,CAAC,CAAC;UACH;UACA,IAAIlV,QAAQ,CAAC6H,YAAW,GAAI,CAAC,IAAI1L,QAAQ,CAAC;YACtCmZ,QAAQ,EAAE,CACN,IAAIlZ,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAE,UAAU;kBAChBkU,IAAI,EAAE,IAAI;kBACVC,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHmB,UAAU,EAAE,CAAC;cACbJ,OAAO,EAAE;gBACLC,IAAI,EAAE,QAAQ,CAAE;cACpB,CAAC;cACD7B,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBAAE7B,GAAG,EAAE,GAAG;gBAAEE,MAAM,EAAE,GAAG;gBAAEC,IAAI,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI;YAC5D,CAAC,CAAC,EACF,IAAI9Y,SAAS,CAAC;cACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;gBACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;kBACnBwF,IAAI,EAAE,IAAI,CAACiS,cAAc,CAAC1T,QAAQ,CAAC6H,YAAY,CAAC;kBAChD8N,IAAI,EAAE,IAAI;kBACVC,IAAI,EAAE,OAAO;kBACbjB,IAAI,EAAE,EAAE;kBACRyB,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACHL,aAAa,EAAE,IAAI;gBACnBN,SAAS,EAAEnZ,aAAa,CAACoZ;cAC7B,CAAC,CAAC,CAAC;cACHe,OAAO,EAAE;gBACLC,IAAI,EAAE,QAAQ,CAAE;cACpB,CAAC;cACD7B,OAAO,EAAE;gBACLC,GAAG,EAAE;kBAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC5DpB,MAAM,EAAE;kBAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC/DnB,IAAI,EAAE;kBAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS,CAAC;gBAC7DlB,KAAK,EAAE;kBAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;kBAAExB,IAAI,EAAE,CAAC;kBAAEyB,KAAK,EAAE;gBAAS;cACjE,CAAC;cACDO,OAAO,EAAE;gBAAE7B,GAAG,EAAE,GAAG;gBAAEE,MAAM,EAAE,GAAG;gBAAEC,IAAI,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI;YAC5D,CAAC,CAAC;UAEV,CAAC,CAAC,IAAI,EAAE,CAAC;QAEjB,CAAC,CAAC;QAEFX,QAAQ,CAACxT,IAAI,CAACyV,WAAW,CAAC;MAC9B;;MAEA;MACA,MAAMM,GAAE,GAAI,IAAIhb,QAAQ,CAAC;QACrByY,QAAQ,EAAE,CAAC;UACPe,QAAQ,EAAEf,QAAQ;UAClBwC,UAAU,EAAE;YACRC,IAAI,EAAE;cACFC,MAAM,EAAE;gBACJnC,GAAG,EAAE,IAAI;gBAAK;gBACdI,KAAK,EAAE,IAAI;gBAAG;gBACdF,MAAM,EAAE,IAAI;gBAAE;gBACdC,IAAI,EAAE,IAAI,CAAI;cAClB;YACJ;UACJ;QACJ,CAAC,CAAC;QACFiC,MAAM,EAAE;UACJjD,OAAO,EAAE;YACLnR,QAAQ,EAAE;cACNqU,GAAG,EAAE;gBACDvB,IAAI,EAAE,OAAO;gBACbjB,IAAI,EAAE;cACV,CAAC;cACDyC,SAAS,EAAE;gBACPvB,OAAO,EAAE;kBACLwB,IAAI,EAAE;gBACV;cACJ;YACJ;UACJ;QACJ;MACJ,CAAC,CAAC;;MAEF;MACA,IAAI;QACA,MAAMvG,IAAG,GAAI,MAAM/U,MAAM,CAACub,MAAM,CAACR,GAAG,CAAC;QACrC,MAAMjE,QAAO,GAAI,QAAQ7S,QAAQ,CAACnC,KAAK,CAAC0Z,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,IAAI,IAAIrO,IAAI,CAAC,CAAC,CAACsI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;QACjHhV,MAAM,CAACqU,IAAI,EAAE+B,QAAQ,CAAC;QAEtB,IAAI,CAAC5P,MAAM,CAACgF,OAAO,CAAC,sBAAsB,CAAC;MAC/C,EAAE,OAAOxF,KAAK,EAAE;QACZE,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAACQ,MAAM,CAACR,KAAK,CAAC,2BAA2B,CAAC;MAClD;IACJ,CAAC;IACD4T,cAAcA,CAACmB,KAAK,EAAE5T,KAAK,EAAE;MACzB,OAAO,IAAIzH,QAAQ,CAAC;QAChBmZ,QAAQ,EAAE;QACN;QACA,IAAIlZ,SAAS,CAAC;UACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;YACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;cACnBwF,IAAI,EAAEmC,KAAI,IAAK,UAAU;cACzBgS,IAAI,EAAE,OAAO;cACbjB,IAAI,EAAE,EAAE;cACRyB,KAAK,EAAE;YACX,CAAC,CAAC,CAAC;YACHL,aAAa,EAAE,IAAI;YACnBN,SAAS,EAAEnZ,aAAa,CAACmb;UAC7B,CAAC,CAAC,CAAC;UACH/C,KAAK,EAAE;YAAEC,IAAI,EAAE,EAAE;YAAEjP,IAAI,EAAErJ,SAAS,CAACuY;UAAW,CAAC;UAC/C6B,OAAO,EAAE;YACLC,IAAI,EAAE,QAAQ,CAAE;UACpB,CAAC;UACD7B,OAAO,EAAE;YACLC,GAAG,EAAE;cAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS,CAAC;YAC5DpB,MAAM,EAAE;cAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS,CAAC;YAC/DnB,IAAI,EAAE;cAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS,CAAC;YAC7DlB,KAAK,EAAE;cAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS;UACjE,CAAC;UACDO,OAAO,EAAE;YACL7B,GAAG,EAAE,GAAG;YACRE,MAAM,EAAE,GAAG;YACXC,IAAI,EAAE,GAAG;YACTC,KAAK,EAAE;UACX;QACJ,CAAC,CAAC;QACF;QACA,IAAI9Y,SAAS,CAAC;UACVkZ,QAAQ,EAAE,CAAC,IAAItZ,SAAS,CAAC;YACrBsZ,QAAQ,EAAE,CAAC,IAAIrZ,OAAO,CAAC;cACnBwF,IAAI,EAAE+V,KAAK;cACX7B,IAAI,EAAE,IAAI;cACVC,IAAI,EAAE,OAAO;cACbjB,IAAI,EAAE,EAAE;cACRyB,KAAK,EAAE;YACX,CAAC,CAAC,CAAC;YACHL,aAAa,EAAE,IAAI;YACnBN,SAAS,EAAEnZ,aAAa,CAACmb;UAC7B,CAAC,CAAC,CAAC;UACH/C,KAAK,EAAE;YAAEC,IAAI,EAAE,EAAE;YAAEjP,IAAI,EAAErJ,SAAS,CAACuY;UAAW,CAAC;UAC/C6B,OAAO,EAAE;YACLC,IAAI,EAAE,QAAQ,CAAE;UACpB,CAAC;UACD7B,OAAO,EAAE;YACLC,GAAG,EAAE;cAAExI,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS,CAAC;YAC5DpB,MAAM,EAAE;cAAE1I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS,CAAC;YAC/DnB,IAAI,EAAE;cAAE3I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS,CAAC;YAC7DlB,KAAK,EAAE;cAAE5I,KAAK,EAAE/P,WAAW,CAAC4Z,MAAM;cAAExB,IAAI,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAS;UACjE,CAAC;UACDO,OAAO,EAAE;YACL7B,GAAG,EAAE,GAAG;YACRE,MAAM,EAAE,GAAG;YACXC,IAAI,EAAE,GAAG;YACTC,KAAK,EAAE;UACX;QACJ,CAAC,CAAC;MAEV,CAAC,CAAC;IACN,CAAC;IACD,MAAMwC,aAAaA,CAAA,EAAG;MAClB,IAAI,CAAC,IAAI,CAACvX,gBAAgB,EAAE;MAE5B,IAAI;QACA;QACA,MAAMwX,UAAS,GAAI7U,QAAQ,CAACQ,aAAa,CAAC,sBAAsB,CAAC;QACjE,MAAMsU,WAAU,GAAI9U,QAAQ,CAACQ,aAAa,CAAC,uBAAuB,CAAC;QAEnE,MAAMuU,UAAS,GAAIF,UAAS,GAAIA,UAAU,CAACG,GAAE,GAAI,EAAE;QACnD,MAAMC,WAAU,GAAIH,WAAU,GAAIA,WAAW,CAACE,GAAE,GAAI,EAAE;;QAEtD;QACA,MAAME,WAAU,GAAIC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;;QAE7C;QACA,MAAMC,YAAW,GAAI,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACjY,gBAAgB,EAAE0X,UAAU,EAAEE,WAAW,CAAC;;QAE9F;QACAC,WAAW,CAAClV,QAAQ,CAACuV,KAAK,CAACF,YAAY,CAAC;QACxCH,WAAW,CAAClV,QAAQ,CAACwV,KAAK,CAAC,CAAC;;QAE5B;QACAN,WAAW,CAACO,MAAK,GAAI,MAAM;UACvB;UACAC,UAAU,CAAC,MAAM;YACbR,WAAW,CAACS,KAAK,CAAC,CAAC;YACnBT,WAAW,CAACM,KAAK,CAAC,CAAC;UACvB,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC;MACL,EAAE,OAAO7V,KAAK,EAAE;QACZE,OAAO,CAACF,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACQ,MAAM,CAACR,KAAK,CAAC,uBAAuB,CAAC;MAC9C;IACJ,CAAC;IACD2V,oBAAoBA,CAACpY,QAAQ,EAAE6X,UAAS,GAAI,EAAE,EAAEE,WAAU,GAAI,EAAE,EAAE;MAC9D,OAAO;AACnB;AACA;AACA;;;6BAG6B/X,QAAQ,CAACnC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2M3C;AACA;;;;kBAIkBga,UAAS,GAAI,aAAaA,UAAU,gEAA+D,GAAI,EAAE;;;;;;;;kBAQzGE,WAAU,GAAI,aAAaA,WAAW,uEAAsE,GAAI,EAAE;;;;;kBAKlH/X,QAAQ,CAACnC,KAAK;;;;;;;sEAOsCmC,QAAQ,CAACyI,WAAW,MAAMzI,QAAQ,CAACgH,UAAU;;;;;wCAK3EhH,QAAQ,CAACnC,KAAK;;;UAG5CmC,QAAQ,CAACoH,aAAY,GAAI;;;wCAGKpH,QAAQ,CAACoH,aAAa;;SAEtD,GAAI,EAAE;;UAEJpH,QAAQ,CAACqH,cAAa,IAAKrH,QAAQ,CAACqH,cAAc,CAAC1F,MAAK,GAAI,IAAI;;;;;sBAKpD3B,QAAQ,CAACqH,cAAc,CAAChD,GAAG,CAACE,IAAG,IAAK,OAAOA,IAAI,CAAC9C,IAAI,OAAO,CAAC,CAACwE,IAAI,CAAC,EAAE,CAAC;;;;SAInF,GAAI,EAAE;;UAEJjG,QAAQ,CAACsH,aAAY,IAAKtH,QAAQ,CAACsH,aAAa,CAAC3F,MAAK,GAAI,IAAI;;;;;sBAKlD3B,QAAQ,CAACsH,aAAa,CAACjD,GAAG,CAACI,KAAI,IAAK,OAAOA,KAAK,CAAChD,IAAI,OAAO,CAAC,CAACwE,IAAI,CAAC,EAAE,CAAC;;;;SAIpF,GAAI,EAAE;;UAEJjG,QAAQ,CAACuH,cAAa,GAAI;;;wCAGIvH,QAAQ,CAACuH,cAAc;;SAEvD,GAAI,EAAE;;;;wCAI0B,IAAI,CAAC+L,UAAU,CAACtT,QAAQ,CAACkH,aAAa,CAAC;;;UAGrElH,QAAQ,CAACyH,aAAY,GAAI;;;wCAGK,IAAI,CAAC+L,UAAU,CAACxT,QAAQ,CAACyH,aAAa,CAAC;;SAEvE,GAAI,EAAE;;UAEJzH,QAAQ,CAACwH,iBAAgB,GAAI;;;wCAGCxH,QAAQ,CAACwH,iBAAiB;;SAE1D,GAAI,EAAE;;UAEJxH,QAAQ,CAAC0H,iBAAgB,GAAI;;;wCAGC1H,QAAQ,CAAC0H,iBAAiB;;SAE1D,GAAI,EAAE;;UAEJ1H,QAAQ,CAAC2H,eAAc,GAAI;;;wCAGG,IAAI,CAAC+L,cAAc,CAAC1T,QAAQ,CAAC2H,eAAe,CAAC;;SAE7E,GAAI,EAAE;;UAEJ3H,QAAQ,CAAC8H,eAAc,IAAK9H,QAAQ,CAAC8H,eAAe,CAACnG,MAAK,GAAI,IAAI;;;;;sBAKtD3B,QAAQ,CAAC8H,eAAe,CAACzD,GAAG,CAAC0B,KAAI,IAAK,OAAOA,KAAK,CAAChI,WAAW,OAAO,CAAC,CAACkI,IAAI,CAAC,EAAE,CAAC;;;;SAI7F,GAAI,EAAE;;;;;4CAK8B,IAAI,CAACmE,cAAc,CAACpK,QAAQ,CAACO,KAAK,CAAC;sBACzDP,QAAQ,CAACO,KAAK;;;;;;;wCAOIP,QAAQ,CAACyI,WAAW;;;;;wCAKpBzI,QAAQ,CAACgI,gBAAgB;;;;;wCAKzB,IAAI,CAACsL,UAAU,CAACtT,QAAQ,CAAC0I,UAAU,CAAC;;;;MAItE1I,QAAQ,CAAC4H,cAAa,IAAK5H,QAAQ,CAAC4H,cAAc,CAACjG,MAAK,GAAI,IAAI;;;;;;;;;;;;;;kBAcpD3B,QAAQ,CAAC4H,cAAc,CAACvD,GAAG,CAAC,CAACoB,MAAM,EAAEtC,KAAK,KAAK;sBAC3CA,KAAI,GAAI,MAAM,IAAI,oCAAmC,GAAI,EAAE;0BACvDsC,MAAM,CAAC/I,IAAG,IAAK,EAAE;0BACjB+I,MAAM,CAACC,IAAG,IAAK,EAAE;0BACjBD,MAAM,CAACE,MAAK,IAAK,EAAE;0BACnB,IAAI,CAAC+N,cAAc,CAACjO,MAAM,CAACG,KAAK,CAAC;0BACjC,IAAI,CAAC8N,cAAc,CAACjO,MAAM,CAACI,WAAW,CAAC;;iBAEhD,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC;;cAEbjG,QAAQ,CAAC6H,YAAW,GAAI;;;;kCAIJ,IAAI,CAAC6L,cAAc,CAAC1T,QAAQ,CAAC6H,YAAY,CAAC;;;aAGhE,GAAI,EAAE;;;KAGd,GAAI,EAAE;AACV;AACA;aACa;IACL;EACJ,CAAC;EACD6Q,OAAOA,CAAA,EAAG;IACN;IACA,MAAMC,OAAM,GAAI/X,YAAY,CAACO,OAAO,CAAC,WAAW,CAAC;IACjD,IAAIwX,OAAO,EAAE;MACT,IAAI,CAACha,IAAG,GAAI2D,IAAI,CAACsW,KAAK,CAACD,OAAO,CAAC;IACnC;IACA;IACA7V,QAAQ,CAAC+H,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACoF,kBAAkB,CAAC;IAC3D;IACA,IAAI,CAACM,mBAAmB,CAAC,CAAC;EAC9B,CAAC;EACDsI,aAAaA,CAAA,EAAG;IACZ/V,QAAQ,CAACgW,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC7I,kBAAkB,CAAC;IAC9D;IACAnN,QAAQ,CAAC6D,IAAI,CAAC2F,KAAK,CAAC8G,QAAO,GAAI,MAAM;EACzC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}