import router from '@/router'

class ApiService {
  constructor() {
    this.baseURL = '/api/v1/ndyt-activities'
  }

  async request(endpoint, options = {}) {
    const token = localStorage.getItem('ndyt_token')
    const teamPin = localStorage.getItem('ndyt_team_pin')
    
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    }

    // Add authorization header if token exists
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    // Add team pin header if it exists
    if (teamPin && (config.method === 'POST' || config.method === 'PUT' || config.method === 'DELETE')) {
      config.headers['x-team-pin'] = teamPin
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, config)

    // Handle token expiration globally
    if (response.status === 401 || response.status === 403) {
      this.handleTokenExpiration()
      throw new Error('Session expired')
    }

    return response
  }

  handleTokenExpiration() {
    // Clear all authentication data
    localStorage.removeItem('ndyt_token')
    localStorage.removeItem('ndyt_user')
    localStorage.removeItem('ndyt_team_pin')

    // Show toast notification
    try {
      // Try to use Vue's toast if available
      const app = document.getElementById('app')?.__vue_app__
      if (app && app.config.globalProperties.$toast) {
        app.config.globalProperties.$toast.error('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى')
      } else {
        // Fallback to console log
        console.warn('Session expired. Redirecting to login.')
      }
    } catch (error) {
      console.warn('Session expired. Redirecting to login.')
    }

    // Redirect to login page
    router.push('/login')
  }

  // Convenience methods
  async get(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: 'GET' })
  }

  async post(endpoint, data, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async put(endpoint, data, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  async delete(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: 'DELETE' })
  }

  // File upload method
  async uploadFile(file, options = {}) {
    const token = localStorage.getItem('ndyt_token')
    const formData = new FormData()
    formData.append('file', file)

    const config = {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData,
      ...options
    }

    const response = await fetch(`${this.baseURL}/upload-file`, config)

    if (response.status === 401 || response.status === 403) {
      this.handleTokenExpiration()
      throw new Error('Session expired')
    }

    return response
  }
}

export default new ApiService()
