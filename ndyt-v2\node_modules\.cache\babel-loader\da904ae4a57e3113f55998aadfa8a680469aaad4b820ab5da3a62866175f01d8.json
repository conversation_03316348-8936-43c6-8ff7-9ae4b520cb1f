{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass, vModelSelect as _vModelSelect, withDirectives as _withDirectives, withModifiers as _withModifiers } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-container\"\n};\nconst _hoisted_2 = {\n  class: \"admin-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-top\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"error-container\"\n};\nconst _hoisted_6 = {\n  class: \"error-message\"\n};\nconst _hoisted_7 = {\n  key: 2,\n  class: \"users-table-container\"\n};\nconst _hoisted_8 = {\n  class: \"table-header\"\n};\nconst _hoisted_9 = {\n  class: \"table-wrapper\"\n};\nconst _hoisted_10 = {\n  class: \"users-table\"\n};\nconst _hoisted_11 = {\n  class: \"username\"\n};\nconst _hoisted_12 = [\"onClick\"];\nconst _hoisted_13 = {\n  class: \"modal-header\"\n};\nconst _hoisted_14 = {\n  class: \"form-group\"\n};\nconst _hoisted_15 = {\n  class: \"form-group\"\n};\nconst _hoisted_16 = [\"value\"];\nconst _hoisted_17 = {\n  class: \"form-actions\"\n};\nconst _hoisted_18 = [\"disabled\"];\nconst _hoisted_19 = {\n  key: 0\n};\nconst _hoisted_20 = {\n  key: 1\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.goBack && $options.goBack(...args)),\n    class: \"back-btn\"\n  }, [...(_cache[9] || (_cache[9] = [_createElementVNode(\"svg\", {\n    width: \"20\",\n    height: \"20\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"2\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M19 12H5M12 19l-7-7 7-7\"\n  })], -1 /* CACHED */), _createTextVNode(\" العودة للصفحة الرئيسية \", -1 /* CACHED */)]))])]), _cache[10] || (_cache[10] = _createElementVNode(\"h1\", null, \"إدارة المستخدمين\", -1 /* CACHED */)), _cache[11] || (_cache[11] = _createElementVNode(\"p\", {\n    class: \"admin-subtitle\"\n  }, \"إدارة رتب المستخدمين ومحافظاتهم\", -1 /* CACHED */))]), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [...(_cache[12] || (_cache[12] = [_createElementVNode(\"div\", {\n    class: \"loading-spinner\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"p\", null, \"جاري تحميل المستخدمين...\", -1 /* CACHED */)]))])) : $data.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createElementVNode(\"p\", _hoisted_6, _toDisplayString($data.error), 1 /* TEXT */), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.fetchUsers && $options.fetchUsers(...args)),\n    class: \"retry-btn\"\n  }, \"إعادة المحاولة\")])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"h2\", null, \"قائمة المستخدمين (\" + _toDisplayString($data.users.length) + \")\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"table\", _hoisted_10, [_cache[14] || (_cache[14] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"المعرف\"), _createElementVNode(\"th\", null, \"اسم المستخدم\"), _createElementVNode(\"th\", null, \"الاسم الكامل\"), _createElementVNode(\"th\", null, \"الرتبة\"), _createElementVNode(\"th\", null, \"المحافظة\"), _createElementVNode(\"th\", null, \"تاريخ التسجيل\"), _createElementVNode(\"th\", null, \"آخر دخول\"), _createElementVNode(\"th\", null, \"الإجراءات\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.users, user => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: user.id,\n      class: \"user-row\"\n    }, [_createElementVNode(\"td\", null, _toDisplayString(user.id), 1 /* TEXT */), _createElementVNode(\"td\", _hoisted_11, _toDisplayString(user.username), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(user.full_name || 'غير محدد'), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"rank-badge\", `rank-${user.rank}`])\n    }, _toDisplayString($options.getRankLabel(user.rank)), 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString(user.governorate || 'غير محدد'), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString($options.formatDate(user.created_at)), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(user.last_login ? $options.formatDate(user.last_login) : 'لم يدخل بعد'), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"button\", {\n      onClick: $event => $options.editUser(user),\n      class: \"edit-btn\"\n    }, [...(_cache[13] || (_cache[13] = [_createElementVNode(\"svg\", {\n      width: \"16\",\n      height: \"16\",\n      viewBox: \"0 0 24 24\",\n      fill: \"currentColor\"\n    }, [_createElementVNode(\"path\", {\n      d: \"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"\n    })], -1 /* CACHED */), _createTextVNode(\" تعديل \", -1 /* CACHED */)]))], 8 /* PROPS */, _hoisted_12)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])), _createCommentVNode(\" Edit User Modal \"), $data.editingUser ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 3,\n    class: \"modal-overlay\",\n    onClick: _cache[8] || (_cache[8] = (...args) => $options.closeModal && $options.closeModal(...args))\n  }, [_createElementVNode(\"div\", {\n    class: \"modal-content\",\n    onClick: _cache[7] || (_cache[7] = _withModifiers(() => {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"h3\", null, \"تعديل المستخدم: \" + _toDisplayString($data.editingUser.username), 1 /* TEXT */), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.closeModal && $options.closeModal(...args)),\n    class: \"close-btn\"\n  }, \"×\")]), _createElementVNode(\"form\", {\n    onSubmit: _cache[6] || (_cache[6] = _withModifiers((...args) => $options.updateUser && $options.updateUser(...args), [\"prevent\"])),\n    class: \"edit-form\"\n  }, [_createElementVNode(\"div\", _hoisted_14, [_cache[16] || (_cache[16] = _createElementVNode(\"label\", {\n    for: \"rank\"\n  }, \"الرتبة:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.editForm.rank = $event),\n    id: \"rank\",\n    required: \"\",\n    class: \"form-select\"\n  }, [...(_cache[15] || (_cache[15] = [_createElementVNode(\"option\", {\n    value: \"member\"\n  }, \"عضو\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"leader\"\n  }, \"منسق\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"admin\"\n  }, \"مدير\", -1 /* CACHED */)]))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.editForm.rank]])]), _createElementVNode(\"div\", _hoisted_15, [_cache[17] || (_cache[17] = _createElementVNode(\"label\", {\n    for: \"governorate\"\n  }, \"المحافظة:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.editForm.governorate = $event),\n    id: \"governorate\",\n    required: \"\",\n    class: \"form-select\"\n  }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.governorates, gov => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: gov,\n      value: gov\n    }, _toDisplayString(gov), 9 /* TEXT, PROPS */, _hoisted_16);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.editForm.governorate]])]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"button\", {\n    type: \"button\",\n    onClick: _cache[5] || (_cache[5] = (...args) => $options.closeModal && $options.closeModal(...args)),\n    class: \"cancel-btn\"\n  }, \"إلغاء\"), _createElementVNode(\"button\", {\n    type: \"submit\",\n    disabled: $data.updating,\n    class: \"save-btn\"\n  }, [$data.updating ? (_openBlock(), _createElementBlock(\"span\", _hoisted_19, \"جاري الحفظ...\")) : (_openBlock(), _createElementBlock(\"span\", _hoisted_20, \"حفظ التغييرات\"))], 8 /* PROPS */, _hoisted_18)])], 32 /* NEED_HYDRATION */)])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "goBack", "width", "height", "viewBox", "fill", "stroke", "d", "$data", "loading", "_hoisted_4", "error", "_hoisted_5", "_hoisted_6", "_toDisplayString", "fetchUsers", "_hoisted_7", "_hoisted_8", "users", "length", "_hoisted_9", "_hoisted_10", "_Fragment", "_renderList", "user", "key", "id", "_hoisted_11", "username", "full_name", "_normalizeClass", "rank", "getRankLabel", "governorate", "formatDate", "created_at", "last_login", "$event", "editUser", "_createCommentVNode", "editingUser", "closeModal", "_withModifiers", "_hoisted_13", "onSubmit", "updateUser", "_hoisted_14", "for", "editForm", "required", "value", "_hoisted_15", "governorates", "gov", "_hoisted_16", "_hoisted_17", "type", "disabled", "updating", "_hoisted_19", "_hoisted_20"], "sources": ["F:\\My Apps\\iqtp\\iqtp_backend\\ndyt-v2\\src\\views\\AdminView.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-container\">\n    <div class=\"admin-header\">\n      <div class=\"header-top\">\n        <button @click=\"goBack\" class=\"back-btn\">\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <path d=\"M19 12H5M12 19l-7-7 7-7\"/>\n          </svg>\n          العودة للصفحة الرئيسية\n        </button>\n      </div>\n      <h1>إدارة المستخدمين</h1>\n      <p class=\"admin-subtitle\">إدارة رتب المستخدمين ومحافظاتهم</p>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <div class=\"loading-spinner\"></div>\n      <p>جاري تحميل المستخدمين...</p>\n    </div>\n\n    <div v-else-if=\"error\" class=\"error-container\">\n      <p class=\"error-message\">{{ error }}</p>\n      <button @click=\"fetchUsers\" class=\"retry-btn\">إعادة المحاولة</button>\n    </div>\n\n    <div v-else class=\"users-table-container\">\n      <div class=\"table-header\">\n        <h2>قائمة المستخدمين ({{ users.length }})</h2>\n      </div>\n      \n      <div class=\"table-wrapper\">\n        <table class=\"users-table\">\n          <thead>\n            <tr>\n              <th>المعرف</th>\n              <th>اسم المستخدم</th>\n              <th>الاسم الكامل</th>\n              <th>الرتبة</th>\n              <th>المحافظة</th>\n              <th>تاريخ التسجيل</th>\n              <th>آخر دخول</th>\n              <th>الإجراءات</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr v-for=\"user in users\" :key=\"user.id\" class=\"user-row\">\n              <td>{{ user.id }}</td>\n              <td class=\"username\">{{ user.username }}</td>\n              <td>{{ user.full_name || 'غير محدد' }}</td>\n              <td>\n                <span class=\"rank-badge\" :class=\"`rank-${user.rank}`\">\n                  {{ getRankLabel(user.rank) }}\n                </span>\n              </td>\n              <td>{{ user.governorate || 'غير محدد' }}</td>\n              <td>{{ formatDate(user.created_at) }}</td>\n              <td>{{ user.last_login ? formatDate(user.last_login) : 'لم يدخل بعد' }}</td>\n              <td>\n                <button @click=\"editUser(user)\" class=\"edit-btn\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                    <path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/>\n                  </svg>\n                  تعديل\n                </button>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Edit User Modal -->\n    <div v-if=\"editingUser\" class=\"modal-overlay\" @click=\"closeModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h3>تعديل المستخدم: {{ editingUser.username }}</h3>\n          <button @click=\"closeModal\" class=\"close-btn\">&times;</button>\n        </div>\n        \n        <form @submit.prevent=\"updateUser\" class=\"edit-form\">\n          <div class=\"form-group\">\n            <label for=\"rank\">الرتبة:</label>\n            <select v-model=\"editForm.rank\" id=\"rank\" required class=\"form-select\">\n              <option value=\"member\">عضو</option>\n              <option value=\"leader\">منسق</option>\n              <option value=\"admin\">مدير</option>\n            </select>\n          </div>\n          \n          <div class=\"form-group\">\n            <label for=\"governorate\">المحافظة:</label>\n            <select v-model=\"editForm.governorate\" id=\"governorate\" required class=\"form-select\">\n              <option v-for=\"gov in governorates\" :key=\"gov\" :value=\"gov\">{{ gov }}</option>\n            </select>\n          </div>\n          \n          <div class=\"form-actions\">\n            <button type=\"button\" @click=\"closeModal\" class=\"cancel-btn\">إلغاء</button>\n            <button type=\"submit\" :disabled=\"updating\" class=\"save-btn\">\n              <span v-if=\"updating\">جاري الحفظ...</span>\n              <span v-else>حفظ التغييرات</span>\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'AdminView',\n  data() {\n    return {\n      users: [],\n      loading: true,\n      error: null,\n      editingUser: null,\n      editForm: {\n        rank: '',\n        governorate: ''\n      },\n      updating: false,\n      governorates: [\n        'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى',\n        'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين',\n        'ميسان', 'دهوك', 'السليمانية', 'حلبجة'\n      ]\n    }\n  },\n  mounted() {\n    this.checkAdminAccess();\n  },\n  methods: {\n    async checkAdminAccess() {\n      const token = localStorage.getItem('ndyt_token');\n      if (!token) {\n        this.$router.push('/login');\n        return;\n      }\n\n      try {\n        const response = await this.$api.get('/auth/me');\n\n        if (response.ok) {\n          const user = await response.json();\n          if (user.rank !== 'admin') {\n            this.$swal.fire({\n              title: 'غير مصرح',\n              text: 'ليس لديك صلاحية للوصول إلى هذه الصفحة',\n              icon: 'error'\n            });\n            this.$router.push('/');\n            return;\n          }\n          this.fetchUsers();\n        } else {\n          this.$router.push('/login');\n        }\n      } catch (error) {\n        if (error.message === 'Session expired') {\n          // Token expiration is already handled by the API service\n          return;\n        }\n        console.error('Error checking admin access:', error);\n        this.$router.push('/login');\n      }\n    },\n    \n    goBack() {\n      this.$router.push('/');\n    },\n    \n    async fetchUsers() {\n      this.loading = true;\n      this.error = null;\n      \n      try {\n        const token = localStorage.getItem('ndyt_token');\n        const response = await fetch('/api/v1/ndyt-activities/admin/users', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        \n        if (response.ok) {\n          this.users = await response.json();\n        } else {\n          let errorData = {};\n          try {\n            errorData = await response.json();\n          } catch (jsonError) {\n            console.error('Error parsing response JSON:', jsonError);\n          }\n          this.error = errorData.error || 'فشل في تحميل المستخدمين';\n        }\n      } catch (error) {\n        console.error('Error fetching users:', error);\n        this.error = 'حدث خطأ أثناء تحميل المستخدمين';\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    editUser(user) {\n      this.editingUser = user;\n      this.editForm = {\n        rank: user.rank,\n        governorate: user.governorate || 'بغداد'\n      };\n    },\n    \n    closeModal() {\n      this.editingUser = null;\n      this.editForm = { rank: '', governorate: '' };\n    },\n    \n    async updateUser() {\n      this.updating = true;\n      \n      try {\n        const token = localStorage.getItem('ndyt_token');\n        const response = await fetch(`/api/v1/ndyt-activities/admin/users/${this.editingUser.id}`, {\n          method: 'PUT',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(this.editForm)\n        });\n        \n        if (response.ok) {\n          const updatedUser = await response.json();\n          const index = this.users.findIndex(u => u.id === updatedUser.id);\n          if (index !== -1) {\n            this.users[index] = { ...this.users[index], ...updatedUser };\n          }\n          this.closeModal();\n          this.$toast.success('تم تحديث المستخدم بنجاح!');\n        } else {\n          let errorData = {};\n          try {\n            errorData = await response.json();\n          } catch (jsonError) {\n            console.error('Error parsing response JSON:', jsonError);\n          }\n          this.$toast.error(`فشل في تحديث المستخدم: ${errorData.error || 'خطأ غير معروف'}`);\n        }\n      } catch (error) {\n        console.error('Error updating user:', error);\n        this.$toast.error('حدث خطأ أثناء تحديث المستخدم');\n      } finally {\n        this.updating = false;\n      }\n    },\n    \n    getRankLabel(rank) {\n      const labels = {\n        'member': 'عضو',\n        'leader': 'منسق',\n        'admin': 'مدير'\n      };\n      return labels[rank] || rank;\n    },\n    \n    formatDate(dateString) {\n      if (!dateString) return 'غير محدد';\n      return new Date(dateString).toLocaleDateString('ar-EG', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 20px;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  direction: rtl;\n}\n\n.admin-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 15px;\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\n  position: relative;\n}\n\n.header-top {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n}\n\n.back-btn {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  padding: 10px 16px;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n}\n\n.back-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  border-color: rgba(255, 255, 255, 0.5);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n}\n\n.back-btn svg {\n  transition: transform 0.3s ease;\n}\n\n.back-btn:hover svg {\n  transform: translateX(-2px);\n}\n\n.admin-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.5rem;\n  font-weight: 700;\n}\n\n.admin-subtitle {\n  margin: 0;\n  font-size: 1.1rem;\n  opacity: 0.9;\n}\n\n.loading-container, .error-container {\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #667eea;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.error-message {\n  color: #e74c3c;\n  font-size: 1.1rem;\n  margin-bottom: 20px;\n}\n\n.retry-btn {\n  background: #667eea;\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n}\n\n.retry-btn:hover {\n  background: #5a6fd8;\n  transform: translateY(-2px);\n}\n\n.users-table-container {\n  background: #1a1a2e;\n  border-radius: 15px;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);\n  overflow: hidden;\n  border: 1px solid #16213e;\n}\n\n.table-header {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n  color: white;\n  padding: 20px;\n  text-align: center;\n}\n\n.table-header h2 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.table-wrapper {\n  overflow-x: auto;\n}\n\n.users-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 0.95rem;\n}\n\n.users-table th {\n  background: #0f3460;\n  padding: 15px 12px;\n  text-align: right;\n  font-weight: 600;\n  color: #e2e8f0;\n  border-bottom: 2px solid #16213e;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n.users-table td {\n  padding: 15px 12px;\n  border-bottom: 1px solid #16213e;\n  vertical-align: middle;\n  color: #cbd5e0;\n  background: #1a1a2e;\n}\n\n.user-row {\n  transition: all 0.3s ease;\n}\n\n.user-row:hover {\n  background: linear-gradient(135deg, #667eea20, #764ba220);\n  transform: scale(1.01);\n}\n\n.username {\n  font-weight: 600;\n  color: #4facfe;\n}\n\n.rank-badge {\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  font-weight: 600;\n  text-align: center;\n  display: inline-block;\n  min-width: 60px;\n}\n\n.rank-member {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.rank-leader {\n  background: #fff3e0;\n  color: #f57c00;\n}\n\n.rank-admin {\n  background: #ffebee;\n  color: #d32f2f;\n}\n\n.edit-btn {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  transition: all 0.3s ease;\n}\n\n.edit-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n}\n\n.modal-content {\n  background: #1a1a2e;\n  border-radius: 20px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\n  animation: modalSlideIn 0.3s ease;\n  border: 1px solid #16213e;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-50px) scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.modal-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-radius: 20px 20px 0 0;\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-size: 1.3rem;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 0;\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.edit-form {\n  padding: 30px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #e2e8f0;\n}\n\n.form-select {\n  width: 100%;\n  padding: 12px 16px;\n  border: 2px solid #16213e;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background: #0f3460;\n  color: #e2e8f0;\n}\n\n.form-select:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);\n}\n\n.form-actions {\n  display: flex;\n  gap: 15px;\n  justify-content: flex-end;\n  margin-top: 30px;\n}\n\n.cancel-btn {\n  background: #6c757d;\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 10px;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n}\n\n.cancel-btn:hover {\n  background: #5a6268;\n  transform: translateY(-2px);\n}\n\n.save-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 10px;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  min-width: 120px;\n}\n\n.save-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);\n}\n\n.save-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n\n@media (max-width: 768px) {\n  .admin-container {\n    padding: 10px;\n  }\n  \n  .admin-header h1 {\n    font-size: 2rem;\n  }\n  \n  .users-table {\n    font-size: 0.85rem;\n  }\n  \n  .users-table th,\n  .users-table td {\n    padding: 10px 8px;\n  }\n  \n  .modal-content {\n    width: 95%;\n    margin: 10px;\n  }\n  \n  .edit-form {\n    padding: 20px;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n  }\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;;EAYLA,KAAK,EAAC;;;;EAKHA,KAAK,EAAC;;;EACxBA,KAAK,EAAC;AAAe;;;EAIdA,KAAK,EAAC;;;EACXA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAe;;EACjBA,KAAK,EAAC;AAAa;;EAgBhBA,KAAK,EAAC;AAAU;;;EA2BrBA,KAAK,EAAC;AAAc;;EAMlBA,KAAK,EAAC;AAAY;;EASlBA,KAAK,EAAC;AAAY;;;EAOlBA,KAAK,EAAC;AAAc;;;;;;;;;uBA/FjCC,mBAAA,CAyGM,OAzGNC,UAyGM,GAxGJC,mBAAA,CAWM,OAXNC,UAWM,GAVJD,mBAAA,CAOM,OAPNE,UAOM,GANJF,mBAAA,CAKS;IALAG,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,MAAA,IAAAD,QAAA,CAAAC,MAAA,IAAAF,IAAA,CAAM;IAAER,KAAK,EAAC;qCAC5BG,mBAAA,CAEM;IAFDQ,KAAK,EAAC,IAAI;IAACC,MAAM,EAAC,IAAI;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC;MAC7FZ,mBAAA,CAAmC;IAA7Ba,CAAC,EAAC;EAAyB,G,qCAC7B,0BAER,mB,oCAEFb,mBAAA,CAAyB,YAArB,kBAAgB,qB,4BACpBA,mBAAA,CAA6D;IAA1DH,KAAK,EAAC;EAAgB,GAAC,iCAA+B,oB,GAGhDiB,KAAA,CAAAC,OAAO,I,cAAlBjB,mBAAA,CAGM,OAHNkB,UAGM,OAAAZ,MAAA,SAAAA,MAAA,QAFJJ,mBAAA,CAAmC;IAA9BH,KAAK,EAAC;EAAiB,2BAC5BG,mBAAA,CAA+B,WAA5B,0BAAwB,mB,QAGbc,KAAA,CAAAG,KAAK,I,cAArBnB,mBAAA,CAGM,OAHNoB,UAGM,GAFJlB,mBAAA,CAAwC,KAAxCmB,UAAwC,EAAAC,gBAAA,CAAZN,KAAA,CAAAG,KAAK,kBACjCjB,mBAAA,CAAqE;IAA5DG,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAe,UAAA,IAAAf,QAAA,CAAAe,UAAA,IAAAhB,IAAA,CAAU;IAAER,KAAK,EAAC;KAAY,gBAAc,E,oBAG9DC,mBAAA,CA4CM,OA5CNwB,UA4CM,GA3CJtB,mBAAA,CAEM,OAFNuB,UAEM,GADJvB,mBAAA,CAA8C,YAA1C,oBAAkB,GAAAoB,gBAAA,CAAGN,KAAA,CAAAU,KAAK,CAACC,MAAM,IAAG,GAAC,gB,GAG3CzB,mBAAA,CAsCM,OAtCN0B,UAsCM,GArCJ1B,mBAAA,CAoCQ,SApCR2B,WAoCQ,G,4BAnCN3B,mBAAA,CAWQ,gBAVNA,mBAAA,CASK,aARHA,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAAqB,YAAjB,cAAY,GAChBA,mBAAA,CAAqB,YAAjB,cAAY,GAChBA,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAAiB,YAAb,UAAQ,GACZA,mBAAA,CAAsB,YAAlB,eAAa,GACjBA,mBAAA,CAAiB,YAAb,UAAQ,GACZA,mBAAA,CAAkB,YAAd,WAAS,E,uBAGjBA,mBAAA,CAsBQ,iB,kBArBNF,mBAAA,CAoBK8B,SAAA,QAAAC,WAAA,CApBcf,KAAA,CAAAU,KAAK,EAAbM,IAAI;yBAAfhC,mBAAA,CAoBK;MApBsBiC,GAAG,EAAED,IAAI,CAACE,EAAE;MAAEnC,KAAK,EAAC;QAC7CG,mBAAA,CAAsB,YAAAoB,gBAAA,CAAfU,IAAI,CAACE,EAAE,kBACdhC,mBAAA,CAA6C,MAA7CiC,WAA6C,EAAAb,gBAAA,CAArBU,IAAI,CAACI,QAAQ,kBACrClC,mBAAA,CAA2C,YAAAoB,gBAAA,CAApCU,IAAI,CAACK,SAAS,gCACrBnC,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFDH,KAAK,EAAAuC,eAAA,EAAC,YAAY,UAAiBN,IAAI,CAACO,IAAI;wBAC7C/B,QAAA,CAAAgC,YAAY,CAACR,IAAI,CAACO,IAAI,yB,GAG7BrC,mBAAA,CAA6C,YAAAoB,gBAAA,CAAtCU,IAAI,CAACS,WAAW,gCACvBvC,mBAAA,CAA0C,YAAAoB,gBAAA,CAAnCd,QAAA,CAAAkC,UAAU,CAACV,IAAI,CAACW,UAAU,mBACjCzC,mBAAA,CAA4E,YAAAoB,gBAAA,CAArEU,IAAI,CAACY,UAAU,GAAGpC,QAAA,CAAAkC,UAAU,CAACV,IAAI,CAACY,UAAU,mCACnD1C,mBAAA,CAOK,aANHA,mBAAA,CAKS;MALAG,OAAK,EAAAwC,MAAA,IAAErC,QAAA,CAAAsC,QAAQ,CAACd,IAAI;MAAGjC,KAAK,EAAC;yCACpCG,mBAAA,CAEM;MAFDQ,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC;QACnDX,mBAAA,CAAiK;MAA3Ja,CAAC,EAAC;IAAuJ,G,qCAC3J,SAER,mB;2CAQZgC,mBAAA,qBAAwB,EACb/B,KAAA,CAAAgC,WAAW,I,cAAtBhD,mBAAA,CAiCM;;IAjCkBD,KAAK,EAAC,eAAe;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAyC,UAAA,IAAAzC,QAAA,CAAAyC,UAAA,IAAA1C,IAAA,CAAU;MAC9DL,mBAAA,CA+BM;IA/BDH,KAAK,EAAC,eAAe;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA4C,cAAA,CAAN,QAAW;MACpChD,mBAAA,CAGM,OAHNiD,WAGM,GAFJjD,mBAAA,CAAmD,YAA/C,kBAAgB,GAAAoB,gBAAA,CAAGN,KAAA,CAAAgC,WAAW,CAACZ,QAAQ,kBAC3ClC,mBAAA,CAA8D;IAArDG,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAyC,UAAA,IAAAzC,QAAA,CAAAyC,UAAA,IAAA1C,IAAA,CAAU;IAAER,KAAK,EAAC;KAAY,GAAO,E,GAGvDG,mBAAA,CAwBO;IAxBAkD,QAAM,EAAA9C,MAAA,QAAAA,MAAA,MAAA4C,cAAA,KAAA3C,IAAA,KAAUC,QAAA,CAAA6C,UAAA,IAAA7C,QAAA,CAAA6C,UAAA,IAAA9C,IAAA,CAAU;IAAER,KAAK,EAAC;MACvCG,mBAAA,CAOM,OAPNoD,WAOM,G,4BANJpD,mBAAA,CAAiC;IAA1BqD,GAAG,EAAC;EAAM,GAAC,SAAO,qB,gBACzBrD,mBAAA,CAIS;+DAJQc,KAAA,CAAAwC,QAAQ,CAACjB,IAAI,GAAAM,MAAA;IAAEX,EAAE,EAAC,MAAM;IAACuB,QAAQ,EAAR,EAAQ;IAAC1D,KAAK,EAAC;uCACvDG,mBAAA,CAAmC;IAA3BwD,KAAK,EAAC;EAAQ,GAAC,KAAG,oBAC1BxD,mBAAA,CAAoC;IAA5BwD,KAAK,EAAC;EAAQ,GAAC,MAAI,oBAC3BxD,mBAAA,CAAmC;IAA3BwD,KAAK,EAAC;EAAO,GAAC,MAAI,mB,6CAHX1C,KAAA,CAAAwC,QAAQ,CAACjB,IAAI,E,KAOhCrC,mBAAA,CAKM,OALNyD,WAKM,G,4BAJJzD,mBAAA,CAA0C;IAAnCqD,GAAG,EAAC;EAAa,GAAC,WAAS,qB,gBAClCrD,mBAAA,CAES;+DAFQc,KAAA,CAAAwC,QAAQ,CAACf,WAAW,GAAAI,MAAA;IAAEX,EAAE,EAAC,aAAa;IAACuB,QAAQ,EAAR,EAAQ;IAAC1D,KAAK,EAAC;yBACrEC,mBAAA,CAA8E8B,SAAA,QAAAC,WAAA,CAAxDf,KAAA,CAAA4C,YAAY,EAAnBC,GAAG;yBAAlB7D,mBAAA,CAA8E;MAAzCiC,GAAG,EAAE4B,GAAG;MAAGH,KAAK,EAAEG;wBAAQA,GAAG,wBAAAC,WAAA;2EADnD9C,KAAA,CAAAwC,QAAQ,CAACf,WAAW,E,KAKvCvC,mBAAA,CAMM,OANN6D,WAMM,GALJ7D,mBAAA,CAA2E;IAAnE8D,IAAI,EAAC,QAAQ;IAAE3D,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAyC,UAAA,IAAAzC,QAAA,CAAAyC,UAAA,IAAA1C,IAAA,CAAU;IAAER,KAAK,EAAC;KAAa,OAAK,GAClEG,mBAAA,CAGS;IAHD8D,IAAI,EAAC,QAAQ;IAAEC,QAAQ,EAAEjD,KAAA,CAAAkD,QAAQ;IAAEnE,KAAK,EAAC;MACnCiB,KAAA,CAAAkD,QAAQ,I,cAApBlE,mBAAA,CAA0C,QAAAmE,WAAA,EAApB,eAAa,M,cACnCnE,mBAAA,CAAiC,QAAAoE,WAAA,EAApB,eAAa,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}