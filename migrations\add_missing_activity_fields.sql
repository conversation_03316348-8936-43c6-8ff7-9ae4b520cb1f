-- Migration: Add missing activity fields to ndt_activities table
-- This adds all the form fields that are currently being collected but not stored
-- Run this SQL script to upgrade the activities table schema

BEGIN;

-- Add missing columns to ndt_activities table
ALTER TABLE public.ndt_activities 
ADD COLUMN IF NOT EXISTS activity_idea TEXT,
ADD COLUMN IF NOT EXISTS activity_goals JSONB,
ADD COLUMN IF NOT EXISTS target_groups JSONB,
ADD COLUMN IF NOT EXISTS audience_count INTEGER,
ADD COLUMN IF NOT EXISTS activity_location VARCHAR(255),
ADD COLUMN IF NOT EXISTS activity_time TIME,
ADD COLUMN IF NOT EXISTS activity_duration VARCHAR(100),
ADD COLUMN IF NOT EXISTS activity_budget DECIMAL(12,2),
ADD COLUMN IF NOT EXISTS budget_details JSONB,
ADD COLUMN IF NOT EXISTS total_budget DECIMAL(12,2),
ADD COLUMN IF NOT EXISTS activity_levels JSONB;

-- Add comments to describe the new columns
COMMENT ON COLUMN ndt_activities.activity_idea IS 'Detailed description of the activity idea';
COMMENT ON COLUMN ndt_activities.activity_goals IS 'JSON array of activity goals/objectives';
COMMENT ON COLUMN ndt_activities.target_groups IS 'JSON array of target audience groups';
COMMENT ON COLUMN ndt_activities.audience_count IS 'Number of people targeted by the activity';
COMMENT ON COLUMN ndt_activities.activity_location IS 'Location where the activity will take place';
COMMENT ON COLUMN ndt_activities.activity_time IS 'Time when the activity will start';
COMMENT ON COLUMN ndt_activities.activity_duration IS 'Duration of the activity (e.g., "2 hours", "3 days")';
COMMENT ON COLUMN ndt_activities.activity_budget IS 'Planned budget for the activity in Iraqi Dinars';
COMMENT ON COLUMN ndt_activities.budget_details IS 'JSON array of detailed budget breakdown items';
COMMENT ON COLUMN ndt_activities.total_budget IS 'Total calculated budget amount in Iraqi Dinars';
COMMENT ON COLUMN ndt_activities.activity_levels IS 'JSON array of implementation phases/levels';

-- Create indexes for better query performance on commonly searched fields
CREATE INDEX IF NOT EXISTS idx_ndt_activities_location ON public.ndt_activities(activity_location);
CREATE INDEX IF NOT EXISTS idx_ndt_activities_date_time ON public.ndt_activities(activity_date, activity_time);
CREATE INDEX IF NOT EXISTS idx_ndt_activities_budget ON public.ndt_activities(activity_budget);
CREATE INDEX IF NOT EXISTS idx_ndt_activities_audience_count ON public.ndt_activities(audience_count);

-- Create GIN indexes for JSONB columns to enable efficient JSON queries
CREATE INDEX IF NOT EXISTS idx_ndt_activities_goals_gin ON public.ndt_activities USING GIN(activity_goals);
CREATE INDEX IF NOT EXISTS idx_ndt_activities_target_groups_gin ON public.ndt_activities USING GIN(target_groups);
CREATE INDEX IF NOT EXISTS idx_ndt_activities_budget_details_gin ON public.ndt_activities USING GIN(budget_details);
CREATE INDEX IF NOT EXISTS idx_ndt_activities_levels_gin ON public.ndt_activities USING GIN(activity_levels);

-- Verify the new schema
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'ndt_activities' 
AND table_schema = 'public'
ORDER BY ordinal_position;

COMMIT;

-- Example of the expected JSON structure for the new JSONB columns:
/*
activity_goals: [
  {"text": "تطوير مهارات الشباب"},
  {"text": "زيادة الوعي التقني"}
]

target_groups: [
  {"text": "طلاب الجامعات"},
  {"text": "الخريجين الجدد"}
]

budget_details: [
  {
    "name": "أجهزة كمبيوتر",
    "type": "معدات",
    "amount": 10,
    "price": 500000,
    "budgetPrice": 5000000
  }
]

activity_levels: [
  {"description": "التخطيط والإعداد"},
  {"description": "التنفيذ"},
  {"description": "التقييم"}
]
*/
