{"name": "@vue/cli-plugin-eslint", "version": "5.0.9", "description": "eslint plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-eslint"}, "keywords": ["vue", "cli", "eslint"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-eslint#readme", "publishConfig": {"access": "public"}, "dependencies": {"@vue/cli-shared-utils": "^5.0.9", "eslint-webpack-plugin": "^3.1.0", "globby": "^11.0.2", "webpack": "^5.54.0", "yorkie": "^2.0.0"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0 || ^5.0.0-0", "eslint": ">=7.5.0"}, "gitHead": "7eb93c169c7520935252e2473387f923ef80d856"}