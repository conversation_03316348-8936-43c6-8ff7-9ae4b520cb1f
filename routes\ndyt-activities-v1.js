const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const pool = require('../configcred/configcred');
const path = require('path');
const fs = require('fs');
const multer = require('multer');
const crypto = require('crypto');

// JWT secret
const JWT_SECRET = process.env.JWT_SECRET || 'change-me';

// File upload configuration
const uploadDir = path.join(__dirname, '../uploads/activity-files');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// File validation function
function isValidFileType(filename, mimetype) {
  const allowedTypes = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'];
  const ext = path.extname(filename).toLowerCase();
  return allowedTypes.includes(ext);
}

// File size limit (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Utility: issue JWT
function generateToken(username) {
  const payload = { username };
  return jwt.sign(payload, JWT_SECRET, { algorithm: 'HS256', expiresIn: '8h' });
}

// Middleware: authenticate Bearer token
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  if (!token) return res.status(401).json({ error: 'Authentication required' });
  jwt.verify(token, JWT_SECRET, (err, decoded) => {
    if (err) return res.status(403).json({ error: 'Invalid or expired token' });
    req.user = decoded;
    next();
  });
}

// Middleware: require team pin (رمز الفريق الرقمي) and verify against the current user pin_code
async function requireTeamPin(req, res, next) {
  try {
    // Accept both English and Arabic keys
    const providedPin = req.body?.team_pin || req.body?.['التوقيع_الرقمي'] || req.body?.['التوقيع الرقمي'] || req.headers['x-team-pin'];
    if (!providedPin) return res.status(400).json({ error: 'Team PIN (رمز الفريق الرقمي) is required' });

    // Load current user pin from DB using username in token
    const { rows } = await pool.query('SELECT id, pin_code FROM ndt_users WHERE username = $1', [req.user.username]);
    if (rows.length === 0) return res.status(404).json({ error: 'User not found' });
    const user = rows[0];

    if (user.pin_code !== providedPin) {
      return res.status(401).json({ error: 'Invalid Team PIN (رمز الفريق الرقمي)' });
    }

    req.currentUserId = user.id;
    next();
  } catch (e) {
    console.error('requireTeamPin error:', e);
    res.status(500).json({ error: 'Server error' });
  }
}

// Allowed activity states (front-end provided Arabic labels)
const ALLOWED_STATES = [
  'منفذ بصرف',
  'منفذ بدون صرف',
  'مقبول',
  'مرفوض',
  'يحتاج تعديل',
  'صرف و لم ينفذ',
  'مقبول دون صرف',
  'مرسل'
];

// --- Auth Endpoints ---
// Register: username, full_name, password, team_pin (التوقيع_الرقمي)
router.post('/auth/register', async (req, res) => {
  try {
    const username = (req.body?.username || '').trim();
    const fullName = (req.body?.full_name || req.body?.fullname || '').trim();
    const password = req.body?.password || '';
    const teamPin = req.body?.team_pin || req.body?.['التوقيع_الرقمي'] || req.body?.['التوقيع الرقمي'];
    const governorate = (req.body?.governorate || '').trim();

    if (!username || !fullName || !password || !teamPin || !governorate) {
      return res.status(400).json({ error: 'username, full_name, password, team_pin (التوقيع الرقمي), and governorate are required' });
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const insertSql = `
      INSERT INTO ndt_users (username, full_name, password_hash, pin_code, rank, governorate)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, username, full_name, rank, governorate
    `;

    const { rows } = await pool.query(insertSql, [username, fullName, hashedPassword, teamPin, 'member', governorate]);

    const token = generateToken(username);
    res.status(201).json({ token, user: rows[0] });
  } catch (error) {
    console.error('Register error:', error);
    if (error.code === '23505') {
      return res.status(400).json({ error: 'Username already exists' });
    }
    res.status(500).json({ error: 'Failed to register' });
  }
});

// Login: username, password, team_pin (التوقيع_الرقمي)
router.post('/auth/login', async (req, res) => {
  try {
    const username = (req.body?.username || '').trim();
    const password = req.body?.password || '';
    const teamPin = req.body?.team_pin || req.body?.['التوقيع_الرقمي'] || req.body?.['التوقيع الرقمي'];

    if (!username || !password || !teamPin) {
      return res.status(400).json({ error: 'username, password, and team_pin (التوقيع الرقمي) are required' });
    }

    const { rows } = await pool.query('SELECT * FROM ndt_users WHERE username = $1', [username]);
    if (!rows.length) return res.status(401).json({ error: 'Invalid credentials' });

    const user = rows[0];
    const valid = await bcrypt.compare(password, user.password_hash);
    if (!valid || user.pin_code !== teamPin) return res.status(401).json({ error: 'Invalid credentials' });

    const token = generateToken(username);
    res.json({ token, user: { id: user.id, username: user.username, full_name: user.full_name, rank: user.rank, avatar_url: user.avatar_url } });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Failed to login' });
  }
});

// Current user
router.get('/auth/me', authenticateToken, async (req, res) => {
  try {
    const { rows } = await pool.query('SELECT id, username, full_name, rank, avatar_url FROM ndt_users WHERE username = $1', [req.user.username]);
    if (!rows.length) return res.status(404).json({ error: 'User not found' });
    res.json(rows[0]);
  } catch (e) {
    console.error('auth/me error:', e);
    res.status(500).json({ error: 'Server error' });
  }
});

// Update user profile
router.put('/user/update-profile', authenticateToken, async (req, res) => {
  try {
    const { full_name, team_pin, current_password, new_password } = req.body;
    
    if (!full_name || !team_pin) {
      return res.status(400).json({ error: 'full_name and team_pin are required' });
    }
    
    // Get current user
    const userRes = await pool.query('SELECT id, password_hash FROM ndt_users WHERE username = $1', [req.user.username]);
    if (!userRes.rows.length) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    const user = userRes.rows[0];
    let updateFields = ['full_name = $2', 'pin_code = $3'];
    let updateValues = [user.id, full_name.trim(), team_pin.trim()];
    
    // Handle password update if provided
    if (new_password) {
      if (!current_password) {
        return res.status(400).json({ error: 'current_password is required when updating password' });
      }
      
      // Verify current password
      const passwordMatch = await bcrypt.compare(current_password, user.password_hash);
      if (!passwordMatch) {
        return res.status(400).json({ error: 'Current password is incorrect' });
      }
      
      // Hash new password
      const hashedNewPassword = await bcrypt.hash(new_password, 10);
      updateFields.push('password_hash = $4');
      updateValues.push(hashedNewPassword);
    }
    
    // Update user profile
    const updateSql = `UPDATE ndt_users SET ${updateFields.join(', ')} WHERE id = $1 RETURNING id, username, full_name, rank, avatar_url`;
    const { rows } = await pool.query(updateSql, updateValues);
    
    res.json({ 
      message: 'Profile updated successfully', 
      user: rows[0] 
    });
    
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// --- Activities Submissions ---
// Create submission with activities
router.post('/submissions', authenticateToken, requireTeamPin, async (req, res) => {
  const client = await pool.connect();
  try {
    const { coordinator_name, activities } = req.body || {};

    if (!coordinator_name || !Array.isArray(activities)) {
      return res.status(400).json({ error: 'coordinator_name and activities[] are required' });
    }

    // Get user's governorate from their profile
    const userRes = await client.query('SELECT governorate FROM ndt_users WHERE id = $1', [req.currentUserId]);
    if (userRes.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    const userGovernorate = userRes.rows[0].governorate;

    await client.query('BEGIN');

    const subSql = `
      INSERT INTO ndt_activity_submissions (user_id, governorate, coordinator_name)
      VALUES ($1, $2, $3) RETURNING id, user_id, governorate, coordinator_name, created_at
    `;
    const subRes = await client.query(subSql, [req.currentUserId, userGovernorate, coordinator_name]);
    const submission = subRes.rows[0];

    const createdActivities = [];
    for (const item of activities) {
      const owner = (item.owner || item.owner_name || '').trim();
      const title = (item.title || '').trim();
      const shortDesc = (item.short_description || item.description || '').trim();
      const date = item.activity_date || item.date || null;
      const state = item.state || null;
      const fileId = item.file_id || null;

      // Extract new fields
      const activityIdea = (item.activity_idea || item.idea || '').trim();
      const activityGoals = item.activity_goals || item.goals || null;
      const targetGroups = item.target_groups || item.audience || null;
      const audienceCount = item.audience_count || item.audienceCount || null;
      const activityLocation = (item.activity_location || item.location || '').trim();
      const activityTime = item.activity_time || item.time || null;
      const activityDuration = (item.activity_duration || item.duration || '').trim();
      const activityBudget = item.activity_budget || item.budget || null;
      const budgetDetails = item.budget_details || item.budgetDetails || null;
      const totalBudget = item.total_budget || item.totalBudget || null;
      const activityLevels = item.activity_levels || item.levels || null;

      // Debug logging to check received data
      console.log(`Activity ${activities.indexOf(item) + 1} received data:`, {
        owner, title, date, state,
        budgetDetails: budgetDetails,
        budgetDetailsType: typeof budgetDetails,
        budgetDetailsLength: Array.isArray(budgetDetails) ? budgetDetails.length : 'not array',
        raw_budget_details: item.budget_details,
        raw_budgetDetails: item.budgetDetails,
        raw_item: JSON.stringify(item, null, 2)
      });

      if (!owner || !title || !date || !state) {
        await client.query('ROLLBACK');
        return res.status(400).json({
          error: 'Each activity requires owner, title, date, and state',
          missing_fields: {
            owner: !owner,
            title: !title,
            date: !date,
            state: !state
          },
          received_data: { owner, title, date, state }
        });
      }

      // Optionally validate state
      // if (!ALLOWED_STATES.includes(state)) { ... }

      const actSql = `
        INSERT INTO ndt_activities (
          submission_id, owner_name, title, short_description, activity_date, state,
          activity_idea, activity_goals, target_groups, audience_count,
          activity_location, activity_time, activity_duration, activity_budget,
          budget_details, total_budget, activity_levels
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        RETURNING *
      `;
      const actRes = await client.query(actSql, [
        submission.id, owner, title, shortDesc, date, state,
        activityIdea || null,
        activityGoals ? JSON.stringify(activityGoals) : null,
        targetGroups ? JSON.stringify(targetGroups) : null,
        audienceCount,
        activityLocation || null,
        activityTime,
        activityDuration || null,
        activityBudget,
        budgetDetails ? JSON.stringify(budgetDetails) : null,
        totalBudget,
        activityLevels ? JSON.stringify(activityLevels) : null
      ]);
      const activity = actRes.rows[0];

      let savedFiles = [];
      
      // Handle file_id from uploaded file
      if (fileId) {
        try {
          // Update the uploaded file to link it to this activity
          const updateFileSql = `
            UPDATE ndt_activity_files 
            SET activity_id = $1 
            WHERE id = $2 AND activity_id IS NULL
            RETURNING *
          `;
          const fileRes = await client.query(updateFileSql, [activity.id, fileId]);
          if (fileRes.rows.length > 0) {
            savedFiles.push(fileRes.rows[0]);
          }
        } catch (fileError) {
          console.error('Error linking file to activity:', fileError);
          // Continue without failing the entire submission
        }
      }
      
      // Optional simple files metadata (file_url/file_name) - for backward compatibility
      const files = Array.isArray(item.files) ? item.files : [];
      for (const f of files) {
        const fileName = f.file_name || f.name || null;
        const fileUrl = f.file_url || f.url || null;
        const mimeType = f.mime_type || null;
        const sizeBytes = f.size_bytes || f.size || null;
        if (!fileUrl || !fileName) continue;
        const fileSql = `
          INSERT INTO ndt_activity_files (activity_id, file_name, file_url, mime_type, size_bytes)
          VALUES ($1, $2, $3, $4, $5)
          RETURNING *
        `;
        const fileRes = await client.query(fileSql, [activity.id, fileName, fileUrl, mimeType, sizeBytes]);
        savedFiles.push(fileRes.rows[0]);
      }

      createdActivities.push({ ...activity, files: savedFiles });
    }

    await client.query('COMMIT');

    res.status(201).json({ submission, activities: createdActivities });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Create submission error:', error);
    console.error('Error stack:', error.stack);
    console.error('Request body:', JSON.stringify(req.body, null, 2));
    res.status(500).json({ error: 'Failed to create submission', details: error.message });
  } finally {
    client.release();
  }
});

// List current user's submissions with activities and files
router.get('/submissions', authenticateToken, async (req, res) => {
  try {
    const userRes = await pool.query('SELECT id FROM ndt_users WHERE username = $1', [req.user.username]);
    if (!userRes.rows.length) return res.status(404).json({ error: 'User not found' });
    const userId = userRes.rows[0].id;

    const subsRes = await pool.query(
      'SELECT id, governorate, coordinator_name, created_at FROM ndt_activity_submissions WHERE user_id = $1 ORDER BY created_at DESC',
      [userId]
    );

    const submissions = [];
    for (const sub of subsRes.rows) {
      const actsRes = await pool.query(
        'SELECT * FROM ndt_activities WHERE submission_id = $1 ORDER BY created_at ASC',
        [sub.id]
      );
      const activities = [];
      for (const a of actsRes.rows) {
        const filesRes = await pool.query(
          'SELECT id, file_name, file_url, mime_type, size_bytes, uploaded_at FROM ndt_activity_files WHERE activity_id = $1 ORDER BY uploaded_at ASC',
          [a.id]
        );
        activities.push({ ...a, files: filesRes.rows });
      }
      submissions.push({ ...sub, activities });
    }

    res.json(submissions);
  } catch (error) {
    console.error('List submissions error:', error);
    res.status(500).json({ error: 'Failed to fetch submissions' });
  }
});

// List activities based on user role
// - admin: see all activities
// - leader: see all activities from their governorate
// - member: see only their submitted activities
router.get('/activities', authenticateToken, async (req, res) => {
  try {
    // Get current user info
    const userRes = await pool.query(
      'SELECT id, rank, governorate, avatar_url FROM ndt_users WHERE username = $1',
      [req.user.username]
    );
    
    if (!userRes.rows.length) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    const user = userRes.rows[0];
    let userRank = user.rank;
    
    // Check for intended rank stored in avatar_url field (for leaders)
    if (user.avatar_url && user.avatar_url.startsWith('rank:')) {
      userRank = user.avatar_url.replace('rank:', '');
    }
    
    let activitiesQuery;
    let queryParams;
    
    if (userRank === 'admin') {
      // Admin sees all activities
      activitiesQuery = `
        SELECT
          a.id, a.submission_id, a.owner_name, a.title, a.short_description,
          a.activity_date, a.state, a.created_at,
          a.activity_idea, a.activity_goals, a.target_groups, a.audience_count,
          a.activity_location, a.activity_time, a.activity_duration, a.activity_budget,
          a.budget_details, a.total_budget, a.activity_levels,
          s.governorate, s.coordinator_name, s.user_id,
          u.username, u.full_name as submitter_name
        FROM ndt_activities a
        JOIN ndt_activity_submissions s ON a.submission_id = s.id
        JOIN ndt_users u ON s.user_id = u.id
        ORDER BY a.created_at DESC
      `;
      queryParams = [];
    } else if (userRank === 'leader') {
      // Leader sees all activities from their governorate
      activitiesQuery = `
        SELECT
          a.id, a.submission_id, a.owner_name, a.title, a.short_description,
          a.activity_date, a.state, a.created_at,
          a.activity_idea, a.activity_goals, a.target_groups, a.audience_count,
          a.activity_location, a.activity_time, a.activity_duration, a.activity_budget,
          a.budget_details, a.total_budget, a.activity_levels,
          s.governorate, s.coordinator_name, s.user_id,
          u.username, u.full_name as submitter_name
        FROM ndt_activities a
        JOIN ndt_activity_submissions s ON a.submission_id = s.id
        JOIN ndt_users u ON s.user_id = u.id
        WHERE s.governorate = $1
        ORDER BY a.created_at DESC
      `;
      queryParams = [user.governorate];
    } else {
      // Member sees only their own activities
      activitiesQuery = `
        SELECT
          a.id, a.submission_id, a.owner_name, a.title, a.short_description,
          a.activity_date, a.state, a.created_at,
          a.activity_idea, a.activity_goals, a.target_groups, a.audience_count,
          a.activity_location, a.activity_time, a.activity_duration, a.activity_budget,
          a.budget_details, a.total_budget, a.activity_levels,
          s.governorate, s.coordinator_name, s.user_id,
          u.username, u.full_name as submitter_name
        FROM ndt_activities a
        JOIN ndt_activity_submissions s ON a.submission_id = s.id
        JOIN ndt_users u ON s.user_id = u.id
        WHERE s.user_id = $1
        ORDER BY a.created_at DESC
      `;
      queryParams = [user.id];
    }
    
    const activitiesRes = await pool.query(activitiesQuery, queryParams);
    
    // Get files for each activity
    const activities = [];
    for (const activity of activitiesRes.rows) {
      const filesRes = await pool.query(
        'SELECT id, file_name, file_url, mime_type, size_bytes, uploaded_at FROM ndt_activity_files WHERE activity_id = $1 ORDER BY uploaded_at ASC',
        [activity.id]
      );
      
      activities.push({
        ...activity,
        files: filesRes.rows
      });
    }
    
    res.json({
      activities,
      user_role: userRank,
      total_count: activities.length
    });
    
  } catch (error) {
    console.error('List activities error:', error);
    res.status(500).json({ error: 'Failed to fetch activities' });
  }
});

// Update an individual activity
router.put('/activities/:id', authenticateToken, requireTeamPin, async (req, res) => {
  try {
    const activityId = parseInt(req.params.id, 10);
    if (Number.isNaN(activityId)) return res.status(400).json({ error: 'Invalid activity id' });

    const {
      owner_name, title, short_description, activity_date, state,
      activity_idea, activity_goals, target_groups, audience_count,
      activity_location, activity_time, activity_duration, activity_budget,
      budget_details, total_budget, activity_levels
    } = req.body;

    if (!owner_name || !title || !activity_date || !state) {
      return res.status(400).json({ error: 'owner_name, title, activity_date, and state are required' });
    }

    // Get current user info for role-based permission check
    const userRes = await pool.query(
      'SELECT id, rank, governorate, avatar_url FROM ndt_users WHERE username = $1',
      [req.user.username]
    );
    
    if (!userRes.rows.length) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    const user = userRes.rows[0];
    let userRank = user.rank;
    
    // Check for intended rank stored in avatar_url field (for leaders)
    if (user.avatar_url && user.avatar_url.startsWith('rank:')) {
      userRank = user.avatar_url.replace('rank:', '');
    }
    
    // Get activity details with submission info for permission check
    const activityCheckSql = `
      SELECT a.id, s.user_id, s.governorate, u.username as submitter_username
      FROM ndt_activities a
      JOIN ndt_activity_submissions s ON a.submission_id = s.id
      JOIN ndt_users u ON s.user_id = u.id
      WHERE a.id = $1
    `;
    const activityRes = await pool.query(activityCheckSql, [activityId]);
    
    if (!activityRes.rows.length) {
      return res.status(404).json({ error: 'Activity not found' });
    }
    
    const activity = activityRes.rows[0];
    
    // Role-based permission check
    let hasPermission = false;
    
    if (userRank === 'admin') {
      // Admin can edit all activities
      hasPermission = true;
    } else if (userRank === 'leader') {
      // Leader can edit activities from their governorate
      hasPermission = (activity.governorate === user.governorate);
    } else {
      // Regular user can only edit their own activities
      hasPermission = (activity.user_id === user.id);
    }
    
    if (!hasPermission) {
      return res.status(403).json({ error: 'Access denied: insufficient permissions to edit this activity' });
    }

    // Update the activity
    const updateSql = `
      UPDATE ndt_activities
      SET owner_name = $1, title = $2, short_description = $3, activity_date = $4, state = $5,
          activity_idea = $6, activity_goals = $7, target_groups = $8, audience_count = $9,
          activity_location = $10, activity_time = $11, activity_duration = $12, activity_budget = $13,
          budget_details = $14, total_budget = $15, activity_levels = $16
      WHERE id = $17
      RETURNING *
    `;
    const updateRes = await pool.query(updateSql, [
      owner_name, title, short_description, activity_date, state,
      activity_idea || null,
      activity_goals ? JSON.stringify(activity_goals) : null,
      target_groups ? JSON.stringify(target_groups) : null,
      audience_count,
      activity_location || null,
      activity_time,
      activity_duration || null,
      activity_budget,
      budget_details ? JSON.stringify(budget_details) : null,
      total_budget,
      activity_levels ? JSON.stringify(activity_levels) : null,
      activityId
    ]);
    
    // Get associated files
    const filesRes = await pool.query(
      'SELECT id, file_name, file_url, mime_type, size_bytes, uploaded_at FROM ndt_activity_files WHERE activity_id = $1 ORDER BY uploaded_at ASC',
      [activityId]
    );
    
    const updatedActivity = { ...updateRes.rows[0], files: filesRes.rows };
    res.json(updatedActivity);
  } catch (error) {
    console.error('Update activity error:', error);
    res.status(500).json({ error: 'Failed to update activity' });
  }
});

// Delete an individual activity
router.delete('/activities/:id', authenticateToken, requireTeamPin, async (req, res) => {
  try {
    const activityId = parseInt(req.params.id, 10);
    if (Number.isNaN(activityId)) return res.status(400).json({ error: 'Invalid activity id' });

    // Get current user info for role-based permission check
    const userRes = await pool.query(
      'SELECT id, rank, governorate, avatar_url FROM ndt_users WHERE username = $1',
      [req.user.username]
    );
    
    if (!userRes.rows.length) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    const user = userRes.rows[0];
    let userRank = user.rank;
    
    // Check for intended rank stored in avatar_url field (for leaders)
    if (user.avatar_url && user.avatar_url.startsWith('rank:')) {
      userRank = user.avatar_url.replace('rank:', '');
    }
    
    // Get activity details with submission info for permission check
    const activityCheckSql = `
      SELECT a.id, s.user_id, s.governorate, u.username as submitter_username
      FROM ndt_activities a
      JOIN ndt_activity_submissions s ON a.submission_id = s.id
      JOIN ndt_users u ON s.user_id = u.id
      WHERE a.id = $1
    `;
    const activityRes = await pool.query(activityCheckSql, [activityId]);
    
    if (!activityRes.rows.length) {
      return res.status(404).json({ error: 'Activity not found' });
    }
    
    const activity = activityRes.rows[0];
    
    // Role-based permission check
    let hasPermission = false;
    
    if (userRank === 'admin') {
      // Admin can delete all activities
      hasPermission = true;
    } else if (userRank === 'leader') {
      // Leader can delete activities from their governorate
      hasPermission = (activity.governorate === user.governorate);
    } else {
      // Regular user can only delete their own activities
      hasPermission = (activity.user_id === user.id);
    }
    
    if (!hasPermission) {
      return res.status(403).json({ error: 'Access denied: insufficient permissions to delete this activity' });
    }

    await pool.query('DELETE FROM ndt_activities WHERE id = $1', [activityId]);
    res.json({ success: true });
  } catch (error) {
    console.error('Delete activity error:', error);
    res.status(500).json({ error: 'Failed to delete activity' });
  }
});

// Delete a submission (and cascade activities/files)
router.delete('/submissions/:id', authenticateToken, requireTeamPin, async (req, res) => {
  try {
    const id = parseInt(req.params.id, 10);
    if (Number.isNaN(id)) return res.status(400).json({ error: 'Invalid id' });

    // Ensure submission belongs to current user
    const subRes = await pool.query('SELECT id FROM ndt_activity_submissions WHERE id = $1 AND user_id = $2', [id, req.currentUserId]);
    if (!subRes.rows.length) return res.status(404).json({ error: 'Submission not found' });

    await pool.query('DELETE FROM ndt_activity_submissions WHERE id = $1', [id]);
    res.json({ success: true });
  } catch (error) {
    console.error('Delete submission error:', error);
    res.status(500).json({ error: 'Failed to delete submission' });
  }
});

// Allowed states endpoint (optional helper for frontend)
router.get('/states', (req, res) => {
  res.json(ALLOWED_STATES);
});

// Configure multer for disk storage with chunked streaming
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + crypto.randomBytes(6).toString('hex');
    const ext = path.extname(file.originalname);
    const newFilename = 'file-' + uniqueSuffix + ext;
    cb(null, newFilename);
  }
});

// File filter for validation
const fileFilter = (req, file, cb) => {
  if (isValidFileType(file.originalname, file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only PDF, DOC, DOCX, JPG, and PNG files are allowed.'), false);
  }
};

// Configure multer with disk storage and limits
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: 1
  }
});

// Streaming file upload endpoint (single file) using Multer
router.post('/upload-file', authenticateToken, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { originalname, filename, mimetype, size, path: filePath } = req.file;

    try {
      // Store file metadata in database
      const result = await pool.query(
        `INSERT INTO ndt_activity_files (file_name, file_url, mime_type, size_bytes, uploaded_by, uploaded_at) 
         VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
        [originalname, `/uploads/activity-files/${filename}`, mimetype, size, req.user.username, new Date()]
      );

      const uploadedFile = {
        id: result.rows[0].id,
        originalName: originalname,
        filename: filename,
        url: `/uploads/activity-files/${filename}`,
        size: size,
        mimeType: mimetype
      };

      res.json({ 
        message: 'File uploaded successfully',
        file: uploadedFile 
      });
    } catch (dbError) {
      console.error('Database error:', dbError);
      // Clean up uploaded file if database operation fails
      fs.unlink(filePath, () => {});
      res.status(500).json({ error: 'Failed to save file metadata' });
    }
  } catch (error) {
    console.error('File upload error:', error);
    res.status(500).json({ error: 'Failed to upload file' });
  }
});

// Error handling middleware for multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File size exceeds 10MB limit' });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({ error: 'Only one file is allowed per upload' });
    }
    return res.status(400).json({ error: error.message });
  }
  
  if (error.message.includes('Invalid file type')) {
    return res.status(400).json({ error: error.message });
  }
  
  next(error);
});

// Get coordinator name for user's governorate
router.get('/coordinator', authenticateToken, async (req, res) => {
  try {
    // Get user's governorate from their profile
    const userRes = await pool.query('SELECT governorate FROM ndt_users WHERE username = $1', [req.user.username]);
    if (userRes.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    const userGovernorate = userRes.rows[0].governorate;
    if (!userGovernorate) {
      return res.status(404).json({ error: 'User governorate not set' });
    }
    
    // Find users with leader rank from the same governorate
    // Check both actual rank and intended rank stored in avatar_url
    const coordinatorRes = await pool.query(
      `SELECT full_name FROM ndt_users 
       WHERE governorate = $1 
       AND (rank = 'leader' OR avatar_url = 'rank:leader') 
       AND full_name IS NOT NULL 
       ORDER BY created_at ASC 
       LIMIT 1`,
      [userGovernorate]
    );
    
    let coordinatorName;
    if (coordinatorRes.rows.length > 0) {
      coordinatorName = coordinatorRes.rows[0].full_name;
    } else {
      // Fallback to placeholder if no leader found
      coordinatorName = `منسق ${userGovernorate}`;
    }
    
    res.json({ coordinator_name: coordinatorName });
  } catch (error) {
    console.error('Get coordinator error:', error);
    res.status(500).json({ error: 'Failed to fetch coordinator' });
  }
});

// --- Admin Endpoints ---
// Middleware to check if user is admin
async function requireAdmin(req, res, next) {
  try {
    const { rows } = await pool.query('SELECT rank FROM ndt_users WHERE username = $1', [req.user.username]);
    if (!rows.length || rows[0].rank !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    next();
  } catch (e) {
    console.error('requireAdmin error:', e);
    res.status(500).json({ error: 'Server error' });
  }
}

// Get all users (admin only)
router.get('/admin/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { rows } = await pool.query(
      'SELECT id, username, full_name, rank, governorate, created_at, last_login, avatar_url FROM ndt_users ORDER BY created_at DESC'
    );
    
    // Check for intended rank stored in avatar_url field (temporary solution)
    const usersWithIntendedRank = rows.map(user => {
      let intendedRank = user.rank;
      if (user.avatar_url && user.avatar_url.startsWith('rank:')) {
        intendedRank = user.avatar_url.replace('rank:', '');
      }
      return {
        ...user,
        rank: intendedRank,
        avatar_url: user.avatar_url && user.avatar_url.startsWith('rank:') ? null : user.avatar_url
      };
    });
    
    res.json(usersWithIntendedRank);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Update user rank and governorate (admin only)
router.put('/admin/users/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id, 10);
    if (Number.isNaN(userId)) return res.status(400).json({ error: 'Invalid user id' });
    
    const { rank, governorate } = req.body;
    
    // Validate rank
    const allowedRanks = ['member', 'leader', 'admin'];
    if (!allowedRanks.includes(rank)) {
      return res.status(400).json({ error: 'Invalid rank. Must be: member, leader or admin' });
    }
    
    // Map 'leader' to 'member' for database storage (due to constraint)
    const dbRank = rank === 'leader' ? 'member' : rank;
    
    // Store intended rank in avatar_url field if it's 'leader'
    const avatarValue = rank === 'leader' ? 'rank:leader' : null;
    
    // Validate governorate (Iraqi governorates in Arabic)
    const iraqGovernorates = [
      'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى',
      'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين',
      'ميسان', 'دهوك', 'السليمانية', 'حلبجة'
    ];
    
    if (!iraqGovernorates.includes(governorate)) {
      return res.status(400).json({ error: 'Invalid governorate' });
    }
    
    const { rows } = await pool.query(
      'UPDATE ndt_users SET rank = $1, governorate = $2, avatar_url = $3 WHERE id = $4 RETURNING id, username, full_name, rank, governorate, avatar_url',
      [dbRank, governorate, avatarValue, userId]
    );
    
    // Return the original rank for frontend display
    if (rows.length > 0) {
      rows[0].rank = rank;
      // Clean up avatar_url for response if it contains rank info
      if (rows[0].avatar_url && rows[0].avatar_url.startsWith('rank:')) {
        rows[0].avatar_url = null;
      }
    }
    
    if (!rows.length) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json(rows[0]);
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Get available ranks and governorates (admin only)
router.get('/admin/options', authenticateToken, requireAdmin, (req, res) => {
  const ranks = ['member', 'leader', 'admin'];
  const governorates = [
    'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 'الأنبار', 'ديالى',
    'ذي قار', 'المثنى', 'القادسية', 'بابل', 'كركوك', 'واسط', 'صلاح الدين',
    'ميسان', 'دهوك', 'السليمانية', 'حلبجة'
  ];
  
  res.json({ ranks, governorates });
});

// --- Serve built SPA (Vue) for NDYT Activities ---
// Serve uploaded files statically
router.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// If the Vue app is built, serve its static assets and index.html from here
const distDir = path.resolve(__dirname, '../ndyt-activities/dist');
const hasDist = fs.existsSync(path.join(distDir, 'index.html'));
if (hasDist) {
  // Serve static assets like /assets/*
  router.use(express.static(distDir));
}

function serveSpaIndex(req, res) {
  if (hasDist) {
    return res.sendFile(path.join(distDir, 'index.html'));
  }
  // Fallback to legacy static page if build not available
  const legacyIndex = path.resolve(__dirname, '../public/ndt_index.html');
  return res.sendFile(legacyIndex);
}

// Catch-all for client-side routes (history mode)
// Exclude API endpoints that start with /auth, /submissions, /states, /activities, /coordinator, or /admin
router.get(/^\/(?!auth\/|submissions\b|states\b|activities\b|coordinator\b|admin\b).*/, (req, res) => {
  serveSpaIndex(req, res);
});

module.exports = router;