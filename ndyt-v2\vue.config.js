const { defineConfig } = require('@vue/cli-service')
const timestamp = Date.now()
const randomId = Math.random().toString(36).substr(2, 9)

module.exports = defineConfig({
  publicPath: '/ndyt/',
  chainWebpack: config => {
    config
      .plugin('html')
      .tap(args => {
        args[0].title = 'الفريق الوطني للشباب الرقمي';
        return args;
      });
  },
  devServer: {
    port: 8080,
    hot: true,
    liveReload: true,
    watchFiles: {
      paths: ['src/**/*', 'public/**/*'],
      options: {
        usePolling: true,
      },
    },
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    },
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate, proxy-revalidate, max-age=0',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Last-Modified': new Date().toUTCString(),
      'ETag': `"${timestamp}-${randomId}"`,
      'Vary': '*'
    },
    setupMiddlewares: (middlewares, devServer) => {
      // Add specific cache-busting for login route
      devServer.app.use('/ndyt/login*', (req, res, next) => {
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.setHeader('Last-Modified', new Date().toUTCString());
        res.setHeader('ETag', Date.now().toString());
        next();
      });
      
      // Add aggressive cache-busting for CSS files
      devServer.app.use('/ndyt/css/*', (req, res, next) => {
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.setHeader('Last-Modified', new Date().toUTCString());
        res.setHeader('ETag', `css-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
        res.setHeader('X-CSS-Cache-Bust', Date.now().toString());
        res.setHeader('Vary', 'User-Agent, Accept-Encoding, Cache-Control');
        console.log('CSS middleware hit:', req.url);
        next();
      });
      
      return middlewares;
    },
    proxy: {
      '^/api/v1/ndyt-activities': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        logLevel: 'debug'
      }
    }
  },
  configureWebpack: {
    cache: false,
    optimization: {
      splitChunks: false,
      runtimeChunk: false,
      minimize: false
    },
    output: {
      filename: `[name].[contenthash].${timestamp}.${randomId}.js`,
      chunkFilename: `[name].[contenthash].${timestamp}.${randomId}.js`
    }
  },
  css: {
    extract: {
      filename: `[name].[contenthash].${timestamp}.${randomId}.${Date.now()}.css`,
      chunkFilename: `[name].[contenthash].${timestamp}.${randomId}.${Date.now()}.css`
    },
    sourceMap: false,
    loaderOptions: {
      css: {
        url: {
          filter: (url) => {
            // Add cache busting to CSS URLs
            return !url.includes('?');
          }
        }
      }
    }
  },
  filenameHashing: true,
  // Using an empty array to satisfy plugins expecting an array for transpileDependencies
  transpileDependencies: []
})
